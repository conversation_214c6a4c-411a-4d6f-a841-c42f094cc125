package org.mospital.bsoft

/**
 * @param executDept 执行科室
 * @param expenseAccount 费用数量
 * @param expenseDept 费用科室
 * @param expenseName 费用名称
 * @param expensePrice 费用单价
 * @param expenseTime 费用时间
 * @param settleTime 结账时间
 * @param totalMoney 划价金额
 */
data class ZhuyuanFee(
    val doctor: String = "",
    val executDept: String = "",
    val expenseAccount: String = "",
    val expenseDept: String = "",
    val expenseName: String = "",
    val expensePrice: String = "",
    val expenseTime: String = "",
    val settleTime: String = "",
    val totalMoney: String = "",
)