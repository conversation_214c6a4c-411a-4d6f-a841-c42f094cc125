package org.mospital.bsoft

import java.math.BigDecimal

/**
 * 门诊退款
 * @param creditAmount 退款金额
 * @param operationType 10=银医充值，20=掌医充值，30=微信公众号
 * @param orderNumber 订单号
 * @param patientCard 就诊卡号
 * @param patientId 病人ID
 * @param payType 支付方式，1=现金，23=银行卡，16=微信，14=支付宝
 */
data class MenzhenRefundForm(
    val businessSerialumber: String = "",
    val creditAmount: BigDecimal,
    val oldbOusinessSerialumber: String,
    val operationType: String = "20",
    val orderNumber: String = "",
    val patientCard: String,
    val patientId: String,
    val payType: String = "16",
    val recordNumber: String = "",
    val referenceNumber: String = "",
) : BaseForm()
