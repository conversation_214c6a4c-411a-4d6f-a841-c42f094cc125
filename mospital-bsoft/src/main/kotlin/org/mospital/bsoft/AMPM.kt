package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonFormat

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
enum class AMPM(val code: Int, val desc: String) {
    AM(1, "上午"),
    PM(2, "下午"),
    NIGHT(3, "晚上");

    companion object {
        private val mapByCode = values().associateBy { it.code }

        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun fromCode(code: Int): AMPM? = mapByCode[code]
    }

}