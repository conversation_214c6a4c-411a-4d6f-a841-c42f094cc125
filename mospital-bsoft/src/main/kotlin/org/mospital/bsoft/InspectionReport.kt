package org.mospital.bsoft

/**
 * 检验报告单
 * @property itemNo 项目序号
 * @property itemCode 项目代码
 * @property itemName 项目名称
 * @property printOrder 打印顺序
 * @property textValue 检验指标结果值
 * @property numberValue 检验指标结果数值
 * @property normalRange 检验指标正常范围
 * @property units 单位
 * @property abnormalIndicator 异常指示
 * @property alertFlag 警告标识
 * @property minValue 最小值
 * @property maxValue 最大值
 */
data class InspectionReport(
    val itemNo: String,
    val itemCode: String,
    val itemName: String,
    val printOrder: String,
    val textValue: String,
    val numberValue: String,
    val normalRange: String,
    val units: String = "",
    val abnormalIndicator: String = "",
    val alertFlag: String = "",
    val minValue: String = "",
    val maxValue: String = "",
)
