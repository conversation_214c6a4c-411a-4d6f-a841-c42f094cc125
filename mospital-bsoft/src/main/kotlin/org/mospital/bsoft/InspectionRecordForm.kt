package org.mospital.bsoft

import java.time.LocalDateTime

/**
 * 检验报告
 * @property nucleicAcidFlag 核酸报告标识：0=检验报告单，1=核酸报告单
 * @property patientId 病人ID
 * @property reportNo 报告单号
 * @property stayHospitalMode 报告类型，1=门诊，2=住院
 */
data class InspectionRecordForm(
    val beginTime: LocalDateTime? = null,
    val endTime: LocalDateTime? = null,
    val nucleicAcidFlag: String = "",
    val outpatientNumber: String = "",
    val patientId: String,
    val reportNo: String = "",
    val stayHospitalMode :String = ""
): BaseForm()
