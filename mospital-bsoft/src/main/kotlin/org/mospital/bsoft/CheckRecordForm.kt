package org.mospital.bsoft

import java.time.LocalDateTime

/**
 * 检查报告
 * @property patientId 病人ID
 * @property startDate 开始时间
 * @property endDate 结束时间
 * @property type 报告类型:1=门诊，2=住院，3=体检，4=急诊，5=绿色通道
 * @property visitNumber 就诊序号
 */
data class CheckRecordForm(
    val patientId: String,
    val endDate: LocalDateTime? = null,
    val startDate: LocalDateTime? = null,
    val type: String = "",
    val visitNumber: List<String> = emptyList()
): BaseForm()
