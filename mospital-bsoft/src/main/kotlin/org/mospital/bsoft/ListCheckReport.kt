package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class ListCheckReport(
// Basic information
    @field:JsonProperty("patientName")
    val patientName: String = "",

    @field:JsonProperty("sex")
    val sex: String = "",

    @field:JsonProperty("age")
    val age: String = "",

    // Examination details
    @field:JsonProperty("hospitalName")
    val hospitalName: String = "",

    @field:JsonProperty("hospitalCode")
    val hospitalCode: String = "",

    @field:JsonProperty("doctorName")
    val doctorName: String = "",

    @field:JsonProperty("doctorCode")
    val doctorCode: String = "",

    @field:JsonProperty("reporter")
    val reporter: String = "",

    @field:JsonProperty("reportTime")
    val reportTime: String = "",

    @field:JsonProperty("examTime")
    val examTime: String = "",

    @field:JsonProperty("examDescript")
    val examDescription: String = "",

    @field:JsonProperty("checkName")
    val checkName: String = "",

    @field:JsonProperty("checkId")
    val checkId: String = "",

    @field:JsonProperty("checkMethod")
    val checkMethod: String = "",

    @field:JsonProperty("checkTime")
    val checkTime: String = "",

    @field:JsonProperty("checkPart")
    val checkPart: String = "",

    @field:JsonProperty("executeDepartmentName")
    val executeDepartmentName: String = "",

    @field:JsonProperty("executeDepartmentCode")
    val executeDepartmentCode: String = "",

    @field:JsonAlias("reportTypeCode", "jclx")
    val reportTypeCode: String = "",

    @field:JsonAlias("reportTypeName", "jcmc")
    val reportTypeName: String = "",

    // Report contents
    @field:JsonProperty("reportContents")
    val reportContents: List<ReportContent> = emptyList(),

    // Additional information
    @field:JsonProperty("source")
    val source: String = "",

    @field:JsonProperty("url")
    val url: String = ""
) {
    data class ReportContent(
        @field:JsonProperty("itemName")
        val itemName: String = "",

        @field:JsonProperty("itemContent")
        val itemContent: String = ""
    )
}