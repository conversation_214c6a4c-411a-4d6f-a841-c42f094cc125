package org.mospital.bsoft

import kotlinx.coroutines.runBlocking
import okhttp3.OkHttpClient
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST
import java.time.LocalTime
import java.time.format.DateTimeFormatter


interface BSoftService {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(BSoftService::class.java)
        private val serviceCache = mutableMapOf<String, BSoftService>()

        private fun createService(url: String): BSoftService {
            val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = 5_000,
                readTimeout = 30_000,
                useUuidRequestId = true
            )
            val retrofit = Retrofit.Builder()
                .baseUrl(url)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(JacksonKit.buildMapper()))
                .build()
            return retrofit.create(BSoftService::class.java)
        }

        @Synchronized
        fun getService(url: String): BSoftService {
            return serviceCache.getOrPut(url) { createService(url) }
        }

        val me: BSoftService by lazy {
            getService(BSoftSetting.url)
        }

        fun getPatientInfoByJzCardNo(jzCardNo: String): Result<PatientInfo> = runBlocking {
            me.getPatientInfo(PatientForm(cardNo = jzCardNo))
        }

        fun getPatientInfoByIdCardNo(idCardNo: String): Result<PatientInfo> = runBlocking {
            me.getPatientInfo(PatientForm(patientIdCard = idCardNo))
        }

        fun getPatientInfoByPatientId(patientId: String): Result<PatientInfo> = runBlocking {
            me.getPatientInfo(PatientForm(patientId = patientId))
        }

        fun getPatientsByIdCard(idCardNo: String): Result<List<PatientInfo>> = runBlocking {
            me.getPatientsByIdCard(PatientForm(patientIdCard = idCardNo))
        }

        fun getZhuyuanPatientByAdmissionNumber(admissionNumber: String): Result<ZhuyuanPatient> = runBlocking {
            val result = me.getZhuyuanPatient(
                ZhuyuanPatientForm(
                    admissionNumber = admissionNumber
                )
            )
            return@runBlocking if (result.isOk()) {
                Result.ok(result.data!!.first())
            } else {
                Result.fail(result.code, result.message)
            }
        }

        fun getZhuyuanHistoryListByIdCardNo(idCardNo: String): Result<List<ZhuyuanHistory>> = runBlocking {
            me.getZhuyuanHistoryList(ZhuyuanHistoryForm(patientIdCard = idCardNo))
        }

        fun getZhuyuanHistoryListByNum(admissionNumber: String): Result<List<ZhuyuanHistory>> = runBlocking {
            me.getZhuyuanHistoryList(ZhuyuanHistoryForm(admissionNumber = admissionNumber))
        }

        fun getReservationDeptList(): Result<List<ReservationDept>> = runBlocking {
            me.getReservationDeptList(ReservationDeptForm())
        }

        fun getReservationTicketList(form: ReservationTicketForm): Result<List<ReservationTicket>> = runBlocking {
            val result = me.getReservationTicketList(form)
            return@runBlocking if (result.isOk()) {
                val data = result.data!!.onEach {
                    it.period = it.period.split("-").joinToString(separator = "-") {
                        LocalTime.parse(it, DateTimeFormatter.ofPattern("H:mm"))
                            .format(DateTimeFormatter.ofPattern("HH:mm"))
                    }
                }
                Result.ok(data.sortedBy { it.period })
            } else {
                result
            }
        }

        fun getReservationList(patientId: String, operationType: String = "0"): Result<List<Reservation>> =
            runBlocking {
                val result = me.getReservationList(
                    ReservationForm(
                        patientId = patientId,
                        operationType = operationType
                    )
                )
                return@runBlocking if (result.isOk()) {
                    result
                } else {
                    if (result.message == "无预约记录！") {
                        Result.ok(data = emptyList())
                    } else {
                        result
                    }
                }
            }

        fun getNurses(): Result<List<Nurse>> = runBlocking {
            me.getNurses()
        }

        fun createAccountAndCard(form: AccountAndCardForm): Result<AccountAndCard> = runBlocking {
            me.createAccountAndCard(form)
        }

        /**
         * 查询检验报告
         */
        fun getInspectionRecords(form: InspectionRecordForm): Result<List<InspectionRecord>> = runBlocking {
            me.getInspectionRecords(form)
        }

        /**
         * 查询检验报告详情
         */
        fun getInspectionReports(form: InspectionReportForm): Result<List<InspectionReport>> = runBlocking {
            me.getInspectionReports(form)
        }

        /**
         * 住院充值
         */
        fun zhuyuanRecharge(form: ZhuyuanRechargeForm): Result<Recharge> = runBlocking {
            me.zhuyuanRecharge(form)
        }

        /**
         * 对账明细
         */
        fun getBillDetails(form: BillDetailForm): Result<List<BillDetail>> = runBlocking {
            me.getBillDetails(form)
        }


        /**
         * 检查报告
         */
        fun getListCheckReport(form: ListCheckForm): Result<List<ListCheckReport>> = runBlocking {
            me.getListCheckReport(form)
        }

        /**
         * 门诊自费结算
         */
        fun menzhenSettlement(form: MenzhenSettlementForm): Result<MenzhenSettlement> = runBlocking {
            me.menzhenSettlement(form)
        }

        /**
         * 查询门诊待缴费处方
         */
        fun getUnpaidPrescriptions(form: UnpaidPrescriptionForm): Result<List<UnpaidPrescription>> = runBlocking {
            me.getUnpaidPrescriptions(form)
        }

        /**
         * 门诊退款
         */
        fun menzhenRefund(form: MenzhenRefundForm): Result<MenzhenRefund> = runBlocking {
            me.menzhenRefund(form)
        }

        /**
         * 门诊退款
         */
        fun menzhenRefund7(form: MenzhenRefund7Form): Result<MenzhenRefund> = runBlocking {
            me.menzhenRefund7(form)
        }

    }

    /**
     * 获取患者信息
     */
    @POST("/api/outpatient/getPatientInfo")
    suspend fun getPatientInfo(@Body form: PatientForm): Result<PatientInfo>

    @POST("/api/outpatient/getPatientInfosBysfzh")
    suspend fun getPatientsByIdCard(@Body form: PatientForm): Result<List<PatientInfo>>

    /**
     * 获取门诊预交金充值退款记录
     */
    @POST("/api/payMent/queryBalanceRecord")
    suspend fun getMenzhenBillList(@Body form: MenzhenBillForm): Result<List<MenzhenBill>>

    /**
     * 查询住院历史记录
     */
    @POST("/api/inpatientArea/getInpatientHistory")
    suspend fun getZhuyuanHistoryList(@Body form: ZhuyuanHistoryForm): Result<List<ZhuyuanHistory>>

    /**
     * 查询住院病人信息
     */
    @POST("/api/inpatientArea/getInpatientInfo")
    suspend fun getZhuyuanPatient(@Body form: ZhuyuanPatientForm): Result<List<ZhuyuanPatient>>

    /**
     * 查询住院费用清单
     */
    @POST("/api/inpatientArea/getInpatientFeeList")
    suspend fun getZhuyuanFeeList(@Body form: ZhuyuanFeeForm): Result<List<ZhuyuanFee>>

    /**
     * 住院充值
     */
    @POST("/api/inpatientArea/inpatientAddDeposit")
    suspend fun zhuyuanRecharge(@Body form: ZhuyuanRechargeForm): Result<Recharge>

    /**
     * 查询住院充值退款记录
     */
    @POST("/api/inpatientArea/getRechargeRecord")
    suspend fun getZhuyuanBillList(@Body form: ZhuyuanBillForm): Result<List<ZhuyuanBill>>

    /**
     * 一键建档发卡
     */
    @POST("/api/outpatient/createAccountAndCard")
    suspend fun createAccountAndCard(@Body form: AccountAndCardForm): Result<AccountAndCard>

    /**
     * 电子健康卡绑定
     */
    @POST("/api/outpatient/createVmcardQRcode")
    suspend fun createVmcardQrCode(@Body form: VmcardQrCodeForm): Result<VmcardQrCode>

    /**
     * 门诊充值
     */
    @POST("/api/payMent/outpatientRecharge")
    suspend fun menzhenRecharge(@Body form: MenzhenRechargeForm): Result<Recharge>

    /**
     * 门诊退款
     */
    @POST("/api/payMent/outpatientRefund")
    suspend fun menzhenRefund(@Body form: MenzhenRefundForm): Result<MenzhenRefund>

    @POST("/api/payMent/outpatientRefund")
    suspend fun menzhenRefund7(@Body form: MenzhenRefund7Form): Result<MenzhenRefund>

    /**
     * 门诊自费结算
     */
    @POST("/api/payMent/paymentSettlement")
    suspend fun menzhenSettlement(@Body form: MenzhenSettlementForm): Result<MenzhenSettlement>

    /**
     * 查询排班科室
     */
    @POST("/api/reservation/getReservationDept")
    suspend fun getReservationDeptList(@Body form: ReservationDeptForm): Result<List<ReservationDept>>

    /**
     * 查询排班医生
     */
    @POST("/api/reservation/getReservationDoctor")
    suspend fun getReservationDoctorList(@Body form: ReservationDoctorForm): Result<List<ReservationDoctor>>

    /**
     * 近期可挂号医生号源查询
     */
    @POST("/api/reservation/getReservationComingDoctor")
    suspend fun getReservationComingDoctor(@Body form: ReservationComingDoctorForm): Result<List<ReservationComingDoctor>>

    /**
     * 查询排班号源
     */
    @POST("/api/reservation/getSourceList")
    suspend fun getReservationTicketList(@Body form: ReservationTicketForm): Result<List<ReservationTicket>>

    /**
     * 预约
     */
    @POST("/api/reservation/reservationRegister")
    suspend fun reserve(@Body form: ReserveForm): Result<ReserveResult>

    /**
     * 查询预约记录
     */
    @POST("/api/reservation/getReservationList")
    suspend fun getReservationList(@Body form: ReservationForm): Result<List<Reservation>>

    /**
     * 取消预约
     */
    @POST("/api/reservation/cancelReserve")
    suspend fun cancelReservation(@Body form: ReservationCancelForm): ReservationCancelResult

    /**
     * 查询材料信息
     */
    @POST("/api/hospitalInfo/queryMaterialInfo")
    suspend fun getMaterials(@Body form: MaterialForm): Result<List<Material>>

    /**
     * 查询药品信息
     */
    @POST("/api/hospitalInfo/queryDrugsInfo")
    suspend fun getDrugs(@Body form: DrugForm): Result<List<Drug>>

    /**
     * 查询出院带药
     */
    @POST("/api/inpatientArea/getDischargeMedicationInformation")
    suspend fun getDischargeInstructions(@Body form: DischargeInstructionForm): Result<DischargeInstruction>

    /**
     * 查询待缴费医技
     */
    @POST("/api/payMent/noPayMedicalTechnology")
    suspend fun getUnpaidMedicalTechnology(@Body form: UnpaidMedicalTechnologyForm): Result<List<UnpaidMedicalTechnology>>

    /**
     * 门诊待缴费处方查询
     */
    @POST("/api/payMent/noPayPrescription")
    suspend fun getUnpaidPrescriptions(@Body form: UnpaidPrescriptionForm): Result<List<UnpaidPrescription>>

    @POST("/api/payMent/getUFeeDataById")
    suspend fun getPendingSettlements(@Body form: PendingSettlementForm): Result<List<JiuzhenRecord>>

    @POST("/api/payMent/queryOutpatientFeeDetail")
    suspend fun getMenzhenFees(@Body form: MenzhenFeeForm): Result<List<MenzhenFee>>

    /**
     * 查询未缴费列表，用于诊间缴费
     */
    @POST("/api/payMent/getUnpayedList")
    suspend fun getUnpaidFees(@Body form: UnpaidFeeForm): Result<List<UnpaidFeeGroup>>

    /**
     * 查询对账明细
     */
    @POST("/api/hospitalInfo/getAccountFeeList")
    suspend fun getBillDetails(@Body form: BillDetailForm): Result<List<BillDetail>>

    /**
     * 获取护士列表
     */
    @POST("/api/hospitalInfo/getEmployeeInformation")
    suspend fun getNurses(): Result<List<Nurse>>

    /**
     * 获取检验报告
     */
    @POST("/api/check/getInspectionRecord")
    suspend fun getInspectionRecords(@Body form: InspectionRecordForm): Result<List<InspectionRecord>>

    /**
     * 获取检验报告详情
     */
    @POST("/api/check/getInspectionReport")
    suspend fun getInspectionReports(@Body form: InspectionReportForm): Result<List<InspectionReport>>

    /**
     * 获取检查报告
     */
    @POST("/api/check/getCheckRecord")
    suspend fun getCheckRecords(@Body form: CheckRecordForm): Result<List<CheckRecord>>

    /**
     * 获取检查报告详情
     */
    @POST("/api/check/getCheckReport")
    suspend fun getCheckReports(@Body form: CheckReportForm): Result<CheckReport>

    /**
     * 获取电子发票
     */
    @POST("/api/payMent/getElectronicBill")
    suspend fun getElectronicBills(@Body form: ElectronicBillForm): Result<List<ElectronicBill>>

    /**
     * 获取检查报告
     */
    @POST("/api/check/listCheckReport")
    suspend fun getListCheckReport(@Body form: ListCheckForm): Result<List<ListCheckReport>>

}
