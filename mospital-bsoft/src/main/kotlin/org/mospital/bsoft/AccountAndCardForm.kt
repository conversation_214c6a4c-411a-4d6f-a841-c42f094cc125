package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnore
import org.dromara.hutool.core.data.IdcardUtil
import org.mospital.jackson.DateTimeFormatters
import java.time.LocalDate

/**
 * @param operationType 操作来源:10=银医充值,20=掌医充值,30=微信公众号
 */
data class AccountAndCardForm(
    val patientIdCardType: String = "01",
    val patientIdCard: String = "",
    val patientName: String,
    val patientNation: String,
    val patientPhone: String,
    val cardNo: String = "",
    val careerCode: String = "",
    val debitAmount: String,
    val operationType: String = "20",
    val address: String = "",
    val bankNo: String = "",
    val batchNumber: String = "",
    val cardPass: String = "",
    val orderNumber: String = "",
    // 无身份证儿童需要填写出生日期
    @field:JsonIgnore
    val childBirthDate: LocalDate? = null,
    @field:JsonIgnore
    val patientBirthDate: LocalDate? = childBirthDate
        ?: patientIdCard.takeIf(IdcardUtil::isValidCard)
            ?.let { IdcardUtil.getBirthDate(it).toLocalDateTime().toLocalDate() },
    val patientSex: Int = if (patientIdCard.isBlank()) 9 else when (IdcardUtil.getGender(patientIdCard)) {
        0 -> 2  // 女
        1 -> 1  // 男
        else -> 9   // 未知
    },
    val payType: Int = 0,
    val referenceNumber: String = "",
    val serialBatchNumber: String = "",
    val serialNumber: String = "",
    val terminalNumber: String = "",
    //1:就诊卡;2:电子健康卡（虚拟卡）
    val cardType: String = "2",
    //监护人信息
    val guarderIDCard: String = "",
    val guarderName: String = "",
    val guarderPhoneNumber: String = ""
) : BaseForm() {
    @get:JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    val childBirthDay: String
        get() = this.childBirthDate?.format(DateTimeFormatters.NORM_DATE_FORMATTER).orEmpty()

    @get:JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    val patientBirthday: String
        get() = this.patientBirthDate?.format(DateTimeFormatters.NORM_DATE_FORMATTER).orEmpty()

}
