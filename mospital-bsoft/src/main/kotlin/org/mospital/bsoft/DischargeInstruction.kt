package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import java.math.BigDecimal

data class DischargeInstruction(
    val inHospitalRecordNumber: String,
    val inHospitalRecordCode: String,
    val patientName: String,
    val inDate: String,
    val outDate: String,
    val patientAge: String,
    val departmentName: String,
    @JsonAlias("drugs", "detailsItemsdetailsItems")
    val drugs: List<DischargeInstructionDrug>,
)

data class DischargeInstructionDrug(
    val itemCode: String,
    val itemName: String,
    val itemType: String,
    val drugDose: Int,
    val doseUnit: String,
    val frequencyName: String,
    val usageName: String,
    val itemNumber: String,
    val recipeNumber: String,
    val price: BigDecimal,
    val fee: BigDecimal,
    val unit: String,
    val specifications: String,
)
