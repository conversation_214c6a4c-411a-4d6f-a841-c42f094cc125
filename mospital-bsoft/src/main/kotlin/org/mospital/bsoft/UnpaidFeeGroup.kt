package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import java.math.BigDecimal

data class UnpaidFeeGroup(
    val patientId: String,
    val patientMedicalCardType: Int,
    val patientMedicalCardNumber: String,
    @Json<PERSON>lias("fees", "medicalInformation")
    val jiuzhenRecords: List<JiuzhenRecord>
) {

    data class JiuzhenRecord(
        val admNumber: String,
        val departmentId: String,
        val departmentName: String,
        val doctorId: String,
        val doctorName: String,
        val medicalDate: String,
        val totalFee: String,
        @JsonAlias("fees", "mergingItems")
        val fees: List<Fee>
    )

    data class Fee(
        val mergingCode: String,
        val mergingName: String,
        val mergingSubtotal: String,
        val pharmacyId: String?,
        val feeNo: String,
        val feeDate: String,
        val feeTypeCode: String,
        val feeTypeName: String,
        val required: Int,
        val boilSign: Int?,
        @JsonAlias("items", "detailsItems")
        val items: List<FeeItem>
    )

    data class FeeItem(
        val fee: BigDecimal,
        val price: BigDecimal,
        val unit: String,
        val specifications: String?,
        val itemCode: String,
        val itemName: String,
        val itemType: String,
        val itemNumber: String,
        val recipeNumber: String?,
    )
}