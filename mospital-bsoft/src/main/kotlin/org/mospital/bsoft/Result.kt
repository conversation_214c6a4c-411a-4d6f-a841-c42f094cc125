package org.mospital.bsoft

data class Result<T>(
    val data: T? = null,
    val code: Int,
    val message: String,
) {

    companion object {
        fun <T> ok(data: T? = null): Result<T> {
            return Result(data, 200, "OK")
        }

        fun <T> fail(code: Int = 900, message: String = ""): Result<T> {
            return Result(null, code, message)
        }
    }

    fun isOk(): Boolean {
        return code == 200 && data != null
    }

}