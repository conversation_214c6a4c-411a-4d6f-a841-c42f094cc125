package org.mospital.bsoft

import org.dromara.hutool.crypto.digest.DigestUtil
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import org.mospital.jackson.JacksonKit
import java.time.Instant

open class BaseForm() {

    open var hospitalId: String = BSoftSetting.hospitalId

    open var hospitalName: String = ""

    open var userId: String = BSoftSetting.userId

    open fun sign(timestamp: Long = Instant.now().epochSecond, appKey: String = BSoftSetting.appKey): String {
        val parameterMap: Map<String, String?> = JacksonKit.convertValue(this, jacksonTypeRef())
        var rawText = parameterMap.asSequence()
            .filterNot { it.value.isNullOrEmpty() }
            .sortedByDescending { it.key }
            .joinToString(separator = "&") { "${it.key}=${it.value}" }
        rawText += "&timestamp=$timestamp&appKey=$appKey"
        return DigestUtil.md5Hex(rawText).uppercase()
    }

}
