package org.mospital.bsoft

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 对账明细
 * @param accountTime 交易时间
 * @param accountType 账户类型：1=门诊，2=住院
 * @param bizType 交易类型，掌医充值、掌医退费等
 * @param visitCard 就诊卡号
 * @param patientId 患者ID
 * @param platformTransId 交易流水号
 * @param paymentMoney 交易金额
 * @param moneyType 充值标识：0=充值，1=退款
 * @param paymentType 支付方式：16=微信，14=支付宝，21=微信，15=支付宝
 */
data class BillDetail(
    val accountTime: LocalDateTime,
    val userId: String,
    val paymentType: String,
    val bizType: String,
    val accountType: String,
    val visitCard: String,
    val patientId: String,
    val platformTransId: String,
    val paymentMoney: BigDecimal,
    val moneyType: String,
) {
    val isMenzhen: Boolean
        get() = accountType == "1"

    val isZhuyuan: Boolean
        get() = accountType == "2"

    val isRecharge: Boolean
        get() = moneyType == "0"

    val isRefund: Boolean
        get() = moneyType == "1"

    val isWechat: Boolean
        get() = paymentType == "16" || paymentType == "21"

    val isAlipay: Boolean
        get() = paymentType == "14" || paymentType == "15"
}
