package org.mospital.bsoft

/**
 * 门诊费用
 * @property feeTypeName String 费用类型名称
 * @property feeName String 费用名称
 * @property feeUnit String 费用单位
 * @property feeSpecifications String 费用规格
 * @property feePrice String 费用单价
 * @property feeNumber String 费用数量
 * @property totalMoney String 总金额
 * @property billingDate String 开单时间
 * @property prescriptionDept String 开单科室
 * @property implementDept String 执行科室
 * @property invoiceNumber String 发票号
 */
data class MenzhenFee(
    val feeTypeName: String,
    val feeName: String,
    val feeUnit: String,
    val feeSpecifications: String,
    val feePrice: String,
    val feeNumber: String,
    val totalMoney: String,
    val billingDate: String,
    val prescriptionDept: String,
    val implementDept: String,
    val invoiceNumber: String
)
