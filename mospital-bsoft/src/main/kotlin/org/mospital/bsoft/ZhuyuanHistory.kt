package org.mospital.bsoft

import java.time.LocalDateTime

data class ZhuyuanHistory(
    /**
     * 住院号
     */
    val admissionNo: String = "",

    /**
     * 住院号码
     */
    val admissionNumber: String = "",

    /**
     * 入院登记日期
     */
    val admissionTime: LocalDateTime? = null,

    /**
     * 病案号码
     */
    val archivesNumber: String = "",

    /**
     * 住院余额
     */
    val balance: String = "",

    /**
     * 科室名称
     */
    val departmentName: String = "",

    /**
     * 医院ID
     */
    val hospitalId: String = "",

    /**
     * 住院医生
     */
    val inpatientDoctor: String = "",

    /**
     * 病人床号
     */
    val patientBedNumber: String = "",

    /**
     * 出生年月
     */
    val patientBirthday: String = "",

    /**
     * 病人科室
     */
    val patientDepartMent: String = "",

    /**
     * 病人ID
     */
    val patientId: String = "",

    /**
     * 身份证号码
     */
    var patientIdCard: String = "",

    /**
     * 手机号（此接口未返回）
     */
    var patientMobile: String = "",

    var patientPhone: String = "",

    /**
     * 病人姓名
     */
    var patientName: String = "",

    /**
     * 民族代码
     */
    val patientNation: String = "",

    /**
     * 病人性别
     */
    val patientSex: String = "",

    /**
     * 病人性质
     */
    val patientType: String = "",

    /**
     * 入院计费开始日期
     */
    val startTime: String = "",

    val leaveTime: LocalDateTime?,

    /**
     * 出院状态：0=在院病人，1=出院证明，2=预结出院，8=正常出院，9=终结出院，99=注销出院
     */
    val leaveStatus: Int,

    /**
     * 结算状态：0=未结算，1=已结算未退款，2=已结算已退款
     */
    val settleStatus: Int
)