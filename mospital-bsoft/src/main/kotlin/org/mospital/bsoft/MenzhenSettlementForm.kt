package org.mospital.bsoft

/**
 * 门诊自费结算
 * @param applyNos 处方医技号列表：以逗号的型式拼接
 * @param debitAmount 支付金额
 * @param hospitalId 医院代码
 * @param patientId 病人ID
 * @param payType 支付方式：12：就诊卡支付,23:银行卡,16:微信,14:支付宝
 * @param totalMoney 结算总金额
 */
data class MenzhenSettlementForm(
    val applyNos: String,
    val bankNo: String = "",
    val batchNumber: String = "",
    val debitAmount: String,
    val operator: String = "",
    val orderNumber: String = "",
    val patientId: String,
    val payType: String,
    val referenceNumber: String = "",
    val serialBatchNumber: String = "",
    val serialNumber: String = "",
    val terminalNumber: String = "",
    val totalMoney: String,
    val serviceType: String = "1",
    val serialNo: String = ""
) : BaseForm()