package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * 排班医生
 * @param doctorName 诊室名称
 * @param name 医生姓名
 * @param totalTicketCount 总号数
 * @param reservedTicketCount 已预约号数
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class ReservationDoctor(
    @JsonAlias("ampm", "dateType")
    val ampm: AMPM,
    val doctorId: String,
    val doctorName: String,
    val registerFee: String? = null,
    val registerType: RegisterType,
    val name: String = "",
    val feeNo: String = "",
    val pedRatio: String = "1",
    @JsonAlias("totalTicketCount", "hyzs")
    val totalTicketCount: Int? = null,
    @JsonAlias("reservedTicketCount", "syzs")
    val reservedTicketCount: Int? = null
) {
    /**
     * 专家简介
     */
    var expertInfo: String = ""

    /**
     * 专家擅长
     */
    var expertExcel: String = ""

    /**
     * 剩余可预约号数
     **/
    val availableTicketCount: Int?
        get() = if (totalTicketCount != null && reservedTicketCount != null) {
            (totalTicketCount - reservedTicketCount).coerceAtLeast(0)
        } else {
            null
        }
}
