package org.mospital.bsoft

import java.math.BigDecimal

/**
 * 门诊充值
 * @param debitAmount 充值金额
 * @param operationType 10=银医充值，20=掌医充值，30=微信公众号
 * @param orderNumber 订单号
 * @param patientCard 就诊卡号
 * @param patientId 病人ID
 * @param payType 支付方式，1=现金，23=银行卡，16=微信，14=支付宝
 */
data class MenzhenRechargeForm(
    val bankNo: String = "",
    val batchNumber: String = "",
    val debitAmount: BigDecimal,
    val operationType: String = "20",
    val orderNumber: String,
    val patientCard: String = "",
    val patientId: String,
    val payType: String = "16",
    val referenceNumber: String = "",
    val serialBatchNumber: String = "",
    val szmxId: String = "",
    val szmxIdFlag: String = "",
    val terminalNumber: String = "",
): BaseForm()