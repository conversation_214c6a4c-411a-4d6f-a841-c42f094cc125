package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import java.math.BigDecimal

data class Material(
    @JsonAlias("id", "projectNumber")
    val id: String,

    @<PERSON>sonAlias("name", "projectName")
    val name: String,

    val pinyinCode: String,

    @JsonAlias("typeCode", "projectType")
    val typeCode: String? = null,

    @JsonAlias("typeName", "projectConsolidation")
    val typeName: String,

    @JsonAlias("unit", "projectUnit")
    val unit: String? = null,

    @JsonAlias("price", "standarPrice")
    val price: BigDecimal,
)
