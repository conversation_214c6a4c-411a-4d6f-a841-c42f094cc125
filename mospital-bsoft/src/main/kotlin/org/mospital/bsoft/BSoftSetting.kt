package org.mospital.bsoft

import org.mospital.jackson.ConfigLoader

/**
 * BSoft 配置数据类
 */
data class BSoftConfig(
    val url: String,
    val hospitalId: String,
    val appKey: String,
    val userId: String
)

/**
 * BSoft 配置管理对象
 */
object BSoftSetting {

    private val config: BSoftConfig by lazy {
        ConfigLoader.loadConfig("bsoft.yaml", classLoaderContext = BSoftSetting::class.java)
    }

    val url: String get() = config.url
    val hospitalId: String get() = config.hospitalId
    val appKey: String get() = config.appKey
    val userId: String get() = config.userId
}