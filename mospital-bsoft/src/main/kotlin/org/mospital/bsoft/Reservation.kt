package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import java.time.LocalDateTime

/**
 * 预约挂号信息
 * 
 * @param registerFlag 挂号状态；0：预约，1：履约，2：失约，3：收费，4：退约
 * @param ampm 上午/下午标识，通过@JsonAlias支持"ampm"和"dateType"两种字段名
 * @param deptId 科室ID
 * @param deptName 科室名称
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param registerType 挂号类型
 * @param reservationNumber 预约号
 * @param reservationTime 预约时间
 * @param sourceId 号源ID
 * @param address 就诊地址
 * @param serialNumber 号源序号
 * 
 * @sample 示例数据:
 * ```json
 * {
 *   "reservationNumber": "1397020",
 *   "sourceId": "4652615",
 *   "reservationTime": "2025-07-04 15:30:30",
 *   "deptId": "889",
 *   "deptName": "骨科普通门诊",
 *   "dateType": "2",
 *   "registerType": "3",
 *   "startTime": "15:00",
 *   "endTime": "16:00",
 *   "registerFlag": "2",
 *   "address": "急门诊病房综合楼三楼诊区",
 *   "hyxh": "9"
 * }
 * ```
 */
data class Reservation(
    @JsonAlias("ampm", "dateType")
    val ampm: AMPM,
    val deptId: String?,
    val deptName: String?,
    val startTime: String,
    val endTime: String,
    val registerFlag: String,
    val registerType: RegisterType,
    val reservationNumber: String,
    val reservationTime: LocalDateTime,
    val sourceId: String,
    val address: String?,
    @JsonAlias("serialNumber", "hyxh")
    val serialNumber: String?
) {
    val status: String
        get() = when (registerFlag) {
            "0" -> "预约"
            "1" -> "履约"
            "2" -> "失约"
            "3" -> "收费"
            "4" -> "退约"
            else -> "未知"
        }
}
