package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import java.time.LocalDate

data class ReservationTicket(
    @JsonAlias("amount", "sourceAccount")
    val amount: Int,
    @JsonAlias("date", "sourceDate")
    val date: LocalDate,
    @JsonAlias("period", "timeInterval")
    var period: String,
    @JsonAlias("genderFlag", "syxb")
    val genderFlag: Int? = null,
    @JsonAlias("ageFlag", "crbyxyy")
    val ageFlag: Int? = null,
) {
    // 是否仅限男性
    val isMaleOnly: Boolean
        get() = genderFlag == 1

    // 是否仅限女性
    val isFemaleOnly: Boolean
        get() = genderFlag == 2

    // 是否仅限儿童
    val isChildOnly: Boolean
        get() = ageFlag == 1

}