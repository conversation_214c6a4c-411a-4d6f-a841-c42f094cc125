package org.mospital.bsoft

import java.time.LocalDateTime

/**
 * 门诊待缴费处方
 */
data class UnpaidPrescription(
    val costDate: LocalDateTime?,
    val costName: String,
    val costNumber: String,
    val costPrice: String,
    val costSpecifications: String,
    val costType: String,
    val costUnit: String,
    val depart: String,
    val doctor: String,
    val itemsType: String,
    val patientId: String,
    val patientName: String,
    val payState: String,
    val prescriptionCode: String,
    val projectConsolidation: String,
    val projectMoney: String
)