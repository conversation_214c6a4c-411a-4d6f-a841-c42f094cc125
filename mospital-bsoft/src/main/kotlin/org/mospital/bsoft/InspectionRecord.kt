package org.mospital.bsoft

/**
 * 检验报告
 * @property sampleNo 样本号
 * @property patientName 病人姓名
 * @property patientId 病人ID
 * @property examinaim 检验目的
 * @property executeTime 执行时间
 * @property resultstatus 结果状态：0：无数据，1：有数据，4：已审核，5：已打印
 * @property requestTime 申请时间
 */
data class InspectionRecord(
    val sampleNo: String,
    val patientName: String,
    val patientId: String,
    val examinaim: String,
    val executeTime: String,
    val resultstatus: String,
    val requestTime: String,
) {
    val isAudited: Boolean
        get() = resultstatus == "4" || resultstatus == "5"
}
