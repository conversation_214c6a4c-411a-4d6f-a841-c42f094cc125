package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias

data class ReserveResult(
    @JsonAlias("ampm", "dateType")
    val ampm: AMPM,
    val deptAddress: String,
    val deptName: String,
    val patientId: String,
    val patientName: String,
    val registerType: RegisterType,
    val reservationNumber: String,
    val reservationTime: String,
    val visitNumber: String,
) {
    var alipayEnergy: Long = 0L
}
