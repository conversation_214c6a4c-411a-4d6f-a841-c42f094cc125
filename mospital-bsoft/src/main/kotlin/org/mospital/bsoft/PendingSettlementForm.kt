package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDate

data class PendingSettlementForm(
    @JsonProperty("brid")
    val patientId: String,

    @JsonProperty("brno")
    val patientNo: String = "",

    @JsonProperty("idNo")
    val idCardNo: String = "",

    val name: String = "",

    val startDate: LocalDate? = null,

    val endDate: LocalDate? = null,
)
