package org.mospital.bsoft

import java.time.LocalDateTime

/**
 * @param operationType 1=充值，2=退还，空值=全部
 */
data class MenzhenBillForm(
    val patientId: String,
    val operationType: String = OPERATION_TYPE_ALL,
    val startTime: LocalDateTime? = null,
    val endTime: LocalDateTime? = null
): BaseForm() {
    companion object {
        const val OPERATION_TYPE_RECHARGE = "1"
        const val OPERATION_TYPE_REFUND = "2"
        const val OPERATION_TYPE_ALL = ""
    }
}