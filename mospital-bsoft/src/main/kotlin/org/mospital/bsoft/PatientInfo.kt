package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * 患者信息
 * @param patientSex 性别，1=男
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class PatientInfo(
    val patientId: String,
    val patientIdCard: String = "",
    val cardNo: String = "",
    val empi: String = "",
    @JsonAlias("erhcCardNo", "erhccardno")
    val erhcCardNo: String = "",
    val patientName: String,
    val cardBalance: String,
    val outpatientNumber: String,
    val patientBirthday: String?,
    val patientNation: String?,
    val patientPhone: String?,
    val patientSex: String?,
    val patientType: String?,
)