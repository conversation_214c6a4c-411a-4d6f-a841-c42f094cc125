package org.mospital.bsoft

/**
 * 检查报告
 * @property examRequestID 检查申请ID
 * @property patientSource 病人来源:1=门诊，2=住院，3=体检，4=急诊，5=绿色通道
 * @property patientNumber 病人号
 * @property patientName 病人姓名
 * @property patientCard 病人卡号
 * @property sex 性别
 * @property age 年龄
 * @property bedNum 床号
 * @property reportState 报告状态
 * @property reportId 报告ID
 * @property itemName 检查项目
 * @property parts 部位
 * @property examDate 检查日期
 * @property applyer 申请人
 * @property applyDept 申请科室
 * @property applyTime 申请时间
 * @property inspectionTime 检查时间
 * @property auditTime 审核时间
 */
data class CheckRecord(
    val examRequestID: String,
    val patientSource: String,
    val patientNumber: String,
    val patientName: String,
    val patientCard: String,
    val sex: String,
    val age: String,
    val bedNum: String,
    val reportState: String,
    val reportId: String,
    val itemName: String,
    val parts: String,
    val examDate: String,
    val applyer: String,
    val applyDept: String,
    val applyTime: String,
    val inspectionTime: String,
    val auditTime: String
)
