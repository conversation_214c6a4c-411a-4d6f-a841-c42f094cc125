package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias

data class ZhuyuanBill(
    /**
     * 住院号
     */
    val admissionNo: String = "",

    /**
     * 住院号码
     */
    val admissionNumber: String = "",

    /**
     * 支票号码
     */
    val checkNumber: String = "",

    val hospitalId: String = "",

    /**
     * 住院类别
     */
    val inpatientType: String = "",

    /**
     * 作废判别: 0:正常，1：作废
     */
    @JsonAlias("invalidStatus", "invalidStatu")
    val invalidStatus: String = "",

    /**
     * 操作病区
     */
    val operationArea: String = "",

    /**
     * 操作类型
     */
    val operationType: String = "",

    /**
     * 订单号
     */
    val orderNo: String = "",

    /**
     * 病人床号
     */
    val patientBedNumber: String = "",

    /**
     * 病人科室
     */
    val patientDepartMent: String = "",

    /**
     * 病人病区
     */
    val patientInpatientArea: String = "",

    /**
     * 病人姓名
     */
    val patientName: String = "",

    /**
     * 缴款金额
     */
    val paymentMoney: String = "",

    /**
     * 缴款号码
     */
    val paymentNo: String = "",

    /**
     * 缴款时间
     */
    val paymentTime: String = "",

    /**
     * 缴款方式：6：现金；8：银行卡；21：微信；15：支付宝,18:医保支付
     */
    val paymentType: String = "",

    /**
     * 收据号码
     */
    val receiptNumber: String = "",

    /**
     * 退费关联订单号
     */
    val refundcorrelationNo: String = "",

    /**
     * 交易流水号
     */
    val serialNo: String = "",

    /**
     * 结账日期
     */
    val settleTime: String = "",

    /**
     * 结算次数
     */
    val settleTimes: String = "",

    /**
     * 汇总日期
     */
    val summaryDate: String = "",

    /**
     * 转存判别
     */
    @JsonAlias("transferStatus", "transferStatu")
    val transferStatus : String = "",

    /**
     * 操作工号
     */
    val userId: String = "",
)