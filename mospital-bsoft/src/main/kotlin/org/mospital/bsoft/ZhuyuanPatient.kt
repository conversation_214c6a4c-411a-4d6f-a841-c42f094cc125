package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.LocalDateTime

/**
 * 住院患者
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class ZhuyuanPatient(
    /**
     * 住院号
     */
    val admissionNo: String = "",

    /**
     * 住院号码
     */
    val admissionNumber: String = "",

    /**
     * 入院登记日期
     */
    val admissionTime: LocalDateTime? = null,

    /**
     * 病案号码
     */
    val archivesNumber: String = "",

    /**
     * 住院余额
     */
    val balance: String = "",

    /**
     * 科室名称
     */
    val departmentName: String = "",

    /**
     * 未结金额
     */
    val depoAmount: String = "",

    /**
     * 医院ID
     */
    val hospitalId: String = "",

    /**
     * 住院医生
     */
    val inpatientDoctor: String = "",

    /**
     * 病人床号
     */
    val patientBedNumber: String = "",

    /**
     * 出生年月
     */
    val patientBirthday: String = "",

    /**
     * 病人科室
     */
    val patientDepartMent: String = "",

    /**
     * 病人ID
     */
    val patientId: String = "",

    /**
     * 身份证号码
     */
    val patientIdCard: String = "",

    /**
     * 病人姓名
     */
    val patientName: String = "",

    /**
     * 民族代码
     */
    val patientNation: String = "",

    /**
     * 联系电话
     */
    val patientPhone: String = "",

    /**
     * 病人性别: 1=男, 2=女
     */
    val patientSex: String = "",

    /**
     * 病人性质
     */
    val patientType: String = "",

    /**
     * 入院计费开始日期
     */
    val startTime: String = "",

    /**
     * 未结金额
     */
    val totalFee: String = "",

    /**
     * 出院时间
     */
    val leaveTime: LocalDateTime? = null,

    /**
     * 出院状态：0=在院病人，1=出院证明，2=预结出院，8=正常出院，9=终结出院，99=注销出院
     * 只有状态为0的病人才能充值
     */
    val leaveStatus: Int? = null,
)