package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.math.BigDecimal
import java.time.LocalDate

/**
 * 近期可挂号医生号源查询
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class ReservationComingDoctor(
    val reserveTime: LocalDate,
    val sourceType: String?,
    val registerFee: BigDecimal,
    val registerType: RegisterType,
    val sourceAccount: Int?,
    val deptCode: String,
    val deptName: String,
    val dateType: AMPM,
    val pedRatio: BigDecimal?
)