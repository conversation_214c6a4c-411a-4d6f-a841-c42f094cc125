package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDate

/**
 * @param operationType 操作类型；0：预约，1：现场挂号
 * @param registerType 挂号类别：1=普通门诊，2=急诊门诊，3=专家门诊，4=专科门诊
 */
data class ReserveForm(
    val deptId: String,
    val doctorId: String,
    @JsonProperty("opetationType")
    val operationType: Int = 0,
    val patientId: String,
    val registerType: Int,
    @JsonProperty("sourceDate")
    val date: LocalDate,
    @JsonProperty("timeInterval")
    val period: String,
    val feeNo: String = "",
    val registerFee: String = ""
) : BaseForm()