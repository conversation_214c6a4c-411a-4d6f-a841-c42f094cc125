package org.mospital.bsoft

import com.fasterxml.jackson.annotation.JsonAlias
import java.math.BigDecimal

data class Drug(
    @JsonAlias("id", "drugNumber")
    val id: String,

    @<PERSON>sonAlias("name", "drugName")
    val name: String,

    @JsonAlias("type", "drugType")
    val type: String,

    @JsonAlias("specification", "drugSpecification")
    val specification: String?,

    @JsonAlias("unit", "drugUnit")
    val unit: String,

    @JsonAlias("price", "purchasePrice")
    val price: BigDecimal,
)
