package org.mospital.bsoft

import java.time.LocalDateTime

/**
 * 门诊待缴费处方查询
 * @param hospitalId 医院代码
 * @param patientId 病人ID
 * @param userId 操作员工号
 * @param isPayFlag 支付状态；0：未缴费；1：已缴费
 * @param startTime 查询开始时间
 * @param endTime 查询结束时间
 */
data class UnpaidPrescriptionForm(
    val patientId: String,
    val isPayFlag: String = "0",
    val startTime: LocalDateTime,
    val endTime: LocalDateTime,
    val operator: String = ""
) : BaseForm()