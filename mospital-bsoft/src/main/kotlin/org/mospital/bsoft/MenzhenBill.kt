package org.mospital.bsoft

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 门诊预交金充值退款记录
 * @param operationDate 交易时间
 * @param payType 支付方式，1=现金，23=银行卡，16=微信，14=支付宝
 * @param debitAmount 充值金额
 * @param creditAmount 退款金额
 * @param operationType 交易方式：掌医充值、银医充值、医技执行、医生收费...
 * @param orderNumber 订单号
 */
data class MenzhenBill(
    val operationDate: LocalDateTime,
    val payType: String = "",
    val debitAmount: String,
    val creditAmount: String,
    val operationType: String = "",
    val orderNumber: String = ""
) {
    val payTypeText: String
        get() = when (payType) {
            "1" -> "现金"
            "23" -> "银行卡"
            "16" -> "微信"
            "14" -> "支付宝"
            else -> "其它"
        }

    val amount: BigDecimal
        get() = if (creditAmount.toBigDecimal() > BigDecimal.ZERO) {
            creditAmount.toBigDecimal().negate()
        } else {
            debitAmount.toBigDecimal()
        }

    fun isRecharge(): Boolean {
        return amount > BigDecimal.ZERO
    }

    fun isRefund(): Boolean {
        return amount < BigDecimal.ZERO
    }
}

enum class ClientType {
    WXXCX,
    ZFBXCX,
    ZZJ
}

data class MenzhenBillForRefund(
    val orderId: String,
    val orderTime: String,
    val clientType: ClientType,
    val amount: BigDecimal,
    var exrefund: BigDecimal = BigDecimal.ZERO,
) {
    val unrefund: BigDecimal
        get() = amount - exrefund

    fun increExrefund(increment: BigDecimal) {
        exrefund += increment
    }

}
