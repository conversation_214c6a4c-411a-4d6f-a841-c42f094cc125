package org.mospital.bsoft

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mospital.jackson.JacksonKit

class ReservationDoctorTest {

    @Test
    fun testJsonDeserialize() {
        val json = """
            {
                "doctorId": "753",
                "doctorName": "小儿鼾症筛查及变应性鼻炎诊治专病",
                "dateType": "1",
                "registerType": "4",
                "registerFee": "6",
                "feeNo": "20120",
                "pedRatio": "1.3",
                "expertInfo": "陈玉莉，耳鼻喉科主治医师，毕业于河南大学医学院，新疆医学会会员，新疆耳鼻喉头颈外科学会会员，新疆小儿耳鼻喉头颈外科学会会员，从事耳鼻喉专业三十年。擅长:耳鼻喉常见病，疑难病的诊治，如小儿鼾症，分泌性中耳炎，鼻炎，过敏性鼻炎，腺样体肥大，扁桃体肥大，咽炎等。",
                "hyzs": "30",
                "syzs": "30",
                "expertExcel": "小儿扁桃体炎、扁桃体肥大、腺样体肥大、变应性鼻炎、鼻窦炎、分泌性中耳炎等耳鼻喉科常见病及疑难病的诊治"
            }
        """.trimIndent()
        val doctor = JacksonKit.readValue(json, ReservationDoctor::class.java)
        assertNotNull(doctor)
        assertEquals("753", doctor.doctorId)
        assertEquals("小儿鼾症筛查及变应性鼻炎诊治专病", doctor.doctorName)
        assertEquals(AMPM.AM, doctor.ampm)
        assertEquals(RegisterType.SPECIALIST, doctor.registerType)
        assertEquals("6", doctor.registerFee)
        assertEquals("20120", doctor.feeNo)
        assertEquals("1.3", doctor.pedRatio)
        assertEquals("陈玉莉，耳鼻喉科主治医师，毕业于河南大学医学院，新疆医学会会员，新疆耳鼻喉头颈外科学会会员，新疆小儿耳鼻喉头颈外科学会会员，从事耳鼻喉专业三十年。擅长:耳鼻喉常见病，疑难病的诊治，如小儿鼾症，分泌性中耳炎，鼻炎，过敏性鼻炎，腺样体肥大，扁桃体肥大，咽炎等。", doctor.expertInfo)
        assertEquals("小儿扁桃体炎、扁桃体肥大、腺样体肥大、变应性鼻炎、鼻窦炎、分泌性中耳炎等耳鼻喉科常见病及疑难病的诊治", doctor.expertExcel)
        assertEquals(30, doctor.totalTicketCount)
        assertEquals(30, doctor.reservedTicketCount)
        assertEquals(0, doctor.availableTicketCount)
    }

}