package org.mospital.bsoft

import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mospital.jackson.JacksonKit
import java.time.LocalDate
import java.math.BigDecimal

class ReservationComingDoctorTest {

    @Test
    fun testJsonDeserialize() {
        val json = """
        {
            "reserveTime": "2025-08-14",
            "registerType": "3",
            "registerFee": "18",
            "sourceAccount": "26",
            "deptCode": "101",
            "deptName": "艾力克木·阿不都玩克",
            "pedRatio": "1.3",
            "dateType": "1"
        }
        """.trimIndent()
        val doctor = JacksonKit.readValue(json, ReservationComingDoctor::class.java)
        assertNotNull(doctor)
        assertAll("ReservationComingDoctor properties",
            { assertEquals(LocalDate.of(2025, 8, 14), doctor.reserveTime) },
            { assertEquals(RegisterType.EXPERT, doctor.registerType) },
            { assertEquals(BigDecimal("18"), doctor.registerFee) },
            { assertEquals(26, doctor.sourceAccount) },
            { assertEquals("101", doctor.deptCode) },
            { assertEquals("艾力克木·阿不都玩克", doctor.deptName) },
            { assertEquals(AMPM.AM, doctor.dateType) },
            { assertEquals(BigDecimal("1.3"), doctor.pedRatio) }
        )
    }

}