package org.mospital.bsoft

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mospital.jackson.DateTimeFormatters
import org.mospital.jackson.JacksonKit
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertTrue


class BSoftServiceTest {

    @Test
    fun testGetPatientInfoByErhcCardNo() {
        val result = runBlocking {
            BSoftService.me.getPatientInfo(
                PatientForm(
                    cardNo = "6568F49C31EF30894A7267C8F3EC4048C5A3506760C5F1773708E944C282BC04",
                    cardType = "2",
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetPatientInfoByJzCardNo() {
        val result = runBlocking {
            BSoftService.me.getPatientInfo(
                PatientForm(
                    cardNo = "00723347",
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetPatientInfoByIdCardNo() {
        val result = runBlocking {
            BSoftService.me.getPatientInfo(
                PatientForm(
                    cardNo = "",
                    cardType = "0",
                    patientId = "",
                    patientIdCard = "******************",
                    patientName = "",
                    patientType = ""
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetPatientsByIdCard() {
        val result = BSoftService.getPatientsByIdCard("******************")
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetPatientInfoByPatientId() {
        val result = runBlocking {
            BSoftService.me.getPatientInfo(
                PatientForm(
                    patientId = "1039803"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetMenzhenBillList() {
        val result = runBlocking {
            BSoftService.me.getMenzhenBillList(
                MenzhenBillForm(
                    patientId = "1883811",
                    startTime = LocalDateTime.parse("2023-07-01T00:00:00"),
                    endTime = LocalDateTime.parse("2023-08-01T00:00:00"),
                )
            )
        }

        val billsForRefund = result.data.orEmpty()
            .filter { it.operationType == "银医充值" && it.isRecharge() }
            .map {
                MenzhenBillForRefund(
                    orderId = it.orderNumber,
                    orderTime = it.operationDate.format(DateTimeFormatters.NORM_DATETIME_FORMATTER),
                    amount = it.amount,
                    clientType = ClientType.ZZJ,
                ).also { ctx ->
                    result.data.orEmpty()
                        .onEach { bill ->
                            if (bill.orderNumber.startsWith(ctx.orderId) && bill.isRefund()) {
                                ctx.increExrefund(bill.amount.abs())
                            }
                        }
                }
            }
            .filter { it.unrefund > BigDecimal.ZERO }
        println(JacksonKit.writeValueAsString(billsForRefund))

//        val billMap = mutableMapOf<String, MenzhenBillForRefund>()
//        result.data.orEmpty().forEach {
//            if (billMap.containsKey(it.orderNumber)) {
//                val bill = billMap[it.billNo]!!
//                bill.amount = bill.amount + it.amount
//                billMap[it.billNo] = bill
//            } else {
//                billMap[it.billNo] = it
//            }
//        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetZhuyuanHistory() {
        val result = BSoftService.getZhuyuanHistoryListByIdCardNo("65010320180726061X")
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetZhuyuanHistoryByNum() {
        val result = BSoftService.getZhuyuanHistoryListByNum("185887")
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetZhuyuanPatient() {
        val result = BSoftService.getZhuyuanPatientByAdmissionNumber(admissionNumber = "203049")
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetZhuyuanFee() {
        val result = runBlocking {
            BSoftService.me.getZhuyuanFeeList(
                ZhuyuanFeeForm(
                    admissionNo = "66974",
                    startTime = LocalDateTime.parse("2023-04-17T00:00:00"),
                    endTime = LocalDateTime.parse("2023-04-30T00:00:00"),
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetZhuyuanBill() {
        val result = runBlocking {
            BSoftService.me.getZhuyuanBillList(
                ZhuyuanBillForm(
                    admissionNo = "66974",
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testCreateAccountAndCard() {
        val result = runBlocking {
            BSoftService.me.createAccountAndCard(
                AccountAndCardForm(
                    patientIdCard = "65232219870510252X",
                    patientName = "杨霞",
                    patientNation = "汉族",
                    debitAmount = "0",
                    patientPhone = "***********",
                    address = "昌吉市",
                    cardNo = "65232219870510252X"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testCreateVmcardQrCode() {
        val result = runBlocking {
            BSoftService.me.createVmcardQrCode(
                VmcardQrCodeForm(
                    erhcCardNo = "6568F49C31EF30894A7267C8F3EC4048C5A3506760C5F1773708E944C282BC04",
                    empi = "531E8A43202E0FF139A81B822B1C037BBF895632B57A2433D8AFBAE376AA3878",
                    patientId = "1963406",
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetReservationDeptList() {
        val result = BSoftService.getReservationDeptList()
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetReservationDoctorList() {
        val result = runBlocking {
            BSoftService.me.getReservationDoctorList(
                ReservationDoctorForm(
                    deptId = "118",
                    reserveTime = LocalDate.now().plusDays(2)
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetReservationTicketList() {
        val result = runBlocking {
            BSoftService.me.getReservationTicketList(
                ReservationTicketForm(
                    deptId = "168",
                    doctorId = "810",
                    registerDate = LocalDate.now().plusDays(2),
                    ampm = AMPM.PM.code
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testReserve() {
        val result = runBlocking {
            BSoftService.me.reserve(
                ReserveForm(
                    deptId = "319",
                    doctorId = "",
                    patientId = "1963406",
                    registerType = RegisterType.GENERAL.code,
                    date = LocalDate.now().plusDays(1),
                    period = "13:00-14:00"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetReservationList() {
        val result = BSoftService.getReservationList(patientId = "1963406")
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testCancelReservation() {
        val result = runBlocking {
            BSoftService.me.cancelReservation(
                ReservationCancelForm(
                    patientId = "1963406",
                    registerType = RegisterType.EXPERT.code,
                    reservationNumber = "158838"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testMenzhenRefund() {
        val result = runBlocking {
            BSoftService.me.menzhenRefund(
                MenzhenRefundForm(
                    patientId = "1883811",
                    patientCard = "65000001000000009913140603",
                    creditAmount = 3.00.toBigDecimal(),
                    oldbOusinessSerialumber = "105000080626868267430725194553",
                    orderNumber = "105000080626868267430725194553R3",
                    operationType = "10",
                    payType = "16",
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetMaterials() {
        val result = runBlocking {
            BSoftService.me.getMaterials(MaterialForm())
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetDrugs() {
        val result = runBlocking {
            BSoftService.me.getDrugs(DrugForm())
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetDischargeInstructions() {
        val result = runBlocking {
            BSoftService.me.getDischargeInstructions(
                DischargeInstructionForm(
                    patientId = "833582",
                    admissionNo = "44713"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetBillDetails() {
        val result = runBlocking {
            BSoftService.me.getBillDetails(
                BillDetailForm(
                    startDate = LocalDate.now().minusDays(10),
                    endDate = LocalDate.now().plusDays(1),
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetMenzhenFees() {
        val result = runBlocking {
            BSoftService.me.getMenzhenFees(
                MenzhenFeeForm(
                    patientId = "939733",
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetInspectionRecords() {
        val result = runBlocking {
            BSoftService.me.getInspectionRecords(
                InspectionRecordForm(
                    patientId = "648403", stayHospitalMode = "2"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetInspectionReports() {
        val result = runBlocking {
            BSoftService.me.getInspectionReports(
                InspectionReportForm(
                    patientId = "648403",
                    reportNo = "20230417FGZ0914"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetCheckRecords() {
        val result = runBlocking {
            BSoftService.me.getCheckRecords(
                CheckRecordForm(
                    patientId = "648403"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetCheckReports() {
        val result = runBlocking {
            BSoftService.me.getCheckReports(
                CheckReportForm(
                    reportId = "1133375"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetElectronicBills() {
        val result = runBlocking {
            BSoftService.me.getElectronicBills(
                ElectronicBillForm(
                    patientIdCard = "653021193209210228",
                    outpatientInpatient = 1
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun menzhenRecharge() {
        val result = runBlocking {
            BSoftService.me.menzhenRecharge(
                MenzhenRechargeForm(
                    patientId = "483322",
                    patientCard = "**********",
                    batchNumber = "12333",
                    orderNumber = "1233",
                    debitAmount = "20".toBigDecimal(),
                    operationType = "20",   // 掌医
                    payType = "16",         // 微信
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun menzhenRefund() {
        val result = runBlocking {
            BSoftService.me.menzhenRefund(
                MenzhenRefundForm(
                    patientId = "483322",
                    patientCard = "**********",
                    creditAmount = "20".toBigDecimal(),
                    orderNumber = "7899",
                    oldbOusinessSerialumber = "c5bef7a8c3604b939f26f8d45616",
                    operationType = "20",   // 掌医
                    payType = "16",         // 微信
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun getListCheckReport() {
        val result = runBlocking {
            BSoftService.me.getListCheckReport(
                ListCheckForm(
                    patientId = "829982",
                    source = "1"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    /**
     * 住院充值
     */
    @Test
    fun zhuyuanRecharge() {
        val result = runBlocking {
            BSoftService.me.zhuyuanRecharge(
                ZhuyuanRechargeForm(
                    admissionNo = "66988",
                    debitAmount = "20".toBigDecimal(),
                    orderNumber = "123",
                    payType = "20",
                    operationType = "10"
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    /**
     * 近期可挂号医生号源查询
     */
    @Test
    fun getReservationComingDoctor() {
        val form = ReservationComingDoctorForm("563")
        val result = runBlocking {
            BSoftService.me.getReservationComingDoctor(form)
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetUnpaidFees() {
        val result = runBlocking {
            BSoftService.me.getUnpaidFees(
                UnpaidFeeForm(
                    patientIdList = listOf("2113971"),
                    beginTime = LocalDateTime.parse("2025-03-12T00:00:00"),
                    endTime = LocalDateTime.parse("2025-03-14T23:59:59"),
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetUnpaidPrescription() {
        val result = runBlocking {
            BSoftService.me.getUnpaidPrescriptions(
                UnpaidPrescriptionForm(
                    patientId = "2113971",
                    startTime = LocalDateTime.parse("2025-03-12T00:00:00"),
                    endTime = LocalDateTime.parse("2025-03-14T23:59:59"),
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testGetUnpaidMedicalTechnology() {
        val result = runBlocking {
            BSoftService.me.getUnpaidMedicalTechnology(
                UnpaidMedicalTechnologyForm(
                    patientId = "2113971",
                    startTime = LocalDateTime.parse("2025-03-12T00:00:00"),
                    endTime = LocalDateTime.parse("2025-03-14T23:59:59"),
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }

    @Test
    fun testMenzhenSettlement() {
        val result = runBlocking {
            BSoftService.me.menzhenSettlement(
                MenzhenSettlementForm(
                    applyNos = "123",
                    debitAmount = "20",
                    batchNumber = "",
                    operator = "",
                    bankNo = "",
                    orderNumber = "",
                    patientId = "",
                    payType = "",
                    referenceNumber = "",
                    serialBatchNumber = "",
                    serialNumber = "",
                    terminalNumber = "",
                    totalMoney = ""
                )
            )
        }
        assertTrue(result.isOk(), result.message)
    }
}