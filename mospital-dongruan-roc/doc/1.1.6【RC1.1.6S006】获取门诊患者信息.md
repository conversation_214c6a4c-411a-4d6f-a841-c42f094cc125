# request

```bash
curl -s "http://127.0.0.1:10012/roc/curr-web/api/v1/common/out-patient/get/**********" \
     -H 'domain: NEU_HZFW' \
     -H 'key: f69897d6-d8a7-4c84-ac86-cca3b463b249' \
     --fail | jq . | pbcopy
```

# response

```json
{
  "code": 200,
  "msg": "操作成功！",
  "data": {
    "cardNo": "**********",
    "name": "李伟",
    "birthday": "1982-10-10 00:00:00",
    "sexCode": "M",
    "idenno": "654125198210100530",
    "home": "新疆新源县则克台镇阿西勒村青年路六巷22号",
    "homeTel": "***********",
    "nationCode": "03",
    "paykindCode": "01",
    "paykindName": "普通",
    "pactCode": "01",
    "pactName": "自费",
    "framt": 0,
    "anaphyFlag": "0",
    "hepatitisFlag": "0",
    "actAmt": 0,
    "lactSum": 0,
    "lbankSum": 0,
    "arrearTimes": 0,
    "arrearSum": 0,
    "operCode": "admin",
    "operDate": "2024-10-31 21:17:51",
    "isValid": "1",
    "isEncryptname": "0",
    "idcardtype": "01",
    "vipFlag": "0",
    "isTreatment": "0",
    "emrPatid": 2770618,
    "province": "650000",
    "city": "654100",
    "area": "654125",
    "province1": "650000",
    "city1": "654100",
    "area1": "654125",
    "province3": "650000",
    "city3": "654100",
    "area3": "654125"
  }
}
```