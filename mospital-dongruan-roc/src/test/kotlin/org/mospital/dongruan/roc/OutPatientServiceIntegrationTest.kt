package org.mospital.dongruan.roc

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * 门诊患者信息查询服务集成测试
 * 这个测试需要真实的网络连接和有效的ROC系统
 */
class OutPatientServiceIntegrationTest {

    private val logger = LoggerFactory.getLogger(OutPatientServiceIntegrationTest::class.java)
    private lateinit var service: DongruanRocService
    private lateinit var config: DongruanRocConfig

    @BeforeEach
    fun setUp() {
        // 使用测试配置
        config = DongruanRocConfig(
            url = "http://127.0.0.1:10012",
            domain = "NEU_HZFW",
            key = "f69897d6-d8a7-4c84-ac86-cca3b463b249",
            connectTimeout = 30000,
            readTimeout = 30000
        )
        service = DongruanRocService.getService(config)
    }

    @Test
    fun testGetOutPatientInfoRaw() = runTest {
        logger.info("集成测试 - 开始测试获取门诊患者信息原始接口")
        
        try {
            // 使用测试数据中的患者ID
            val patientId = "**********"
            val response = service.getOutPatientInfoRaw(patientId)
            
            // 验证响应结构
            assertNotNull(response, "响应不应为空")
            logger.info("响应码: ${response.code}")
            logger.info("响应消息: ${response.msg}")
            
            if (response.isSuccess) {
                assertNotNull(response.data, "成功响应的数据不应为空")
                val patientInfo = response.data
                
                // 验证基本信息
                assertEquals(patientId, patientInfo.cardNo, "患者卡号应该匹配")
                assertNotNull(patientInfo.name, "患者姓名不应为空")
                logger.info("患者姓名: ${patientInfo.name}")
                logger.info("患者性别: ${patientInfo.sexDescription}")
                logger.info("患者生日: ${patientInfo.birthday}")
                logger.info("身份证号: ${patientInfo.idenno}")
                logger.info("联系电话: ${patientInfo.homeTel}")
                logger.info("家庭地址: ${patientInfo.home}")
                logger.info("结算类别: ${patientInfo.paykindName}")
                logger.info("合同单位: ${patientInfo.pactName}")
                
                // 验证患者状态
                logger.info("是否有效患者: ${patientInfo.isValidPatient}")
                logger.info("是否VIP患者: ${patientInfo.isVip}")
                logger.info("是否有药物过敏: ${patientInfo.hasAllergy}")
                logger.info("是否有重大疾病: ${patientInfo.hasSeriousDisease}")
                logger.info("是否急诊患者: ${patientInfo.isEmergency}")
                
                // 验证金额信息
                logger.info("账户总额: ${patientInfo.actAmt}")
                logger.info("欠费金额: ${patientInfo.arrearSum}")
                logger.info("医疗费用: ${patientInfo.framt}")
                
                logger.info("集成测试 - 获取门诊患者信息原始接口测试通过")
            } else {
                logger.warn("获取门诊患者信息失败: ${response.msg}")
                // 对于失败的情况，我们也可以验证响应结构的正确性
                assertTrue(response.code != 200, "失败响应的状态码不应为200")
                assertNotNull(response.msg, "失败响应的消息不应为空")
            }
            
        } catch (e: Exception) {
            logger.error("集成测试失败", e)
            throw e
        }
    }

    @Test
    fun testGetOutPatientInfoConvenience() = runTest {
        logger.info("集成测试 - 开始测试获取门诊患者信息便捷方法")
        
        try {
            val patientId = "**********"
            val result = DongruanRocService.getOutPatientInfo(patientId)
            
            if (result.isSuccess) {
                val patientInfo = result.getOrNull()
                assertNotNull(patientInfo, "成功结果的数据不应为空")
                
                assertEquals(patientId, patientInfo.cardNo, "患者卡号应该匹配")
                assertNotNull(patientInfo.name, "患者姓名不应为空")
                
                logger.info("便捷方法测试 - 患者信息: ${patientInfo.name}")
                logger.info("集成测试 - 获取门诊患者信息便捷方法测试通过")
            } else {
                val exception = result.exceptionOrNull()
                logger.warn("获取门诊患者信息失败: ${exception?.message}")
                assertNotNull(exception, "失败结果应该包含异常信息")
            }
            
        } catch (e: Exception) {
            logger.error("集成测试失败", e)
            throw e
        }
    }

    @Test
    fun testGetOutPatientInfoWithInvalidId() = runTest {
        logger.info("集成测试 - 开始测试使用无效患者ID获取门诊患者信息")
        
        try {
            val invalidPatientId = "INVALID_ID_12345"
            val response = service.getOutPatientInfoRaw(invalidPatientId)
            
            // 验证响应结构
            assertNotNull(response, "响应不应为空")
            logger.info("无效ID响应码: ${response.code}")
            logger.info("无效ID响应消息: ${response.msg}")
            
            // 对于无效ID，通常会返回错误状态码或空数据
            if (!response.isSuccess) {
                assertTrue(response.code != 200, "无效ID的响应状态码不应为200")
                assertNotNull(response.msg, "错误响应的消息不应为空")
                logger.info("集成测试 - 无效患者ID测试通过")
            } else if (response.data == null) {
                // 有些系统可能返回200但data为null
                assertNull(response.data, "无效ID应该返回空数据")
                logger.info("集成测试 - 无效患者ID返回空数据测试通过")
            } else {
                logger.warn("无效ID竟然返回了数据，这可能表示ID实际存在或系统行为不符合预期")
            }
            
        } catch (e: Exception) {
            logger.error("集成测试失败", e)
            throw e
        }
    }

    @Test
    fun testGetOutPatientInfoMultipleRequests() = runTest {
        logger.info("集成测试 - 开始测试多次请求门诊患者信息")
        
        try {
            val patientId = "**********"
            val requestCount = 3
            
            repeat(requestCount) { index ->
                logger.info("执行第 ${index + 1} 次请求")
                val response = service.getOutPatientInfoRaw(patientId)
                assertNotNull(response, "第 ${index + 1} 次响应不应为空")
                
                if (response.isSuccess) {
                    assertNotNull(response.data, "第 ${index + 1} 次成功响应的数据不应为空")
                    assertEquals(patientId, response.data.cardNo, "第 ${index + 1} 次响应的患者卡号应该匹配")
                }
                
                // 短暂延迟避免请求过于频繁
                kotlinx.coroutines.delay(100)
            }
            
            logger.info("集成测试 - 多次请求门诊患者信息测试通过")
            
        } catch (e: Exception) {
            logger.error("集成测试失败", e)
            throw e
        }
    }

    @Test
    fun testGetOutPatientInfoResponseTime() = runTest {
        logger.info("集成测试 - 开始测试门诊患者信息查询响应时间")
        
        try {
            val patientId = "**********"
            val startTime = System.currentTimeMillis()
            
            val response = service.getOutPatientInfoRaw(patientId)
            
            val endTime = System.currentTimeMillis()
            val responseTime = endTime - startTime
            
            logger.info("响应时间: ${responseTime}ms")
            
            // 验证响应时间合理（例如小于5秒）
            assertTrue(responseTime < 5000, "响应时间应该小于5秒")
            
            assertNotNull(response, "响应不应为空")
            
            logger.info("集成测试 - 门诊患者信息查询响应时间测试通过")
            
        } catch (e: Exception) {
            logger.error("集成测试失败", e)
            throw e
        }
    }
}