package org.mospital.dongruan.roc

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable

/**
 * 住院患者详细信息接口测试
 */
class InPatientDetailInfoTest {

    @Test
    @EnabledIfEnvironmentVariable(named = "TEST_DONGRUAN_ROC", matches = "true")
    fun `test getInPatientDetailInfo with real API`() = runTest {
        // 使用示例中的患者编号进行测试
        val inpatientNo = "ZY001**********"
        
        val result = DongruanRocService.getInPatientDetailInfo(inpatientNo)
        
        assertTrue(result.isSuccess, "API调用应该成功")
        
        val patientInfo = result.getOrNull()
        assertNotNull(patientInfo, "返回的患者信息不应为null")
        
        // 验证一些关键字段（基于示例数据）
        patientInfo?.let { info ->
            assertEquals("83011", info.patientId, "患者ID应该匹配")
            assertEquals("ZY001**********", info.hisPatientNo, "住院流水号应该匹配")
            assertEquals("**********", info.patientNo, "住院号应该匹配")
            assertEquals("**********", info.cardNo, "卡号应该匹配")
            assertEquals("毛力旦·地里夏提", info.name, "患者姓名应该匹配")
            assertEquals("F", info.genderCode, "性别编码应该匹配")
            assertEquals("654101199312231762", info.idenno, "身份证号应该匹配")
            assertEquals("0182", info.deptCode, "科室代码应该匹配")
            assertEquals("心理医学科", info.deptName, "科室名称应该匹配")
            assertEquals("8037005", info.bedNo, "床号应该匹配")
            assertEquals("8037", info.nurseCellCode, "护理单元代码应该匹配")
            assertEquals("心理医学科护士站", info.nurseCellName, "护理单元名称应该匹配")
            assertEquals("常军委", info.chargeDocName, "主治医师姓名应该匹配")
            assertEquals("谭佳佳", info.dutyNurseName, "责任护士姓名应该匹配")
            assertEquals("伴有躯体症状的中度抑郁发作", info.mainDiagnose, "主诊断应该匹配")
            assertEquals("I", info.inState, "在院状态应该匹配")
            
            println("患者基本信息：")
            println("- 姓名: ${info.name}")
            println("- 性别: ${info.genderCode}")
            println("- 身份证号: ${info.idenno}")
            println("- 住院号: ${info.patientNo}")
            println("- 科室: ${info.deptName}")
            println("- 床号: ${info.bedNo}")
            println("- 主治医师: ${info.chargeDocName}")
            println("- 责任护士: ${info.dutyNurseName}")
            println("- 主诊断: ${info.mainDiagnose}")
            println("- 入院时间: ${info.inDate}")
            println("- 在院状态: ${info.inState}")
            println("- 预交金: ${info.prepayCost}")
            println("- 总费用: ${info.totCost}")
            println("- 余额: ${info.freeCost}")
        }
    }

    @Test
    fun `test InPatientDetailInfo data class structure`() {
        // 测试数据类的基本结构
        val patientInfo = InPatientDetailInfo(
            name = "测试患者",
            genderCode = "M",
            patientNo = "TEST001",
            deptName = "测试科室",
            bedNo = "001",
            mainDiagnose = "测试诊断"
        )
        
        assertEquals("测试患者", patientInfo.name)
        assertEquals("M", patientInfo.genderCode)
        assertEquals("TEST001", patientInfo.patientNo)
        assertEquals("测试科室", patientInfo.deptName)
        assertEquals("001", patientInfo.bedNo)
        assertEquals("测试诊断", patientInfo.mainDiagnose)
        
        // 验证可选字段为null
        assertNull(patientInfo.height)
        assertNull(patientInfo.weight)
        assertNull(patientInfo.bloodCode)
    }

    @Test
    @EnabledIfEnvironmentVariable(named = "TEST_DONGRUAN_ROC", matches = "true")
    fun `test getInPatientDetailInfo with invalid inpatientNo`() = runTest {
        // 测试无效的患者编号
        val invalidInpatientNo = "INVALID_ID_12345"
        
        val result = DongruanRocService.getInPatientDetailInfo(invalidInpatientNo)
        
        // 根据API的实际行为，可能返回失败或者返回空数据
        // 这里我们记录结果用于了解API行为
        if (result.isFailure) {
            println("无效患者编号返回失败: ${result.exceptionOrNull()?.message}")
        } else {
            val patientInfo = result.getOrNull()
            println("无效患者编号返回结果: $patientInfo")
        }
    }
}
