package org.mospital.dongruan.roc

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import java.math.BigDecimal

/**
 * 门诊费用明细查询服务测试
 */
class FeeDetailServiceTest {

    companion object {
        private val logger = LoggerFactory.getLogger(FeeDetailServiceTest::class.java)
    }

    @Test
    fun `测试根据挂号流水号查询费用明细`() = runBlocking {
        // 根据挂号流水号查询
        val result = DongruanRocService.queryFeeDetail(clinicCode = "16779894")

        result.fold(
            onSuccess = { feeDetail ->
                logger.info("查询成功！")
                logger.info("科室信息: ${feeDetail.deptName} (${feeDetail.deptCode})")
                logger.info("科室地址: ${feeDetail.deptAddress}")
                logger.info("看诊时间: ${feeDetail.regDate}")
                logger.info("费用汇总: 自费=${feeDetail.ownCost}, 报销=${feeDetail.pubCost}, 优惠=${feeDetail.payCost}")

                feeDetail.feeList?.forEach { item ->
                    logger.info("费用明细: ${item.itemName} - 数量:${item.qty} 单价:${item.singlePrice} 金额:${item.ownCost}")
                    if (item.isDrug) {
                        logger.info("  药品信息: 规格=${item.specs}, 用法=${item.usageName}, 频次=${item.frequencyName}")
                    }
                    logger.info("  执行科室: ${item.execDpnm}")
                    logger.info("  医生: ${item.docName}")
                    logger.info("  缴费状态: ${if (item.isPaid) "已缴费" else "未缴费"}")
                }
            },
            onFailure = { exception ->
                logger.error("查询失败: ${exception.message}")
            }
        )
    }

    @Test
    fun `测试根据处方号查询费用明细`() = runBlocking {
        // 根据处方号查询
        val result = DongruanRocService.queryFeeDetail(recipeNo = "120797052")

        result.fold(
            onSuccess = { feeDetail ->
                logger.info("根据处方号查询成功！")
                logger.info("科室: ${feeDetail.deptName}")
                logger.info("费用项目数量: ${feeDetail.feeList?.size ?: 0}")
            },
            onFailure = { exception ->
                logger.error("根据处方号查询失败: ${exception.message}")
            }
        )
    }

    @Test
    fun `测试根据门诊号查询费用明细`() = runBlocking {
        // 根据门诊号查询
        val result = DongruanRocService.queryFeeDetail(cardNo = "0002805085")

        result.fold(
            onSuccess = { feeDetail ->
                logger.info("根据门诊号查询成功！")
                logger.info("总费用: ${feeDetail.ownCost}")
            },
            onFailure = { exception ->
                logger.error("根据门诊号查询失败: ${exception.message}")
            }
        )
    }

    @Test
    fun `测试根据时间范围查询费用明细`() = runBlocking {
        // 根据时间范围查询
        val result = DongruanRocService.queryFeeDetail(
            beginDate = "2025-07-15",
            endDate = "2025-07-15",
            cardNo = "0002805085"
        )

        result.fold(
            onSuccess = { feeDetail ->
                logger.info("根据时间范围查询成功！")
                feeDetail.feeList?.forEach { item ->
                    logger.info("${item.feeDate}: ${item.itemName} - ${item.ownCost}元")
                }
            },
            onFailure = { exception ->
                logger.error("根据时间范围查询失败: ${exception.message}")
            }
        )
    }

    @Test
    fun `测试查询已缴费的费用明细`() = runBlocking {
        // 查询已缴费的费用明细
        val result = DongruanRocService.queryFeeDetail(
            clinicCode = "16779894",
            payFlag = "1"  // 1表示已缴费
        )

        result.fold(
            onSuccess = { feeDetail ->
                logger.info("查询已缴费费用明细成功！")
                val paidItems = feeDetail.feeList?.filter { it.isPaid } ?: emptyList()
                logger.info("已缴费项目数量: ${paidItems.size}")

                paidItems.forEach { item ->
                    logger.info("已缴费项目: ${item.itemName} - ${item.ownCost}元 (缴费时间: ${item.feeDate})")
                }
            },
            onFailure = { exception ->
                logger.error("查询已缴费费用明细失败: ${exception.message}")
            }
        )
    }

    @Test
    fun `测试组合条件查询费用明细`() = runBlocking {
        // 组合多个查询条件
        val result = DongruanRocService.queryFeeDetail(
            clinicCode = "16779894",
            payFlag = "1",
            beginDate = "2025-07-15",
            endDate = "2025-07-15"
        )

        result.fold(
            onSuccess = { feeDetail ->
                logger.info("组合条件查询成功！")

                // 统计药品和非药品项目
                val drugItems = feeDetail.feeList?.filter { it.isDrug } ?: emptyList()
                val nonDrugItems = feeDetail.feeList?.filter { !it.isDrug } ?: emptyList()

                logger.info("药品项目数量: ${drugItems.size}")
                logger.info("非药品项目数量: ${nonDrugItems.size}")

                // 计算各类费用总额
                val totalOwnCost = feeDetail.feeList?.mapNotNull {
                    it.ownCost
                }?.fold(BigDecimal.ZERO) { acc, cost ->
                    acc + cost
                }
                logger.info("自费总额: ${totalOwnCost}元")
            },
            onFailure = { exception ->
                logger.error("组合条件查询失败: ${exception.message}")
            }
        )
    }
}