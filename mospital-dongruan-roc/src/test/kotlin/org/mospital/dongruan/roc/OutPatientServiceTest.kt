package org.mospital.dongruan.roc

import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * 门诊患者信息查询服务单元测试
 */
class OutPatientServiceTest {

    private val logger = LoggerFactory.getLogger(OutPatientServiceTest::class.java)
    private lateinit var mockService: DongruanRocService

    @BeforeEach
    fun setUp() {
        // 使用MockK创建mock对象
        mockService = mockk<DongruanRocService>()
    }

    @Test
    fun testOutPatientInfoDataClassProperties() {
        // 测试数据类的属性和方法
        val patientInfo = createMockPatientInfo()
        
        // 测试基本属性
        assertEquals("**********", patientInfo.cardNo)
        assertEquals("李伟", patientInfo.name)
        assertEquals(LocalDate.parse("1982-10-10"), patientInfo.birthday)
        assertEquals("M", patientInfo.sexCode)
        assertEquals("654125198210100530", patientInfo.idenno)
        assertEquals("新疆新源县则克台镇阿西勒村青年路六巷22号", patientInfo.home)
        assertEquals("19190678611", patientInfo.homeTel)
        assertEquals("03", patientInfo.nationCode)
        assertEquals("01", patientInfo.paykindCode)
        assertEquals("普通", patientInfo.paykindName)
        assertEquals("01", patientInfo.pactCode)
        assertEquals("自费", patientInfo.pactName)
        assertEquals("1", patientInfo.isValid)
        assertEquals("0", patientInfo.isEncryptname)
        assertEquals("01", patientInfo.idcardtype)
        assertEquals("0", patientInfo.vipFlag)
        assertEquals("0", patientInfo.isTreatment)
        assertEquals(2770618, patientInfo.emrPatid)
        
        // 测试计算属性
        assertTrue(patientInfo.isValidPatient, "应该为有效患者")
        assertFalse(patientInfo.isVip, "不应该为VIP患者")
        assertFalse(patientInfo.hasAllergy, "不应该有药物过敏史")
        assertFalse(patientInfo.hasSeriousDisease, "不应该有重大疾病")
        assertFalse(patientInfo.isEmergency, "不应该为急诊患者")

        // 测试性别描述
        assertEquals("男", patientInfo.sexDescription)
        
        logger.info("单元测试 - 数据类属性测试通过")
    }

    @Test
    fun testOutPatientInfoFemaleVipPatient() {
        // 测试女性VIP患者
        val femaleVipPatient = createMockPatientInfo(
            sexCode = "F",
            vipFlag = "1",
            anaphyFlag = "1",
            hepatitisFlag = "1",
            isTreatment = "1"
        )
        
        assertEquals("女", femaleVipPatient.sexDescription)
        assertTrue(femaleVipPatient.isVip, "应该为VIP患者")
        assertTrue(femaleVipPatient.hasAllergy, "应该有药物过敏史")
        assertTrue(femaleVipPatient.hasSeriousDisease, "应该有重大疾病")
        assertTrue(femaleVipPatient.isEmergency, "应该为急诊患者")
        
        logger.info("单元测试 - 女性VIP患者测试通过")
    }

    @Test
    fun testOutPatientInfoInvalidPatient() {
        // 测试无效患者
        val invalidPatient = createMockPatientInfo(isValid = "0")
        
        assertFalse(invalidPatient.isValidPatient, "不应该为有效患者")
        
        logger.info("单元测试 - 无效患者测试通过")
    }

    @Test
    fun testGetOutPatientInfoSuccess() = runTest {
        // 模拟成功响应
        val mockResponse = DongruanRocResponse(
            code = 200,
            msg = "操作成功！",
            data = createMockPatientInfo()
        )
        
        coEvery { mockService.getOutPatientInfoRaw("**********") } returns mockResponse
        
        // 调用方法
        val result = mockService.getOutPatientInfoRaw("**********")
        
        // 验证结果
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(200, result.code)
        assertEquals("操作成功！", result.msg)
        assertNotNull(result.data)
        assertEquals("**********", result.data?.cardNo)
        assertEquals("李伟", result.data?.name)
        
        // 验证调用
        coVerify(exactly = 1) { mockService.getOutPatientInfoRaw("**********") }
        
        logger.info("单元测试 - 获取门诊患者信息成功测试通过")
    }

    @Test
    fun testGetOutPatientInfoFailure() = runTest {
        // 模拟失败响应
        val mockResponse = DongruanRocResponse(
            code = 500,
            msg = "患者不存在",
            data = null as OutPatientInfo?
        )
        
        coEvery { mockService.getOutPatientInfoRaw("invalid_id") } returns mockResponse
        
        // 调用方法
        val result = mockService.getOutPatientInfoRaw("invalid_id")
        
        // 验证结果
        assertNotNull(result)
        assertFalse(result.isSuccess)
        assertEquals(500, result.code)
        assertEquals("患者不存在", result.msg)
        
        // 验证调用
        coVerify(exactly = 1) { mockService.getOutPatientInfoRaw("invalid_id") }
        
        logger.info("单元测试 - 获取门诊患者信息失败测试通过")
    }

    /**
     * 创建模拟的门诊患者信息
     */
    private fun createMockPatientInfo(
        cardNo: String = "**********",
        name: String = "李伟",
        birthday: LocalDate = LocalDate.parse("1982-10-10"),
        sexCode: String = "M",
        idenno: String = "654125198210100530",
        home: String = "新疆新源县则克台镇阿西勒村青年路六巷22号",
        homeTel: String = "19190678611",
        nationCode: String = "03",
        paykindCode: String = "01",
        paykindName: String = "普通",
        pactCode: String = "01",
        pactName: String = "自费",
        isValid: String = "1",
        isEncryptname: String = "0",
        idcardtype: String = "01",
        vipFlag: String = "0",
        isTreatment: String = "0",
        anaphyFlag: String = "0",
        hepatitisFlag: String = "0",
        emrPatid: Int = 2770618,
        actAmt: BigDecimal = BigDecimal.ZERO,
        arrearSum: BigDecimal = BigDecimal.ZERO,
        framt: BigDecimal = BigDecimal.ZERO
    ): OutPatientInfo {
        return OutPatientInfo(
            cardNo = cardNo,
            name = name,
            birthday = birthday,
            sexCode = sexCode,
            idenno = idenno,
            home = home,
            homeTel = homeTel,
            nationCode = nationCode,
            paykindCode = paykindCode,
            paykindName = paykindName,
            pactCode = pactCode,
            pactName = pactName,
            isValid = isValid,
            isEncryptname = isEncryptname,
            idcardtype = idcardtype,
            vipFlag = vipFlag,
            isTreatment = isTreatment,
            anaphyFlag = anaphyFlag,
            hepatitisFlag = hepatitisFlag,
            emrPatid = emrPatid,
            actAmt = actAmt,
            arrearSum = arrearSum,
            framt = framt,
            // 其他字段使用默认值
            actCode = null,
            area = "654125",
            area1 = "654125",
            area2 = null,
            area3 = "654125",
            areaCode = null,
            arrearTimes = 0,
            bloodCode = null,
            caseNo = null,
            city = "654100",
            city1 = "654100",
            city2 = null,
            city3 = "654100",
            counCode = null,
            disobyCnt = null,
            district = null,
            email = null,
            emplCode = null,
            endDate = null,
            feeKind = null,
            firSeeDate = null,
            homeDoorNo = null,
            homeZip = null,
            icCardno = null,
            inhosSource = null,
            inhosTimes = null,
            insuranceId = null,
            insuranceName = null,
            lactSum = BigDecimal.ZERO,
            lbankSum = BigDecimal.ZERO,
            lihosDate = null,
            linkmanAdd = null,
            linkmanDoorNo = null,
            linkmanName = null,
            linkmanTel = null,
            louthosDate = null,
            lregDate = null,
            mari = null,
            mark = null,
            mcardNo = null,
            montherName = null,
            normalname = null,
            oldCardno = null,
            operCode = "admin",
            operDate = LocalDateTime.of(2024, 10, 31, 21, 17, 51),
            profCode = null,
            profName = null,
            province = "650000",
            province1 = "650000",
            province2 = null,
            province3 = "650000",
            relaCode = null,
            road = null,
            road1 = null,
            spellCode = null,
            wbCode = null,
            workHome = null,
            workTel = null,
            workZip = null
        )
    }
}