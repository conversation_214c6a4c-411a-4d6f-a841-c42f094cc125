package org.mospital.dongruan.roc

import java.time.LocalDate

import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.DateTimeFormatters
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.Body
import retrofit2.http.Path

/**
 * 东软ROC系统集成平台服务接口
 */
interface DongruanRocService {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(DongruanRocService::class.java)
        private val serviceCache = mutableMapOf<DongruanRocConfig, DongruanRocService>()

        /**
         * 统一 Header 拦截器
         * 自动为所有请求添加 domain 和 key header
         */
        private class HeaderInterceptor(
            private val domain: String,
            private val key: String
        ) : Interceptor {
            override fun intercept(chain: Interceptor.Chain): Response {
                val originalRequest = chain.request()
                val requestWithHeaders = originalRequest.newBuilder()
                    .header("domain", domain)
                    .header("key", key)
                    .build()
                return chain.proceed(requestWithHeaders)
            }
        }

        private fun createService(config: DongruanRocConfig): DongruanRocService {
            val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = config.connectTimeout,
                readTimeout = config.readTimeout,
                useUuidRequestId = true
            ) { builder ->
                builder.addInterceptor(HeaderInterceptor(config.domain, config.key))
            }

            val retrofit = Retrofit.Builder()
                .baseUrl(config.url)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(JacksonKit.buildMapper()))
                .build()
            return retrofit.create(DongruanRocService::class.java)
        }

        @Synchronized
        fun getService(config: DongruanRocConfig): DongruanRocService {
            return serviceCache.getOrPut(config) { createService(config) }
        }

        val me: DongruanRocService by lazy {
            getService(DongruanRocConfig.me)
        }

        /**
         * 查询药品物价项目列表（便捷方法，直接返回 Result）
         * @param vagueName 项目名称，可选
         * @param simpleSpell 拼音码，可选
         * @param identifyKey 项目编码，可选
         * @param sysClassList 系统类别，可选
         * @return 药品物价列表
         */
        suspend fun queryDrugPriceList(
            vagueName: String? = null,
            simpleSpell: String? = null,
            identifyKey: String? = null,
            sysClassList: String? = null
        ): Result<List<DrugPriceInfo>> {
            return try {
                val response = me.queryDrugPriceListRaw(
                    vagueName = vagueName,
                    simpleSpell = simpleSpell,
                    identifyKey = identifyKey,
                    sysClassList = sysClassList
                )
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 查询非药品物价项目列表（便捷方法，直接返回 Result）
         * @param vagueName 项目名称，可选
         * @param simpleSpell 拼音码，可选
         * @param identifyKey 项目编码，可选
         * @param sysClassList 系统类别，可选
         * @return 非药品物价列表
         */
        suspend fun queryUndrugPriceList(
            vagueName: String? = null,
            simpleSpell: String? = null,
            identifyKey: String? = null,
            sysClassList: String? = null
        ): Result<List<UndrugPriceInfo>> {
            return try {
                val response = me.queryUndrugPriceListRaw(
                    vagueName = vagueName,
                    simpleSpell = simpleSpell,
                    identifyKey = identifyKey,
                    sysClassList = sysClassList
                )
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 查询可挂号科室列表（便捷方法，直接返回 Result）
         * @param beginDate 开始日期
         * @param endDate 结束日期
         * @param branchCode 院区编码，可选
         * @param limitType 号源类型（多个类型使用逗号","拼接），可选。0:线下号源 1:线上号源 2:特诊号源 3:线下加号 4:线上加号
         * @param filter 是否过滤当前时间以后的有效排班，可选
         * @param dto 查询可挂号科室列表入参，可选
         * @return 可挂号科室列表
         */
        suspend fun queryAvailableDepts(
            beginDate: LocalDate,
            endDate: LocalDate,
            branchCode: String? = null,
            limitType: String? = null,
            filter: Boolean? = null,
            dto: String? = null
        ): Result<List<DeptInfo>> {
            return try {
                val response = me.queryAvailableDeptsRaw(
                    beginTime = beginDate.format(DateTimeFormatters.NORM_DATE_FORMATTER),
                    endTime = endDate.format(DateTimeFormatters.NORM_DATE_FORMATTER),
                    branchCode = branchCode,
                    limitType = limitType,
                    filter = filter?.let { if (it) 1 else 0 },
                    dto = dto
                )
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 查询科室医生排班列表（便捷方法，直接返回 Result）
         * 如果要查询某一日的排班，可以将 beginDate 和 endDate 设置为同一天
         * @param beginDate 开始日期
         * @param endDate 结束日期
         * @param branchCode 院区编码，可选
         * @param docCode 医生编码，可选
         * @param deptCode 挂号科室编码，可选
         * @param schemaType 排班类型（0：科室 1：医生），可选
         * @param pactCode 合同单位:01-自费,02-市医保,默认01，可选
         * @param filter 是否过滤当前时间以后的有效排班，可选
         * @param limitType 号源类型（多个类型使用逗号","拼接），可选。0:线下号源 1:线上号源 2:特诊号源 3:线下加号 4:线上加号
         * @param dto 查询科室医生排班列表入参，可选
         * @return 科室医生排班列表
         */
        suspend fun queryDoctorScheduleList(
            beginDate: LocalDate,
            endDate: LocalDate,
            branchCode: String? = null,
            docCode: String? = null,
            deptCode: String? = null,
            schemaType: String? = null,
            pactCode: String? = null,
            filter: Boolean? = null,
            limitType: String? = null,
            dto: String? = null
        ): Result<List<DoctorScheduleInfo>> {
            return try {
                val response = me.queryDoctorScheduleListRaw(
                    beginTime = beginDate.format(DateTimeFormatters.NORM_DATE_FORMATTER),
                    endTime = endDate.format(DateTimeFormatters.NORM_DATE_FORMATTER),
                    branchCode = branchCode,
                    docCode = docCode,
                    deptCode = deptCode,
                    schemaType = schemaType,
                    pactCode = pactCode,
                    filter = filter?.let { if (it) 1 else 0 },
                    limitType = limitType,
                    dto = dto
                )
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 获取排班号位明细列表（便捷方法，直接返回 Result）
         * @param schemaId 排班ID，必须
         * @param timeType 时间点类型，可选。XCGH 现场挂号;XCYY 预约挂号;XCGH(JH) 现场加号;XCYY(JH) 预约加号
         * @param isValid 是否正常排班，可选。true-正常，false-停诊，null-不过滤
         * @param isUsed 占用情况，可选。true-已占用，false-未占用，null-不过滤
         * @param filter 是否过滤当前时间以后的有效排班，可选
         * @param dto 获取排班号位明细列表入参，可选
         * @return 排班号位明细信息
         */
        suspend fun getSchemaDetail(
            schemaId: String,
            timeType: String? = null,
            isValid: Boolean? = null,
            isUsed: Boolean? = null,
            filter: Boolean? = null,
            dto: String? = null
        ): Result<SchemaDetailInfo> {
            return try {
                val response = me.getSchemaDetailRaw(
                    schemaId = schemaId,
                    timeType = timeType,
                    validFlag = isValid?.let { if (it) 1 else 0 },
                    usedStatus = isUsed?.let { if (it) 1 else 0 },
                    filter = filter?.let { if (it) 1 else 0 },
                    dto = dto
                )
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 查询门诊患者挂号记录（便捷方法，直接返回 Result）
         * @param beginTime 开始时间，可选
         * @param branchCode 院区编码，可选
         * @param cardNo 卡号，可选
         * @param clinicCode 挂号流水号，可选
         * @param endTime 看诊结束时间-MM-DD HH24:MI:SS，可选
         * @param idNo 身份证号，可选
         * @param patientName 姓名，可选
         * @return 门诊患者挂号记录列表
         */
        suspend fun queryOutpatientRegistrationList(
            beginTime: String? = null,
            branchCode: String? = null,
            cardNo: String? = null,
            clinicCode: String? = null,
            endTime: String? = null,
            idNo: String? = null,
            patientName: String? = null
        ): Result<List<OutpatientRegistrationInfo>> {
            return try {
                val request = OutpatientRegistrationRequest(
                    beginTime = beginTime,
                    branchCode = branchCode,
                    cardNo = cardNo,
                    clinicCode = clinicCode,
                    endTime = endTime,
                    idNo = idNo,
                    patientName = patientName
                )
                val response = me.queryOutpatientRegistrationListRaw(request)
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 根据号位占号（便捷方法，直接返回 Result）
         * @param cardNo 患者ID --院内唯一索引
         * @param limitType 号源类型（多个类型使用逗号","拼接）:0 线下号源;1 线上号源;2 特诊号源;3 线下加号;4 线上加号
         * @param schemaId 排班ID
         * @param sourseCode 客户端类型 窗口-WIN 互联网-NET 公众号 OA 自助机-ZZJ APP-APP 小程序-MP 诊间-ZJ
         * @param bookFlag 占号类型标识（默认0） 0-当日挂号占号 1-预约挂号占号，可选
         * @param clinicCode 挂号流水号，可选
         * @param operCode 当前操作人代码，可选
         * @param pactCode 合同单位，可选
         * @param sortNo 排班序号，可选
         * @param timeRangeId 排班时间段id，可选
         * @return 占号结果
         */
        suspend fun occupyNumber(
            cardNo: String,
            limitType: String,
            schemaId: String,
            sourseCode: String,
            bookFlag: String? = null,
            clinicCode: String? = null,
            operCode: String? = null,
            pactCode: String? = null,
            sortNo: Int? = null,
            timeRangeId: String? = null
        ): Result<TakeSchemaTo> {
            return try {
                val request = OccupyNumberRequest(
                    bookFlag = bookFlag,
                    cardNo = cardNo,
                    clinicCode = clinicCode,
                    limitType = limitType,
                    operCode = operCode,
                    pactCode = pactCode,
                    schemaId = schemaId,
                    sortNo = sortNo,
                    sourseCode = sourseCode,
                    timeRangeId = timeRangeId
                )
                val response = me.occupyNumberRaw(request)
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 取消占号（便捷方法，直接返回 Result）
         * @param cardNo 患者卡号
         * @param clinicCode 挂号流水号
         * @param operCode 当前操作人
         * @param sourseCode 客户端类型 窗口-WIN 互联网-NET 公众号 OA 自助机-ZZJ APP-APP 小程序-MP 诊间-ZJ
         * @param billNo 订单号，可选
         * @param bookId 预约表ID，可选
         * @return 取消占号结果（返回操作结果数值）
         */
        suspend fun cancelOccupyNumber(
            cardNo: String,
            clinicCode: String,
            operCode: String,
            sourseCode: String,
            billNo: String? = null,
            bookId: String? = null
        ): Result<Int> {
            return try {
                val request = CancelOccupyNumberRequest(
                    billNo = billNo,
                    bookId = bookId,
                    cardNo = cardNo,
                    clinicCode = clinicCode,
                    operCode = operCode,
                    sourseCode = sourseCode
                )
                val response = me.cancelOccupyNumberRaw(request)
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 获取挂号订单信息（便捷方法，直接返回 Result）
         * @param branchCode 院区编码，可选
         * @param billNo 订单号，可选
         * @param clinicCode 门诊流水号，可选
         * @param cardNo 门诊号，可选
         * @param bookingId 预约流水号，可选
         * @param transNo 聚合支付订单号，可选
         * @param type 订单类型：1占号 2取消占号 3挂号缴费 4退号，可选
         * @param beginTime 订单创建时间-起始，可选
         * @param endTime 订单创建时间-截止，可选
         * @return 挂号订单信息列表
         */
        suspend fun getRegisterBillInfo(
            branchCode: String? = null,
            billNo: String? = null,
            clinicCode: String? = null,
            cardNo: String? = null,
            bookingId: String? = null,
            transNo: String? = null,
            type: String? = null,
            beginTime: String? = null,
            endTime: String? = null
        ): Result<List<RegisterBillInfo>> {
            return try {
                val response = me.getRegisterBillInfoRaw(
                    branchCode = branchCode,
                    billNo = billNo,
                    clinicCode = clinicCode,
                    cardNo = cardNo,
                    bookingId = bookingId,
                    transNo = transNo,
                    type = type,
                    beginTime = beginTime,
                    endTime = endTime
                )
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 获取门诊患者信息（便捷方法，直接返回 Result）
         * @param outPatientId 患者编号
         * @return 门诊患者信息
         */
        suspend fun getOutPatientInfo(outPatientId: String): Result<OutPatientInfo> {
            return try {
                val response = me.getOutPatientInfoRaw(outPatientId)
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 查询门诊患者费用明细列表（便捷方法，直接返回 Result）
         * @param clinicCode 挂号流水号，可选
         * @param recipeNo 处方号，可选
         * @param orderId 医嘱id，可选
         * @param payFlag 收费标识，可选
         * @param cardNo 门诊号，可选
         * @param beginDate 收费时间范围From，可选
         * @param endDate 收费时间范围To，可选
         * @return 门诊费用明细信息
         */
        suspend fun queryFeeDetail(
            clinicCode: String? = null,
            recipeNo: String? = null,
            orderId: String? = null,
            payFlag: String? = null,
            cardNo: String? = null,
            beginDate: String? = null,
            endDate: String? = null
        ): Result<FeeDetailInfo> {
            return try {
                val response = me.queryFeeDetailRaw(
                    clinicCode = clinicCode,
                    recipeNo = recipeNo,
                    orderId = orderId,
                    payFlag = payFlag,
                    cardNo = cardNo,
                    beginDate = beginDate,
                    endDate = endDate
                )
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 查询住院患者列表（便捷方法，直接返回 Result）
         * @param cardNo 就诊卡号，可选
         * @param deptCode 科室编码，可选
         * @param nurseCellCode 护理单元编码，可选
         * @param idenno 身份证号，可选
         * @param tel 电话号码，可选
         * @param patientNo 住院号，可选
         * @param patientId emr患者ID，可选
         * @param patientName 患者姓名，可选
         * @param inDate 入院时间 YYYY-MM-DD HH24:MI:SS，可选
         * @param patientState 患者在院状态，可选
         * @param outDate 出院时间，可选
         * @param beginTime 筛选时间begin，可选
         * @param endTime 筛选时间end，可选
         * @param pageSize 数据分页条数，可选
         * @param pageNum 分页页码，可选
         * @param pageFlag 分页flag，可选
         * @param outBeginDate 开始时间（出院），可选
         * @param outEndDate 结束时间（出院），可选
         * @return 住院患者列表信息
         */
        suspend fun queryInPatientList(
            cardNo: String? = null,
            deptCode: String? = null,
            nurseCellCode: String? = null,
            idenno: String? = null,
            tel: String? = null,
            patientNo: String? = null,
            patientId: String? = null,
            patientName: String? = null,
            inDate: String? = null,
            patientState: String? = null,
            outDate: String? = null,
            beginTime: String? = null,
            endTime: String? = null,
            pageSize: Int? = null,
            pageNum: Int? = null,
            pageFlag: String? = null,
            outBeginDate: String? = null,
            outEndDate: String? = null
        ): Result<InPatientListResponse> {
            return try {
                val response = me.queryInPatientListRaw(
                    cardNo = cardNo,
                    deptCode = deptCode,
                    nurseCellCode = nurseCellCode,
                    idenno = idenno,
                    tel = tel,
                    patientNo = patientNo,
                    patientId = patientId,
                    patientName = patientName,
                    inDate = inDate,
                    patientState = patientState,
                    outDate = outDate,
                    beginTime = beginTime,
                    endTime = endTime,
                    pageSize = pageSize,
                    pageNum = pageNum,
                    pageFlag = pageFlag,
                    outBeginDate = outBeginDate,
                    outEndDate = outEndDate
                )
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        /**
         * 获取住院患者详细信息（便捷方法，直接返回 Result）
         * @param inpatientNo 患者编号
         * @return 住院患者详细信息
         */
        suspend fun getInPatientDetailInfo(inpatientNo: String): Result<InPatientDetailInfo> {
            return try {
                val response = me.getInPatientDetailInfoRaw(inpatientNo)
                response.toResult()
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 【RC1.1.12S012】查询药品物价项目列表（原始接口）
     * @param vagueName 项目名称
     * @param simpleSpell 拼音码
     * @param identifyKey 项目编码
     * @param sysClassList 系统类别
     * @return 药品物价列表响应
     */
    @GET("/roc/curr-web/api/v1/curr/pharmaceutical/drug/query")
    suspend fun queryDrugPriceListRaw(
        @Query("vagueName") vagueName: String? = null,
        @Query("simpleSpell") simpleSpell: String? = null,
        @Query("identifyKey") identifyKey: String? = null,
        @Query("sysClassList") sysClassList: String? = null
    ): DongruanRocResponse<List<DrugPriceInfo>>

    /**
     * 【RC1.1.13S013】查询非药品物价项目列表（原始接口）
     * @param vagueName 项目名称
     * @param simpleSpell 拼音码
     * @param identifyKey 项目编码
     * @param sysClassList 系统类别
     * @return 非药品物价列表响应
     */
    @GET("/roc/curr-web/api/v1/curr/unpharmaceutical/undrug/eisaiOrDiagnose/query")
    suspend fun queryUndrugPriceListRaw(
        @Query("vagueName") vagueName: String? = null,
        @Query("simpleSpell") simpleSpell: String? = null,
        @Query("identifyKey") identifyKey: String? = null,
        @Query("sysClassList") sysClassList: String? = null
    ): DongruanRocResponse<List<UndrugPriceInfo>>

    /**
     * 【RP2.3.1S078】查询可挂号科室列表（原始接口）
     * @param beginTime 开始时间 yyyy-MM-dd
     * @param endTime 结束时间 yyyy-MM-dd
     * @param branchCode 院区编码
     * @param limitType 号源类型（多个类型使用逗号","拼接）:0 线下号源;1 线上号源;2 特诊号源;3 线下加号;4 线上加号
     * @param filter 是否过滤当前时间以后的有效排班
     * @param dto 查询可挂号科室列表入参
     * @return 可挂号科室列表响应
     */
    @GET("/roc/patient-service/api/v1/register/schema/dept/query")
    suspend fun queryAvailableDeptsRaw(
        @Query("beginTime") beginTime: String,
        @Query("endTime") endTime: String,
        @Query("branchCode") branchCode: String? = null,
        @Query("limitType") limitType: String? = null,
        @Query("filter") filter: Int? = null,
        @Query("dto") dto: String? = null
    ): DongruanRocResponse<List<DeptInfo>>

    /**
     * 【RP2.3.2S079】查询科室医生排班列表（原始接口）
     * @param beginTime 开始时间 yyyy-MM-dd
     * @param endTime 结束时间 yyyy-MM-dd
     * @param branchCode 院区编码
     * @param docCode 医生编码
     * @param deptCode 挂号科室编码
     * @param schemaType 排班类型（0：科室 1：医生）
     * @param pactCode 合同单位:01-自费,02-市医保,默认01
     * @param filter 是否过滤当前时间以后的有效排班
     * @param limitType 号源类型（多个类型使用逗号","拼接）:0 线下号源;1 线上号源;2 特诊号源;3 线下加号;4 线上加号
     * @param dto 查询科室医生排班列表入参
     * @return 科室医生排班列表响应
     */
    @GET("/roc/patient-service/api/v1/register/schema/doctor/query")
    suspend fun queryDoctorScheduleListRaw(
        @Query("beginTime") beginTime: String,
        @Query("endTime") endTime: String,
        @Query("branchCode") branchCode: String? = null,
        @Query("docCode") docCode: String? = null,
        @Query("deptCode") deptCode: String? = null,
        @Query("schemaType") schemaType: String? = null,
        @Query("pactCode") pactCode: String? = null,
        @Query("filter") filter: Int? = null,
        @Query("limitType") limitType: String? = null,
        @Query("dto") dto: String? = null
    ): DongruanRocResponse<List<DoctorScheduleInfo>>

    /**
     * 【RP2.3.4S081】获取排班号位明细列表（原始接口）
     * @param schemaId 排班ID，必须
     * @param timeType 时间点类型，可选。XCGH 现场挂号;XCYY 预约挂号;XCGH(JH) 现场加号;XCYY(JH) 预约加号
     * @param validFlag 1正常/0停诊 默认1，可选
     * @param usedStatus 占用情况，0：未占用 1：已占用，可选
     * @param filter 是否过滤当前时间以后的有效排班，可选
     * @param dto 获取排班号位明细列表入参，可选
     * @return 排班号位明细信息响应
     */
    @GET("/roc/patient-service/api/v1/register/schema/doctor/multimes/get")
    suspend fun getSchemaDetailRaw(
        @Query("schemaId") schemaId: String,
        @Query("timeType") timeType: String? = null,
        @Query("validFlag") validFlag: Int? = null,
        @Query("usedStatus") usedStatus: Int? = null,
        @Query("filter") filter: Int? = null,
        @Query("dto") dto: String? = null
    ): DongruanRocResponse<SchemaDetailInfo>

    /**
     * 【RP2.1.6S062】查询门诊患者挂号记录（原始接口）
     * @param request 查询门诊患者挂号记录请求参数
     * @return 门诊患者挂号记录列表响应
     */
    @POST("/roc/patient-service/api/v1/register/patient/getFinOprRegList")
    suspend fun queryOutpatientRegistrationListRaw(
        @Body request: OutpatientRegistrationRequest
    ): DongruanRocResponse<List<OutpatientRegistrationInfo>>

    /**
     * 【RP2.2.3U068】根据号位占号（原始接口）
     * @param request 根据号位占号请求参数
     * @return 占号结果响应
     */
    @POST("/roc/patient-service/api/v1/register/occupy/confirm")
    suspend fun occupyNumberRaw(
        @Body request: OccupyNumberRequest
    ): DongruanRocResponse<TakeSchemaTo>

    /**
     * 【RP2.2.4U069】取消占号（原始接口）
     * @param request 取消占号请求参数
     * @return 取消占号结果响应
     */
    @POST("/roc/patient-service/api/v1/register/occupy/cancel")
    suspend fun cancelOccupyNumberRaw(
        @Body request: CancelOccupyNumberRequest
    ): DongruanRocResponse<Int>

    /**
     * 【RP2.1.2S058】获取挂号订单信息（原始接口）
     * @param branchCode 院区编码，可选
     * @param billNo 订单号，可选
     * @param clinicCode 门诊流水号，可选
     * @param cardNo 门诊号，可选
     * @param bookingId 预约流水号，可选
     * @param transNo 聚合支付订单号，可选
     * @param type 订单类型：1占号 2取消占号 3挂号缴费 4退号，可选
     * @param beginTime 订单创建时间-起始，可选
     * @param endTime 订单创建时间-截止，可选
     * @return 挂号订单信息列表响应
     */
    @GET("/roc/patient-service/api/v1/register/patient/bill/get")
    suspend fun getRegisterBillInfoRaw(
        @Query("branchCode") branchCode: String? = null,
        @Query("billNo") billNo: String? = null,
        @Query("clinicCode") clinicCode: String? = null,
        @Query("cardNo") cardNo: String? = null,
        @Query("bookingId") bookingId: String? = null,
        @Query("transNo") transNo: String? = null,
        @Query("type") type: String? = null,
        @Query("beginTime") beginTime: String? = null,
        @Query("endTime") endTime: String? = null
    ): DongruanRocResponse<List<RegisterBillInfo>>

    /**
     * 【RC1.1.6S006】获取门诊患者信息（原始接口）
     * @param outPatientId 患者编号
     * @return 门诊患者信息响应
     */
    @GET("/roc/curr-web/api/v1/common/out-patient/get/{outPatientId}")
    suspend fun getOutPatientInfoRaw(
        @Path("outPatientId") outPatientId: String
    ): DongruanRocResponse<OutPatientInfo>

    /**
     * 【RF2.1.1S125】查询门诊患者费用明细列表（原始接口）
     * @param clinicCode 挂号流水号
     * @param recipeNo 处方号
     * @param orderId 医嘱id
     * @param payFlag 收费标识
     * @param cardNo 门诊号
     * @param beginDate 收费时间范围From
     * @param endDate 收费时间范围To
     * @return 门诊费用明细信息响应
     */
    @GET("/roc/fee-service/api/v1/fee/detail/query")
    suspend fun queryFeeDetailRaw(
        @Query("clinicCode") clinicCode: String? = null,
        @Query("recipeNo") recipeNo: String? = null,
        @Query("orderId") orderId: String? = null,
        @Query("payFlag") payFlag: String? = null,
        @Query("cardNo") cardNo: String? = null,
        @Query("beginDate") beginDate: String? = null,
        @Query("endDate") endDate: String? = null
    ): DongruanRocResponse<FeeDetailInfo>

    /**
     * 【RC1.1.7S007】查询住院患者列表（原始接口）
     * @param cardNo 就诊卡号，可选
     * @param deptCode 科室编码，可选
     * @param nurseCellCode 护理单元编码，可选
     * @param idenno 身份证号，可选
     * @param tel 电话号码，可选
     * @param patientNo 住院号，可选
     * @param patientId emr患者ID，可选
     * @param patientName 患者姓名，可选
     * @param inDate 入院时间 YYYY-MM-DD HH24:MI:SS，可选
     * @param patientState 患者在院状态，可选
     * @param outDate 出院时间，可选
     * @param beginTime 筛选时间begin，可选
     * @param endTime 筛选时间end，可选
     * @param pageSize 数据分页条数，可选
     * @param pageNum 分页页码，可选
     * @param pageFlag 分页flag，可选
     * @param outBeginDate 开始时间（出院），可选
     * @param outEndDate 结束时间（出院），可选
     * @return 住院患者列表信息响应
     */
    @GET("/roc/curr-web/api/v1/common/in-patient/query")
    suspend fun queryInPatientListRaw(
        @Query("cardNo") cardNo: String? = null,
        @Query("deptCode") deptCode: String? = null,
        @Query("nurseCellCode") nurseCellCode: String? = null,
        @Query("idenno") idenno: String? = null,
        @Query("tel") tel: String? = null,
        @Query("patientNo") patientNo: String? = null,
        @Query("patientId") patientId: String? = null,
        @Query("patientName") patientName: String? = null,
        @Query("inDate") inDate: String? = null,
        @Query("patientState") patientState: String? = null,
        @Query("outDate") outDate: String? = null,
        @Query("beginTime") beginTime: String? = null,
        @Query("endTime") endTime: String? = null,
        @Query("pageSize") pageSize: Int? = null,
        @Query("pageNum") pageNum: Int? = null,
        @Query("pageFlag") pageFlag: String? = null,
        @Query("outBeginDate") outBeginDate: String? = null,
        @Query("outEndDate") outEndDate: String? = null
    ): DongruanRocResponse<InPatientListResponse>

    /**
     * 【RC1.1.8S008】获取住院患者详细信息（原始接口）
     * @param inpatientNo 患者编号
     * @return 住院患者详细信息响应
     */
    @GET("/roc/curr-web/api/v1/common/in-patient/get/{inpatientNo}")
    suspend fun getInPatientDetailInfoRaw(
        @Path("inpatientNo") inpatientNo: String
    ): DongruanRocResponse<InPatientDetailInfo>

}
