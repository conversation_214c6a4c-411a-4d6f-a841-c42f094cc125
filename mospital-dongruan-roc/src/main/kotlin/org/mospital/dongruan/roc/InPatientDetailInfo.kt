package org.mospital.dongruan.roc

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.LocalDateTime

/**
 * 住院患者详细信息
 * 对应接口：【RC1.1.8S008】获取住院患者信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class InPatientDetailInfo(
    /**
     * 过敏标志
     */
    val anaphyFlag: String? = null,

    /**
     * 床号
     */
    val bedNo: String? = null,

    /**
     * 出生地名称
     */
    val birthArea: String? = null,

    /**
     * 生日
     */
    val birthDate: LocalDateTime? = null,

    /**
     * 血型编码
     */
    val bloodCode: String? = null,

    /**
     * 血压
     */
    val bloodDress: String? = null,

    /**
     * 患者CARDNO
     */
    val cardNo: String? = null,

    /**
     * 医师代码(主治)
     */
    val chargeDocCode: String? = null,

    /**
     * 医师姓名(主治)
     */
    val chargeDocName: String? = null,

    /**
     * 医师代码(主任)
     */
    val chiefDocCode: String? = null,

    /**
     * 医师姓名(主任)
     */
    val chiefDocName: String? = null,

    /**
     * 国籍
     */
    val counCode: String? = null,

    /**
     * 病情标志
     */
    val criticalFlag: String? = null,

    /**
     * 科室代码
     */
    val deptCode: String? = null,

    /**
     * 科室名称
     */
    val deptName: String? = null,

    /**
     * 籍贯
     */
    val dist: String? = null,

    /**
     * 护士代码(责任)
     */
    val dutyNurseCode: String? = null,

    /**
     * 护士姓名(责任)
     */
    val dutyNurseName: String? = null,

    /**
     * 余额(未结)
     */
    val freeCost: String? = null,

    /**
     * 性别
     */
    val genderCode: String? = null,

    /**
     * 身高
     */
    val height: String? = null,

    /**
     * 重大疾病标志
     */
    val hepatitisFlag: String? = null,

    /**
     * 患者住院流水号
     */
    val hisPatientNo: String? = null,

    /**
     * 家庭地址
     */
    val homeAdd: String? = null,

    /**
     * 家庭电话
     */
    val homeTel: String? = null,

    /**
     * 家庭邮编
     */
    val homeZip: String? = null,

    /**
     * 医师代码(住院)
     */
    val houseDocCode: String? = null,

    /**
     * 医师姓名(住院)
     */
    val houseDocName: String? = null,

    /**
     * 身份证号
     */
    val idenno: String? = null,

    /**
     * 入院途径
     */
    val inAvenue: String? = null,

    /**
     * 入院情况
     */
    val inCircs: String? = null,

    /**
     * 入院日期
     */
    val inDate: LocalDateTime? = null,

    /**
     * 入科时间（可能存在于部分实现中）
     */
    val inDeptDate: LocalDateTime? = null,

    /**
     * 入院来源
     */
    val inSource: String? = null,

    /**
     * 在院状态
     */
    val inState: String? = null,

    /**
     * 入院次数
     */
    val inTimes: String? = null,

    /**
     * 是否新生儿
     */
    val isNewBorn: String? = null,

    /**
     * 联系人地址
     */
    val linkmanAdd: String? = null,

    /**
     * 联系人姓名
     */
    val linkmanName: String? = null,

    /**
     * 联系人电话
     */
    val linkmanTel: String? = null,

    /**
     * 主诊断
     */
    val mainDiagnose: String? = null,

    /**
     * 婚姻状况
     */
    val mari: String? = null,

    /**
     * 医疗证号
     */
    val mcardNo: String? = null,

    /**
     * 患者合同类型
     */
    val medicalType: String? = null,

    /**
     * 姓名
     */
    val name: String? = null,

    /**
     * 民族
     */
    val nationCode: String? = null,

    /**
     * 护理单元代码
     */
    val nurseCellCode: String? = null,

    /**
     * 护理单元名称
     */
    val nurseCellName: String? = null,

    /**
     * 出院日期
     */
    val outDate: LocalDateTime? = null,

    /**
     * 合同代码
     */
    val pactCode: String? = null,

    /**
     * 合同单位名称
     */
    val pactName: String? = null,

    /**
     * EMR患者ID
     */
    val patientId: String? = null,

    /**
     * 患者住院号
     */
    val patientNo: String? = null,

    /**
     * 结算类别
     */
    val paykindCode: String? = null,

    /**
     * 预交金
     */
    val prepayCost: String? = null,

    /**
     * 职业代码
     */
    val profCode: String? = null,

    /**
     * 职业名称
     */
    val profName: String? = null,

    /**
     * 联系人编码关系
     */
    val relaCode: String? = null,

    /**
     * 联系人关系
     */
    val relaName: String? = null,

    /**
     * 患者姓名拼音码
     */
    val spellCode: String? = null,

    /**
     * 护理级别
     */
    val tend: String? = null,

    /**
     * 费用汇总
     */
    val totCost: String? = null,

    /**
     * 体重
     */
    val weight: String? = null,

    /**
     * 单位名称
     */
    val workName: String? = null,

    /**
     * 单位电话
     */
    val workTel: String? = null,

    /**
     * 单位邮编
     */
    val workZip: String? = null,

    /**
     * 转归标记
     */
    val zg: String? = null
)
