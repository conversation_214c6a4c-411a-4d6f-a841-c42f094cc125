package org.mospital.dongruan.roc

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 门诊费用明细信息
 * 对应接口：【RF2.1.1S125】查询门诊患者费用明细列表
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class FeeDetailInfo(
    /**
     * 科室编码
     */
    val deptCode: String?,

    /**
     * 科室名称
     */
    val deptName: String?,

    /**
     * 科室地址
     */
    @field:JsonAlias("deptAddress", "deptAdress")
    val deptAddress: String?,

    /**
     * 看诊时间
     */
    val regDate: LocalDateTime?,

    /**
     * 优惠费用
     */
    val payCost: BigDecimal?,

    /**
     * 报销费用
     */
    val pubCost: BigDecimal?,

    /**
     * 自费费用
     */
    val ownCost: BigDecimal?,

    /**
     * 费用明细列表
     */
    val feeList: List<FeeItem>?
)

/**
 * 费用明细项目
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class FeeItem(
    /**
     * 处方号
     */
    val recipeNo: String?,

    /**
     * 处方内项目流水号
     */
    val sequenceNo: Int?,

    /**
     * 医嘱订单号
     */
    val moOrder: String?,

    /**
     * 交易类型,1正交易，2反交易
     */
    val transType: String?,

    /**
     * 项目编码
     */
    val itemCode: String?,

    /**
     * 项目名称
     */
    val itemName: String?,

    /**
     * 数量
     */
    val qty: BigDecimal?,

    /**
     * 单价
     */
    val singlePrice: BigDecimal?,

    /**
     * 优惠费用
     */
    val payCost: BigDecimal?,

    /**
     * 报销费用
     */
    val pubCost: BigDecimal?,

    /**
     * 自费费用
     */
    val ownCost: BigDecimal?,

    /**
     * 单位
     */
    val unit: String?,

    /**
     * 项目状态
     */
    val state: String?,

    /**
     * 执行标记
     */
    val confirmFlag: String?,

    /**
     * 缴费标记
     */
    val payFlag: String?,

    /**
     * 取消标记
     */
    @field:JsonAlias("cancelFlag", "cancleFlag")
    val cancelFlag: String?,

    /**
     * 缴费时间
     */
    val feeDate: LocalDateTime?,

    /**
     * 频次编码
     */
    val frequencyCode: String?,

    /**
     * 频次名称
     */
    val frequencyName: String?,

    /**
     * 用法编码
     */
    val usageCode: String?,

    /**
     * 用法名称
     */
    val usageName: String?,

    /**
     * 规格
     */
    @field:JsonAlias("specs", "spesc")
    val specs: String?,

    /**
     * 医生编码
     */
    val docCode: String?,

    /**
     * 医生名称
     */
    val docName: String?,

    /**
     * 药品标记
     */
    val drugFlag: String?,

    /**
     * 收费序列
     */
    val recipeSeq: String?,

    /**
     * 可退数量
     */
    val noBackNum: BigDecimal?,

    /**
     * 复合项目代码
     */
    val packageCode: String?,

    /**
     * 组合号
     */
    val combNo: String?,

    /**
     * 执行科室编码
     */
    val execDpcd: String?,

    /**
     * 执行科室名称
     */
    val execDpnm: String?,

    /**
     * 执行科室地址
     */
    @field:JsonAlias("execDeptAddress", "execDeptadss")
    val execDeptAddress: String?,

    /**
     * 合同单位编码
     */
    val pactCode: String?,

    /**
     * 合同单位名称
     */
    val pactName: String?,

    /**
     * 项目分类
     */
    val classCode: String?,

    /**
     * 医院编码
     */
    val hospitalCode: String?,

    /**
     * 院区编码
     */
    val branchCode: String?,

    /**
     * 发票号
     */
    val invoiceNo: String?,

    /**
     * 序号
     */
    val serialNum: Int?,

    /**
     * 最小费用编码
     */
    val feeCode: String?,

    /**
     * 最小费用名称
     */
    val feeName: String?,

    /**
     * 处方开立时间
     */
    val recipeDate: LocalDateTime?,

    /**
     * 每次用量
     */
    val doseOnce: BigDecimal?,

    /**
     * 每次用量单位
     */
    val doseUnit: String?,

    /**
     * 基本剂量
     */
    val baseDose: String?,

    /**
     * 科室编码
     */
    val deptCode: String?,

    /**
     * 科室名称
     */
    val deptName: String?,

    /**
     * 看诊时间
     */
    val regDate: LocalDateTime?,

    /**
     * 看诊序号
     */
    val seeNo: String?,

    /**
     * 挂号流水号
     */
    val clinicCode: String?,

    /**
     * 发药窗口名称
     */
    val sendTerminalName: String?,

    /**
     * 支付方式
     */
    val payChannel: String?,

    /**
     * 支付方式名称
     */
    val payChannelName: String?,

    /**
     * 慢病编码
     */
    val diseCode: String?,

    /**
     * 慢病诊断
     */
    val diseName: String?
) {
    /**
     * 判断是否为药品
     */
    val isDrug: Boolean
        get() = drugFlag == "1"

    /**
     * 判断是否已缴费
     */
    val isPaid: Boolean
        get() = payFlag == "1"

    /**
     * 判断是否已确认执行
     */
    val isConfirmed: Boolean
        get() = confirmFlag == "1"

    /**
     * 判断是否已取消
     */
    val isCanceled: Boolean
        get() = cancelFlag == "1"

    /**
     * 计算总费用
     */
    val totalCost: BigDecimal?
        get() = if (payCost != null && pubCost != null && ownCost != null) {
            payCost + pubCost + ownCost
        } else {
            null
        }
}