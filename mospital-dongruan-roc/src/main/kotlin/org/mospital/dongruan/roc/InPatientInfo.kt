package org.mospital.dongruan.roc

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 住院患者信息
 * 对应接口：【RC1.1.7S007】查询住院患者列表
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class InPatientInfo(
    /**
     * 年龄
     */
    val age: String? = null,

    /**
     * 是否药物过敏
     */
    val anaphyFlag: String? = null,

    /**
     * 费用金额(已结)
     */
    val balanceCost: BigDecimal? = null,

    /**
     * 预交金额(已结)
     */
    val balancePrepay: BigDecimal? = null,

    /**
     * 床号
     */
    val bedNo: String? = null,

    /**
     * 出生日期
     */
    val birthday: LocalDateTime? = null,

    /**
     * 院区编码
     */
    val branchCode: String? = null,

    /**
     * 患者ID
     */
    val cardNo: String? = null,

    /**
     * 主诉
     */
    val chiefComplaint: String? = null,

    /**
     * 医师代码(主任)
     */
    val chiefDocCode: String? = null,

    /**
     * 医师姓名(主任)
     */
    val chiefDocName: String? = null,

    /**
     * 危重情况
     */
    val criticalFlag: String? = null,

    /**
     * 科室代码
     */
    val deptCode: String? = null,

    /**
     * 科室
     */
    val deptName: String? = null,

    /**
     * 诊断
     */
    val diagnose: String? = null,

    /**
     * 诊断2
     */
    val diagnose2: String? = null,

    /**
     * 主治医生编码
     */
    val directorDocCode: String? = null,

    /**
     * 主治医生姓名
     */
    val directorDocName: String? = null,

    /**
     * 责任护士编码
     */
    val dutyNurseCode: String? = null,

    /**
     * 责任护士姓名
     */
    val dutyNurseName: String? = null,

    /**
     * 余额
     */
    val freeCost: String? = null,

    /**
     * 身高
     */
    val height: String? = null,

    /**
     * 电话号码
     */
    val homeTel: String? = null,

    /**
     * 身份证号
     */
    val idenno: String? = null,

    /**
     * 入院年龄
     */
    val inAge: String? = null,

    /**
     * 患者来源
     */
    val inAvenuei: String? = null,

    /**
     * 入院时间
     */
    val inDate: LocalDateTime? = null,

    /**
     * 在院天数
     */
    val inDays: String? = null,

    /**
     * 入院科室编码
     */
    val inDeptCode: String? = null,

    /**
     * 入院科室名称
     */
    val inDeptName: String? = null,

    /**
     * 住院次数
     */
    val inTimes: String? = null,

    /**
     * 住院流水号
     */
    val inpatientNo: String? = null,

    /**
     * 是否新生儿
     */
    val isNewBorn: String? = null,

    /**
     * 联系人地址
     */
    val linkmanAdd: String? = null,

    /**
     * 联系人姓名
     */
    val linkmanName: String? = null,

    /**
     * 联系人电话
     */
    val linkmanTel: String? = null,

    /**
     * 主诊断
     */
    val mainDiagnose: String? = null,

    /**
     * 病区编号
     */
    val nurseCellCode: String? = null,

    /**
     * 病区名称
     */
    val nurseCellName: String? = null,

    /**
     * 护理级别
     */
    val nursingLevel: String? = null,

    /**
     * 出院日期
     */
    val outDate: LocalDateTime? = null,

    /**
     * 出院科室编码
     */
    val outDeptCode: String? = null,

    /**
     * 出院科室名称
     */
    val outDeptName: String? = null,

    /**
     * 合同单位
     */
    val pactCode: String? = null,

    /**
     * 合同单位名称
     */
    val pactName: String? = null,

    /**
     * 患者ID
     */
    val patientId: String? = null,

    /**
     * 患者姓名
     */
    val patientName: String? = null,

    /**
     * 住院号
     */
    val patientNo: String? = null,

    /**
     * 患者状态
     */
    val patientState: String? = null,

    /**
     * 费用类别
     */
    val paykindCode: String? = null,

    /**
     * 费用类别名称
     */
    val paykindName: String? = null,

    /**
     * 预交金
     */
    val prepayCost: String? = null,

    /**
     * 现病史
     */
    val presentIllness: String? = null,

    /**
     * 既往史
     */
    val previousHistory: String? = null,

    /**
     * 经治医生编码
     */
    val residencyDocCode: String? = null,

    /**
     * 经治医生姓名
     */
    val residencyDocName: String? = null,

    /**
     * 入院诊断
     */
    val ryDiagnose: String? = null,

    /**
     * 性别
     */
    val sexCode: String? = null,

    /**
     * 总费用
     */
    val totCost: String? = null,

    /**
     * 体重
     */
    val weight: String? = null
) 