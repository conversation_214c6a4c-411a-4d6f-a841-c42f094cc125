package org.mospital.dongruan.roc

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * 住院患者列表响应
 * 对应接口：【RC1.1.7S007】查询住院患者列表
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class InPatientListResponse(
    /**
     * 结束行
     */
    val endRow: Int? = null,

    /**
     * 是否有下一页
     */
    val hasNextPage: Boolean? = null,

    /**
     * 是否有上一页
     */
    val hasPreviousPage: Boolean? = null,

    /**
     * 是否第一页
     */
    val isFirstPage: Boolean? = null,

    /**
     * 是否最后一页
     */
    val isLastPage: Boolean? = null,

    /**
     * 患者列表
     */
    val list: List<InPatientInfo>? = null,

    /**
     * 导航第一页
     */
    val navigateFirstPage: Int? = null,

    /**
     * 导航最后一页
     */
    val navigateLastPage: Int? = null,

    /**
     * 导航页数
     */
    val navigatePages: Int? = null,

    /**
     * 导航页码数组
     */
    @field:JsonAlias("navigatepageNums")
    val navigatePageNums: List<Int>? = null,

    /**
     * 下一页
     */
    val nextPage: Int? = null,

    /**
     * 当前页码
     */
    val pageNum: Int? = null,

    /**
     * 每页大小
     */
    val pageSize: Int? = null,

    /**
     * 总页数
     */
    val pages: Int? = null,

    /**
     * 上一页
     */
    val prePage: Int? = null,

    /**
     * 当前页大小
     */
    val size: Int? = null,

    /**
     * 开始行
     */
    val startRow: Int? = null,

    /**
     * 总记录数
     */
    val total: Long? = null
) 