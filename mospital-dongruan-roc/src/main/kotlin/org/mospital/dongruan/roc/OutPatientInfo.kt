package org.mospital.dongruan.roc

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 门诊患者信息
 * 对应接口：【RC1.1.6S006】获取门诊患者信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class OutPatientInfo(
    /**
     * 帐户总额
     */
    val actAmt: BigDecimal?,

    /**
     * 帐户密码
     */
    val actCode: String?,

    /**
     * 药物过敏
     */
    val anaphyFlag: String?,

    /**
     * 区
     */
    val area: String?,

    /**
     * 出生地区
     */
    val area1: String?,

    /**
     * 户口地址区
     */
    val area2: String?,

    /**
     * 籍贯区
     */
    val area3: String?,

    /**
     * 出生地
     */
    val areaCode: String?,

    /**
     * 欠费金额
     */
    val arrearSum: BigDecimal?,

    /**
     * 欠费次数
     */
    val arrearTimes: Int?,

    /**
     * 出生日期
     */
    val birthday: LocalDate?,

    /**
     * 血型
     */
    val bloodCode: String?,

    /**
     * 患者唯一ID
     */
    val cardNo: String?,

    /**
     * 病案号
     */
    val caseNo: String?,

    /**
     * 城市
     */
    val city: String?,

    /**
     * 出生地城市
     */
    val city1: String?,

    /**
     * 户口地址城市
     */
    val city2: String?,

    /**
     * 籍贯城市
     */
    val city3: String?,

    /**
     * 国籍
     */
    val counCode: String?,

    /**
     * 违约次数
     */
    val disobyCnt: BigDecimal?,

    /**
     * 籍贯
     */
    val district: String?,

    /**
     * email地址
     */
    val email: String?,

    /**
     * 员工编码
     */
    val emplCode: String?,

    /**
     * emr患者号
     */
    val emrPatid: Int?,

    /**
     * 结束日期
     */
    val endDate: LocalDateTime?,

    /**
     * 算法类别
     */
    val feeKind: String?,

    /**
     * 初诊日期
     */
    val firSeeDate: LocalDateTime?,

    /**
     * 医疗费用
     */
    val framt: BigDecimal?,

    /**
     * 重要疾病
     */
    val hepatitisFlag: String?,

    /**
     * 户口或家庭所在
     */
    val home: String?,

    /**
     * 家庭住址门牌号
     */
    val homeDoorNo: String?,

    /**
     * 家庭电话
     */
    val homeTel: String?,

    /**
     * 户口或家庭邮政编码
     */
    val homeZip: String?,

    /**
     * 电脑号
     */
    val icCardno: String?,

    /**
     * 证件类型
     */
    val idcardtype: String?,

    /**
     * 身份证号
     */
    val idenno: String?,

    /**
     * 住院来源
     */
    val inhosSource: String?,

    /**
     * 住院次数（备注字段显示为"民族"，可能是数据错误）
     */
    val inhosTimes: Int?,

    /**
     * 保险公司编码
     */
    val insuranceId: String?,

    /**
     * 保险公司名称
     */
    val insuranceName: String?,

    /**
     * 是否加密姓名
     */
    val isEncryptname: String?,

    /**
     * 是否急诊
     */
    val isTreatment: String?,

    /**
     * 是否有效
     */
    val isValid: String?,

    /**
     * 上期帐户余额
     */
    val lactSum: BigDecimal?,

    /**
     * 上期银行余额
     */
    val lbankSum: BigDecimal?,

    /**
     * 最近住院日期
     */
    val lihosDate: LocalDateTime?,

    /**
     * 联系人住址
     */
    val linkmanAdd: String?,

    /**
     * 联系人地址门牌号
     */
    val linkmanDoorNo: String?,

    /**
     * 联系人姓名
     */
    val linkmanName: String?,

    /**
     * 联系人电话
     */
    val linkmanTel: String?,

    /**
     * 最近出院日期
     */
    val louthosDate: LocalDateTime?,

    /**
     * 最近挂号日期
     */
    val lregDate: LocalDateTime?,

    /**
     * 婚姻状况
     */
    val mari: String?,

    /**
     * 备注
     */
    val mark: String?,

    /**
     * 医疗证号
     */
    val mcardNo: String?,

    /**
     * 母亲姓名
     */
    val montherName: String?,

    /**
     * 姓名
     */
    val name: String?,

    /**
     * 民族
     */
    val nationCode: String?,

    /**
     * 密文
     */
    val normalname: String?,

    /**
     * 旧卡号
     */
    val oldCardno: String?,

    /**
     * 操作员
     */
    val operCode: String?,

    /**
     * 操作日期
     */
    val operDate: LocalDateTime?,

    /**
     * 合同代码
     */
    val pactCode: String?,

    /**
     * 合同单位名称
     */
    val pactName: String?,

    /**
     * 结算类别
     */
    val paykindCode: String?,

    /**
     * 结算类别名称
     */
    val paykindName: String?,

    /**
     * 职业
     */
    val profCode: String?,

    /**
     * 职业名称
     */
    val profName: String?,

    /**
     * 省份
     */
    val province: String?,

    /**
     * 出生地省份
     */
    val province1: String?,

    /**
     * 户口地址省份
     */
    val province2: String?,

    /**
     * 籍贯省份
     */
    val province3: String?,

    /**
     * 联系人关系
     */
    val relaCode: String?,

    /**
     * 街道
     */
    val road: String?,

    /**
     * 户口地址街道
     */
    val road1: String?,

    /**
     * 性别
     */
    val sexCode: String?,

    /**
     * 拼音码
     */
    val spellCode: String?,

    /**
     * 是否Vip
     */
    val vipFlag: String?,

    /**
     * 五笔
     */
    val wbCode: String?,

    /**
     * 工作单位
     */
    val workHome: String?,

    /**
     * 单位电话
     */
    val workTel: String?,

    /**
     * 单位邮编
     */
    val workZip: String?
) {
    /**
     * 获取性别描述
     */
    val sexDescription: String
        get() = when (sexCode) {
            "M" -> "男"
            "F" -> "女"
            else -> "未知"
        }

    /**
     * 判断是否为VIP患者
     */
    val isVip: Boolean
        get() = vipFlag == "1"

    /**
     * 判断是否有效
     */
    val isValidPatient: Boolean
        get() = isValid == "1"

    /**
     * 判断是否有药物过敏史
     */
    val hasAllergy: Boolean
        get() = anaphyFlag == "1"

    /**
     * 判断是否有重大疾病
     */
    val hasSeriousDisease: Boolean
        get() = hepatitisFlag == "1"

    /**
     * 判断是否急诊患者
     */
    val isEmergency: Boolean
        get() = isTreatment == "1"
}