# 东软ROC系统集成平台

## 功能特性

- 药品物价项目查询
- 非药品物价项目查询
- 可挂号科室查询
- 科室医生排班查询
- 排班号位明细查询
- 门诊患者挂号记录查询
- 号位占号/取消占号
- 挂号订单信息查询
- 门诊患者信息查询
- 门诊患者费用明细查询
- **住院患者列表查询** (新增)
- **住院患者详细信息查询** (新增)

## 快速开始

### 查询住院患者列表

```kotlin
import org.mospital.dongruan.roc.DongruanRocService

// 查询指定住院号的患者
val result = DongruanRocService.queryInPatientList(
    patientNo = "**********"
)

if (result.isSuccess) {
    val response = result.getOrNull()
    println("总记录数: ${response?.total}")
    response?.list?.forEach { patient ->
        println("患者姓名: ${patient.patientName}")
        println("住院号: ${patient.patientNo}")
        println("床号: ${patient.bedNo}")
        println("科室: ${patient.deptName}")
        println("主治医生: ${patient.directorDocName}")
        println("责任护士: ${patient.dutyNurseName}")
        println("入院时间: ${patient.inDate}")
        println("在院天数: ${patient.inDays}")
        println("护理级别: ${patient.nursingLevel}")
        println("主诊断: ${patient.mainDiagnose}")
        println("预交金: ${patient.prepayCost}")
        println("总费用: ${patient.totCost}")
        println("余额: ${patient.freeCost}")
        println("---")
    }
} else {
    println("查询失败: ${result.exceptionOrNull()?.message}")
}

// 多条件查询
val result2 = DongruanRocService.queryInPatientList(
    cardNo = "**********",
    deptCode = "0182",
    nurseCellCode = "8037",
    patientName = "毛力旦·地里夏提",
    pageSize = 10,
    pageNum = 1
)

// 按时间范围查询
val result3 = DongruanRocService.queryInPatientList(
    beginTime = "2025-08-01 00:00:00",
    endTime = "2025-08-05 23:59:59",
    patientState = "I" // 在院状态
)
```

### 获取住院患者详细信息

```kotlin
import org.mospital.dongruan.roc.DongruanRocService

// 根据患者编号获取详细信息
val result = DongruanRocService.getInPatientDetailInfo("ZY001**********")

if (result.isSuccess) {
    val patientInfo = result.getOrNull()
    patientInfo?.let { info ->
        println("患者基本信息：")
        println("- 患者ID: ${info.patientId}")
        println("- 住院流水号: ${info.hisPatientNo}")
        println("- 住院号: ${info.patientNo}")
        println("- 卡号: ${info.cardNo}")
        println("- 姓名: ${info.name}")
        println("- 性别: ${info.genderCode}")
        println("- 身份证号: ${info.idenno}")
        println("- 生日: ${info.birthDate}")
        println("- 民族: ${info.nationCode}")
        println("- 联系电话: ${info.homeTel}")
        println("- 家庭地址: ${info.homeAdd}")
        println("- 联系人: ${info.linkmanName}")
        println("- 联系人电话: ${info.linkmanTel}")
        println("- 联系人关系: ${info.relaName}")
        
        println("\n住院信息：")
        println("- 科室: ${info.deptName} (${info.deptCode})")
        println("- 床号: ${info.bedNo}")
        println("- 护理单元: ${info.nurseCellName} (${info.nurseCellCode})")
        println("- 入院时间: ${info.inDate}")
        println("- 入院次数: ${info.inTimes}")
        println("- 在院状态: ${info.inState}")
        println("- 主诊断: ${info.mainDiagnose}")
        
        println("\n医护信息：")
        println("- 住院医师: ${info.houseDocName} (${info.houseDocCode})")
        println("- 主治医师: ${info.chargeDocName} (${info.chargeDocCode})")
        println("- 主任医师: ${info.chiefDocName} (${info.chiefDocCode})")
        println("- 责任护士: ${info.dutyNurseName} (${info.dutyNurseCode})")
        
        println("\n费用信息：")
        println("- 合同单位: ${info.pactName} (${info.pactCode})")
        println("- 结算类别: ${info.paykindCode}")
        println("- 预交金: ${info.prepayCost}")
        println("- 总费用: ${info.totCost}")
        println("- 余额: ${info.freeCost}")
        
        println("\n健康信息：")
        println("- 身高: ${info.height}")
        println("- 体重: ${info.weight}")
        println("- 血型: ${info.bloodCode}")
        println("- 血压: ${info.bloodDress}")
        println("- 过敏标志: ${info.anaphyFlag}")
        println("- 重大疾病标志: ${info.hepatitisFlag}")
        println("- 病情标志: ${info.criticalFlag}")
        println("- 护理级别: ${info.tend}")
    }
} else {
    println("查询失败: ${result.exceptionOrNull()?.message}")
}
```

### 接口参数说明

#### 查询住院患者列表参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cardNo | String | 否 | 就诊卡号 |
| deptCode | String | 否 | 科室编码 |
| nurseCellCode | String | 否 | 护理单元编码 |
| idenno | String | 否 | 身份证号 |
| tel | String | 否 | 电话号码 |
| patientNo | String | 否 | 住院号 |
| patientId | String | 否 | emr患者ID |
| patientName | String | 否 | 患者姓名 |
| inDate | String | 否 | 入院时间 YYYY-MM-DD HH24:MI:SS |
| patientState | String | 否 | 患者在院状态 |
| outDate | String | 否 | 出院时间 |
| beginTime | String | 否 | 筛选时间begin |
| endTime | String | 否 | 筛选时间end |
| pageSize | Int | 否 | 数据分页条数 |
| pageNum | Int | 否 | 分页页码 |
| pageFlag | String | 否 | 分页flag |
| outBeginDate | String | 否 | 开始时间（出院） |
| outEndDate | String | 否 | 结束时间（出院） |

#### 获取住院患者详细信息参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| inpatientNo | String | 是 | 患者编号（住院流水号） |

### 返回数据结构

#### 住院患者列表响应结构

```kotlin
data class InPatientListResponse(
    val total: Long?,                    // 总记录数
    val list: List<InPatientInfo>?,      // 患者列表
    val pageNum: Int?,                   // 当前页码
    val pageSize: Int?,                  // 每页大小
    val size: Int?,                      // 当前页大小
    val startRow: Int?,                  // 开始行
    val endRow: Int?,                    // 结束行
    val pages: Int?,                     // 总页数
    val prePage: Int?,                   // 上一页
    val nextPage: Int?,                  // 下一页
    val isFirstPage: Boolean?,           // 是否第一页
    val isLastPage: Boolean?,            // 是否最后一页
    val hasPreviousPage: Boolean?,       // 是否有上一页
    val hasNextPage: Boolean?,           // 是否有下一页
    val navigatePages: Int?,             // 导航页数
    val navigatepageNums: List<Int>?,    // 导航页码数组
    val navigateFirstPage: Int?,         // 导航第一页
    val navigateLastPage: Int?           // 导航最后一页
)
```

#### 住院患者详细信息结构

```kotlin
data class InPatientDetailInfo(
    // 患者基本信息
    val patientId: String?,              // EMR患者ID
    val hisPatientNo: String?,           // 患者住院流水号
    val patientNo: String?,              // 患者住院号
    val cardNo: String?,                 // 患者CARDNO
    val name: String?,                   // 姓名
    val genderCode: String?,             // 性别
    val idenno: String?,                 // 身份证号
    val birthDate: LocalDateTime?,       // 生日
    val spellCode: String?,              // 患者姓名拼音码
    val nationCode: String?,             // 民族
    val counCode: String?,               // 国籍
    val dist: String?,                   // 籍贯
    val birthArea: String?,              // 出生地名称
    
    // 联系信息
    val homeTel: String?,                // 家庭电话
    val homeAdd: String?,                // 家庭地址
    val homeZip: String?,                // 家庭邮编
    val linkmanName: String?,            // 联系人姓名
    val linkmanTel: String?,             // 联系人电话
    val linkmanAdd: String?,             // 联系人地址
    val relaCode: String?,               // 联系人编码关系
    val relaName: String?,               // 联系人关系
    
    // 工作信息
    val profCode: String?,               // 职业代码
    val profName: String?,               // 职业名称
    val workName: String?,               // 单位名称
    val workTel: String?,                // 单位电话
    val workZip: String?,                // 单位邮编
    val mari: String?,                   // 婚姻状况
    
    // 住院信息
    val deptCode: String?,               // 科室代码
    val deptName: String?,               // 科室名称
    val bedNo: String?,                  // 床号
    val nurseCellCode: String?,          // 护理单元代码
    val nurseCellName: String?,          // 护理单元名称
    val inDate: LocalDateTime?,          // 入院日期
    val outDate: LocalDateTime?,         // 出院日期
    val inTimes: String?,                // 入院次数
    val inState: String?,                // 在院状态
    val inAvenue: String?,               // 入院途径
    val inCircs: String?,                // 入院情况
    val inSource: String?,               // 入院来源
    val isNewBorn: String?,              // 是否新生儿
    val zg: String?,                     // 转归标记
    
    // 医护信息
    val houseDocCode: String?,           // 医师代码(住院)
    val houseDocName: String?,           // 医师姓名(住院)
    val chargeDocCode: String?,          // 医师代码(主治)
    val chargeDocName: String?,          // 医师姓名(主治)
    val chiefDocCode: String?,           // 医师代码(主任)
    val chiefDocName: String?,           // 医师姓名(主任)
    val dutyNurseCode: String?,          // 护士代码(责任)
    val dutyNurseName: String?,          // 护士姓名(责任)
    
    // 诊断信息
    val mainDiagnose: String?,           // 主诊断
    val tend: String?,                   // 护理级别
    val criticalFlag: String?,           // 病情标志
    
    // 费用信息
    val pactCode: String?,               // 合同代码
    val pactName: String?,               // 合同单位名称
    val paykindCode: String?,            // 结算类别
    val medicalType: String?,            // 患者合同类型
    val mcardNo: String?,                // 医疗证号
    val prepayCost: String?,             // 预交金
    val totCost: String?,                // 费用汇总
    val freeCost: String?,               // 余额(未结)
    
    // 健康信息
    val height: String?,                 // 身高
    val weight: String?,                 // 体重
    val bloodCode: String?,              // 血型编码
    val bloodDress: String?,             // 血压
    val anaphyFlag: String?,             // 过敏标志
    val hepatitisFlag: String?,          // 重大疾病标志
    
    // 扩展信息
    val inDeptDate: LocalDateTime?       // 入科时间
)
```

### 患者信息数据结构

```kotlin
data class InPatientInfo(
    val age: String?,                    // 年龄
    val anaphyFlag: String?,             // 是否药物过敏
    val balanceCost: BigDecimal?,        // 费用金额(已结)
    val balancePrepay: BigDecimal?,      // 预交金额(已结)
    val bedNo: String?,                  // 床号
    val birthday: LocalDateTime?,        // 出生日期
    val branchCode: String?,             // 院区编码
    val cardNo: String?,                 // 患者ID
    val chiefComplaint: String?,         // 主诉
    val chiefDocCode: String?,           // 医师代码(主任)
    val chiefDocName: String?,           // 医师姓名(主任)
    val criticalFlag: String?,           // 危重情况
    val deptCode: String?,               // 科室代码
    val deptName: String?,               // 科室
    val diagnose: String?,               // 诊断
    val diagnose2: String?,              // 诊断2
    val directorDocCode: String?,        // 主治医生编码
    val directorDocName: String?,        // 主治医生姓名
    val dutyNurseCode: String?,          // 责任护士编码
    val dutyNurseName: String?,          // 责任护士姓名
    val freeCost: String?,               // 余额
    val height: String?,                 // 身高
    val homeTel: String?,                // 电话号码
    val idenno: String?,                 // 身份证号
    val inAge: String?,                  // 入院年龄
    val inAvenuei: String?,              // 患者来源
    val inDate: LocalDateTime?,          // 入院时间
    val inDays: String?,                 // 在院天数
    val inDeptCode: String?,             // 入院科室编码
    val inDeptName: String?,             // 入院科室名称
    val inTimes: String?,                // 住院次数
    val inpatientNo: String?,            // 住院流水号
    val isNewBorn: String?,              // 是否新生儿
    val linkmanAdd: String?,             // 联系人地址
    val linkmanName: String?,            // 联系人姓名
    val linkmanTel: String?,             // 联系人电话
    val mainDiagnose: String?,           // 主诊断
    val nurseCellCode: String?,          // 病区编号
    val nurseCellName: String?,          // 病区名称
    val nursingLevel: String?,           // 护理级别
    val outDate: LocalDateTime?,         // 出院日期
    val outDeptCode: String?,            // 出院科室编码
    val outDeptName: String?,            // 出院科室名称
    val pactCode: String?,               // 合同单位
    val pactName: String?,               // 合同单位名称
    val patientId: String?,              // 患者ID
    val patientName: String?,            // 患者姓名
    val patientNo: String?,              // 住院号
    val patientState: String?,           // 患者状态
    val paykindCode: String?,            // 费用类别
    val paykindName: String?,            // 费用类别名称
    val prepayCost: String?,             // 预交金
    val presentIllness: String?,         // 现病史
    val previousHistory: String?,        // 既往史
    val residencyDocCode: String?,       // 经治医生编码
    val residencyDocName: String?,       // 经治医生姓名
    val ryDiagnose: String?,             // 入院诊断
    val sexCode: String?,                // 性别
    val totCost: String?,                // 总费用
    val weight: String?                  // 体重
)
```

## 其他功能

### 查询药品物价项目列表

```kotlin
val result = DongruanRocService.queryDrugPriceList(
    vagueName = "阿司匹林",
    simpleSpell = "asp"
)
```

### 查询非药品物价项目列表

```kotlin
val result = DongruanRocService.queryUndrugPriceList(
    vagueName = "CT检查",
    simpleSpell = "ct"
)
```

### 查询可挂号科室列表

```kotlin
val result = DongruanRocService.queryAvailableDepts(
    beginDate = LocalDate.now(),
    endDate = LocalDate.now().plusDays(7)
)
```

### 查询科室医生排班列表

```kotlin
val result = DongruanRocService.queryDoctorScheduleList(
    beginDate = LocalDate.now(),
    endDate = LocalDate.now().plusDays(7),
    deptCode = "001"
)
```

### 查询门诊患者挂号记录

```kotlin
val result = DongruanRocService.queryOutpatientRegistrationList(
    beginTime = "2025-08-01 00:00:00",
    endTime = "2025-08-05 23:59:59",
    patientName = "张三"
)
```

### 查询门诊患者费用明细

```kotlin
val result = DongruanRocService.queryFeeDetail(
    clinicCode = "123456789",
    cardNo = "000123456"
)
```

## 配置说明

在使用前需要配置东软ROC系统的连接信息：

```kotlin
// 配置信息通常在配置文件中设置
val config = DongruanRocConfig(
    url = "http://your-roc-server:port",
    domain = "your-domain",
    key = "your-api-key",
    connectTimeout = 30,
    readTimeout = 60
)
```

## 测试

运行测试：

```bash
mvn test
```

## 许可证

本项目采用 MIT 许可证。 