package org.mospital.erhc.guanxin

import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.mospital.common.Ret
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.ConfigLoader
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import kotlin.random.Random

open class Request {

    constructor()

    constructor(method: String) : this() {
        addHeadParam(Param(name = "method", value = method, toSign = false, toEncrypt = false))
    }

    companion object {
        private val config: ErhcGuanxinConfig by lazy {
            ConfigLoader.loadConfig("erhc-guanxin.yaml", ErhcGuanxinConfig::class.java, Request::class.java)
        }
        val url: String get() = config.url
        val key: String get() = config.key
        val appId: String get() = config.appId
        val orgCode: String get() = config.orgCode
        val terminalCode: String get() = config.terminalCode
        val version: String get() = config.version
        val appRecordNo: String get() = config.appRecordNo
        val mediaType = "application/json;charset=UTF-8".toMediaType()
        val log: Logger = LoggerFactory.getLogger(Request::class.java)
        val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
            logger = log,
            connectTimeout = 5_000,
            readTimeout = 10_000,
            useUuidRequestId = false
        )
    }

    data class Param<T>(
        val name: String,
        val value: T,
        val toEncrypt: Boolean = false,
        val toSign: Boolean = true
    ) {
        val encryptedValue: String =
            if (toEncrypt) {
                SecureUtil.encrypt(value.toString())
            } else {
                value.toString()
            }
    }

    private val headParams: MutableMap<String, Param<*>> = mutableMapOf()

    private val bodyParams: MutableMap<String, Param<*>> = mutableMapOf()


    fun <T> addHeadParam(param: Param<T>) {
        headParams[param.name] = param
    }

    fun <T> addBodyParam(param: Param<T>) {
        bodyParams[param.name] = param
    }

    fun removeHeadParam(name: String) {
        headParams.remove(name)
    }

    fun removeBodyParam(name: String) {
        bodyParams.remove(name)
    }

    fun getHeadParam(name: String) = headParams[name]?.value

    fun getBodyParam(name: String) = bodyParams[name]?.value

    private fun signHead() {
        val map = mutableMapOf<String, String>()
        headParams.entries.forEach {
            val param = it.value
            if (param.toSign) {
                map[it.key] = param.encryptedValue
            }
        }

        addHeadParam(Param(name = "headSign", value = SecureUtil.sign(map, key), toSign = false))
    }

    private fun signBody() {
        val map = mutableMapOf<String, String>()
        bodyParams.entries.forEach {
            val param = it.value
            if (param.toSign) {
                map[it.key] = param.encryptedValue
            }
        }

        addHeadParam(Param(name = "bodySign", value = SecureUtil.sign(map, key), toSign = false))
    }

    private fun buildJSON(): String {
        addHeadParam(Param(name = "appId", value = appId, toSign = true, toEncrypt = false))
        addHeadParam(
            Param(
                name = "timestamp",
                value = (System.currentTimeMillis() / 1000).toString(),
                toSign = true,
                toEncrypt = true
            )
        )
        addHeadParam(
            Param(
                name = "nonceStr",
                value = String.format("%012d", Random.nextLong(0L, 1000000000000L)),
                toSign = true,
                toEncrypt = false
            )
        )
        addHeadParam(Param(name = "version", value = version, toSign = true, toEncrypt = false))
        addHeadParam(Param(name = "signMode", value = "SM3", toSign = false, toEncrypt = false))
        addHeadParam(
            Param(
                name = "encryptMode",
                value = "SM4/ECB/ZeroBytePadding",
                toSign = false,
                toEncrypt = false
            )
        )
        addBodyParam(Param(name = "orgCode", value = orgCode, toSign = true, toEncrypt = false))
        addBodyParam(Param(name = "appRecordNo", value = appRecordNo, toSign = true, toEncrypt = false))
        signHead()
        signBody()

        val headMap = mutableMapOf<String, Any>()
        headParams.entries.forEach {
            headMap[it.key] = it.value.encryptedValue
        }

        val bodyMap = mutableMapOf<String, Any>()
        bodyParams.entries.forEach {
            bodyMap[it.key] = it.value.encryptedValue
        }

        headMap["body"] = bodyMap
        return JacksonKit.writeValueAsString(headMap)
    }

    fun executeAsString(): String? {
        val json = buildJSON()
        val requestBody = json.toRequestBody(mediaType)
        val request = Request.Builder().url(url).post(requestBody).build()
        val s = httpClient.newCall(request).execute().body?.string()
        return s
    }

    open fun executeAsRet(): Ret {
        try {
            val s = executeAsString()
                ?: return Ret.fail("empty response")
            val ret = JacksonKit.readValue(s, Ret::class.java)

            if (ret["returnCode"] == 0) {
                ret.ok()
            } else {
                ret.fail()
            }
            ret.remove("returnCode")

            ret.msg(ret.getStr("returnDesc"))
            ret.remove("returnDesc")

            return ret
        } catch (e: Exception) {
            log.error(e.message, e)
            return Ret.fail(e.message ?: "电子健康卡接口异常错误")
        }
    }
}
