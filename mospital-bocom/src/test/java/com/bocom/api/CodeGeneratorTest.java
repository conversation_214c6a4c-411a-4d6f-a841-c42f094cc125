package com.bocom.api;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class CodeGeneratorTest {

    @Test
    void testGenerateCode() {
        String idCardNo = "622428199803145825";
        String name = "贺永芳";
        String mobile = "19993448253";

        String expected = "zMtbgZHKfa1Otd/U1QnirDvGmvN6+LgBt/xPJtjsKnJH9EFm7/nOfhNFDhjS7y2Q";
        String actual = CodeGenerator.generateCode(idCardNo, name, mobile);

        assertEquals(expected, actual);
    }

}
