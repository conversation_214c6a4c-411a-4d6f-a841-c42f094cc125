package com.bocom.api;

import org.junit.jupiter.api.BeforeAll;

public class BaseTest {

    protected static DefaultBocomClient client;

    @BeforeAll
    static void init() {
        client = new DefaultBocomClient(
                BocomConfig.me.getAppId(),
                BocomConfig.me.getMyPrivateKey(),
                BocomConfig.me.getApigwPublicKey()
        );
        client.ignoreSSLHostnameVerifier();
    }

}
