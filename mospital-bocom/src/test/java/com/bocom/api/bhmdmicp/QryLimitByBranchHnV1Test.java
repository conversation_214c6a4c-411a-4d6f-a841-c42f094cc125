package com.bocom.api.bhmdmicp;

import com.bocom.api.BaseTest;
import com.bocom.api.BocomApiException;
import com.bocom.api.BocomConfig;
import com.bocom.api.request.bhmdmicp.QryLimitByBranchHnRequestV1;
import com.bocom.api.response.bhmdMicp.QryLimitByBranchHnResponseV1;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mospital.common.IdUtil;

import static org.junit.jupiter.api.Assertions.assertTrue;

class QryLimitByBranchHnV1Test extends BaseTest {

    @Test
    @Disabled
    void testQryLimitByBranchHnV1() throws BocomApiException {
        QryLimitByBranchHnRequestV1 request = buildRequest();
        QryLimitByBranchHnResponseV1 response = client.execute(request, IdUtil.INSTANCE.simpleUUID());
        assertTrue(response.isSuccess(), response.getRspCode() + " " + response.getRspMsg());
    }

    private static @NotNull QryLimitByBranchHnRequestV1 buildRequest() {
        QryLimitByBranchHnRequestV1.QryLimitByBranchHnRequestV1Biz bizContent = new QryLimitByBranchHnRequestV1.QryLimitByBranchHnRequestV1Biz();
        bizContent.setIdNo("XXXX");
        bizContent.setIdTyp("0102");
        bizContent.setNme("XXX");
        bizContent.setChannelId("HI00000001");
        bizContent.setAgreementNo("XXXX");
        bizContent.setExtfld("XXXX");
        bizContent.setBbiprequestheadTxncde("XXXX");
        bizContent.setBbiprequestheadReqsyscde("XXXX");
        bizContent.setBbiprequestheadSubsyscde("XXXX");
        bizContent.setBbiprequestheadBbipbuscde("X");
        bizContent.setBbiprequestheadTlrtmlid("XXXX");
        bizContent.setBbiprequestheadReqtyp("X");
        bizContent.setBbiprequestheadAuthlvl("XX");
        bizContent.setBbiprequestheadSup1id("XXX");
        bizContent.setBbiprequestheadSup1auth("XX");
        bizContent.setBbiprequestheadSup2id("XX");
        bizContent.setBbiprequestheadSup2auth("XX");
        bizContent.setBbiprequestheadAuthlog("X");
        bizContent.setBbiprequestheadFiller("XXX");
        bizContent.setBbiprequestheadReqtme("X");
        bizContent.setAuthresntblAuthresn("XXX");

        QryLimitByBranchHnRequestV1 request = new QryLimitByBranchHnRequestV1();
        request.setServiceUrl(BocomConfig.me.getApigwUrlAddress() + "/api/bhmdMicp/qryLimitByBranchHn/v1");
        request.setBizContent(bizContent);
        return request;
    }
}
