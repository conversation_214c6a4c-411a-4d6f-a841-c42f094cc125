package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpQryPayDetailResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryPayDetailRequestV1 extends AbstractBocomRequest<HmdMicpQryPayDetailResponseV1> {

    @Override
    public Class<HmdMicpQryPayDetailResponseV1> getResponseClass() {
        return HmdMicpQryPayDetailResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpQryPayDetailRequestV1Biz.class;
    }

    public static class HmdMicpQryPayDetailRequestV1Biz implements BizContent {

        /**
         * 支付流水号
         */
        @JsonProperty("pay_id")
        private String payId;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }
    }
}
