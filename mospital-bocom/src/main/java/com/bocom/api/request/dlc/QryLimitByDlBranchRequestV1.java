package com.bocom.api.request.dlc;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.dlc.QryLimitByDlBranchResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class QryLimitByDlBranchRequestV1 extends AbstractBocomRequest<QryLimitByDlBranchResponseV1> {

    @Override
    public Class<QryLimitByDlBranchResponseV1> getResponseClass() {
        return QryLimitByDlBranchResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return QryLimitByDlBranchRequestV1Biz.class;
    }

    public static class QryLimitByDlBranchRequestV1Biz implements BizContent {

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 渠道ID
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 扩展域
         */
        @JsonProperty("extfld")
        private String extfld;

        /**
         * 交易码
         */
        @JsonProperty("bbiprequesthead_txncde")
        private String bbiprequestheadTxncde;

        /**
         * 交易渠道
         */
        @JsonProperty("bbiprequesthead_chn")
        private String bbiprequestheadChn;

        /**
         * 请求系统标志
         */
        @JsonProperty("bbiprequesthead_reqsyscde")
        private String bbiprequestheadReqsyscde;

        /**
         * 目标子系统码
         */
        @JsonProperty("bbiprequesthead_subsyscde")
        private String bbiprequestheadSubsyscde;

        /**
         * BBIP业务类型
         */
        @JsonProperty("bbiprequesthead_bbipbuscde")
        private String bbiprequestheadBbipbuscde;

        /**
         * 请求系统流水
         */
        @JsonProperty("bbiprequesthead_reqjrnno")
        private String bbiprequestheadReqjrnno;

        /**
         * 柜员号
         */
        @JsonProperty("bbiprequesthead_tlr")
        private String bbiprequestheadTlr;

        /**
         * 机构号
         */
        @JsonProperty("bbiprequesthead_br")
        private String bbiprequestheadBr;

        /**
         * 分行号
         */
        @JsonProperty("bbiprequesthead_bk")
        private String bbiprequestheadBk;

        /**
         * 授权LOG标志
         */
        @JsonProperty("bbiprequesthead_authlog")
        private String bbiprequestheadAuthlog;

        /**
         * 备用
         */
        @JsonProperty("bbiprequesthead_filler")
        private String bbiprequestheadFiller;

        /**
         * 发起交易时间
         */
        @JsonProperty("bbiprequesthead_reqtme")
        private String bbiprequestheadReqtme;

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getExtfld() {
            return extfld;
        }

        public void setExtfld(String extfld) {
            this.extfld = extfld;
        }

        public String getBbiprequestheadTxncde() {
            return bbiprequestheadTxncde;
        }

        public void setBbiprequestheadTxncde(String bbiprequestheadTxncde) {
            this.bbiprequestheadTxncde = bbiprequestheadTxncde;
        }

        public String getBbiprequestheadChn() {
            return bbiprequestheadChn;
        }

        public void setBbiprequestheadChn(String bbiprequestheadChn) {
            this.bbiprequestheadChn = bbiprequestheadChn;
        }

        public String getBbiprequestheadReqsyscde() {
            return bbiprequestheadReqsyscde;
        }

        public void setBbiprequestheadReqsyscde(String bbiprequestheadReqsyscde) {
            this.bbiprequestheadReqsyscde = bbiprequestheadReqsyscde;
        }

        public String getBbiprequestheadSubsyscde() {
            return bbiprequestheadSubsyscde;
        }

        public void setBbiprequestheadSubsyscde(String bbiprequestheadSubsyscde) {
            this.bbiprequestheadSubsyscde = bbiprequestheadSubsyscde;
        }

        public String getBbiprequestheadBbipbuscde() {
            return bbiprequestheadBbipbuscde;
        }

        public void setBbiprequestheadBbipbuscde(String bbiprequestheadBbipbuscde) {
            this.bbiprequestheadBbipbuscde = bbiprequestheadBbipbuscde;
        }

        public String getBbiprequestheadReqjrnno() {
            return bbiprequestheadReqjrnno;
        }

        public void setBbiprequestheadReqjrnno(String bbiprequestheadReqjrnno) {
            this.bbiprequestheadReqjrnno = bbiprequestheadReqjrnno;
        }

        public String getBbiprequestheadTlr() {
            return bbiprequestheadTlr;
        }

        public void setBbiprequestheadTlr(String bbiprequestheadTlr) {
            this.bbiprequestheadTlr = bbiprequestheadTlr;
        }

        public String getBbiprequestheadBr() {
            return bbiprequestheadBr;
        }

        public void setBbiprequestheadBr(String bbiprequestheadBr) {
            this.bbiprequestheadBr = bbiprequestheadBr;
        }

        public String getBbiprequestheadBk() {
            return bbiprequestheadBk;
        }

        public void setBbiprequestheadBk(String bbiprequestheadBk) {
            this.bbiprequestheadBk = bbiprequestheadBk;
        }

        public String getBbiprequestheadAuthlog() {
            return bbiprequestheadAuthlog;
        }

        public void setBbiprequestheadAuthlog(String bbiprequestheadAuthlog) {
            this.bbiprequestheadAuthlog = bbiprequestheadAuthlog;
        }

        public String getBbiprequestheadFiller() {
            return bbiprequestheadFiller;
        }

        public void setBbiprequestheadFiller(String bbiprequestheadFiller) {
            this.bbiprequestheadFiller = bbiprequestheadFiller;
        }

        public String getBbiprequestheadReqtme() {
            return bbiprequestheadReqtme;
        }

        public void setBbiprequestheadReqtme(String bbiprequestheadReqtme) {
            this.bbiprequestheadReqtme = bbiprequestheadReqtme;
        }
    }
}
