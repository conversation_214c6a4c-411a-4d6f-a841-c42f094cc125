package com.bocom.api.request.bhmdmicp;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.bhmdMicp.QryLimitByBranchResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class QryLimitByBranchRequestV1 extends AbstractBocomRequest<QryLimitByBranchResponseV1> {

    @Override
    public Class<QryLimitByBranchResponseV1> getResponseClass() {
        return QryLimitByBranchResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return QryLimitByBranchRequestV1Biz.class;
    }

    public static class QryLimitByBranchRequestV1Biz implements BizContent {

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 渠道ID
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 扩展域
         */
        @JsonProperty("extfld")
        private String extfld;

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getExtfld() {
            return extfld;
        }

        public void setExtfld(String extfld) {
            this.extfld = extfld;
        }
    }
}
