package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpQryAgreementResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryAgreementRequestV1 extends AbstractBocomRequest<HmdMicpQryAgreementResponseV1> {

    @Override
    public Class<HmdMicpQryAgreementResponseV1> getResponseClass() {
        return HmdMicpQryAgreementResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpQryAgreementRequestV1Biz.class;
    }

    public static class HmdMicpQryAgreementRequestV1Biz implements BizContent {

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 用户id
         */
        @JsonProperty("use_id")
        private String useId;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getUseId() {
            return useId;
        }

        public void setUseId(String useId) {
            this.useId = useId;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }
    }
}
