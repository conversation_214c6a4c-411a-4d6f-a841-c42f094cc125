package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpQryAgreementResponseV3;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryAgreementRequestV3 extends AbstractBocomRequest<HmdMicpQryAgreementResponseV3> {

    @Override
    public Class<HmdMicpQryAgreementResponseV3> getResponseClass() {
        return HmdMicpQryAgreementResponseV3.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpQryAgreementRequestV3Biz.class;
    }

    public static class HmdMicpQryAgreementRequestV3Biz implements BizContent {

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 用户id
         */
        @JsonProperty("use_id")
        private String useId;

        /**
         * 交易类型 1-线下 2-线上
         */
        @JsonProperty("pay_type")
        private String payType;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getUseId() {
            return useId;
        }

        public void setUseId(String useId) {
            this.useId = useId;
        }

        public String getPayType() {
            return payType;
        }

        public void setPayType(String payType) {
            this.payType = payType;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }
    }
}
