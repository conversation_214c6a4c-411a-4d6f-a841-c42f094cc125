package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpQryAgreementResponseV2;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryAgreementRequestV2 extends AbstractBocomRequest<HmdMicpQryAgreementResponseV2> {

    @Override
    public Class<HmdMicpQryAgreementResponseV2> getResponseClass() {
        return HmdMicpQryAgreementResponseV2.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpQryAgreementRequestV2Biz.class;
    }

    public static class HmdMicpQryAgreementRequestV2Biz implements BizContent {

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 用户id
         */
        @JsonProperty("use_id")
        private String useId;

        /**
         * 交易类型 1-线下 2-线上
         */
        @JsonProperty("pay_type")
        private String payType;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getUseId() {
            return useId;
        }

        public void setUseId(String useId) {
            this.useId = useId;
        }

        public String getPayType() {
            return payType;
        }

        public void setPayType(String payType) {
            this.payType = payType;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }
    }
}
