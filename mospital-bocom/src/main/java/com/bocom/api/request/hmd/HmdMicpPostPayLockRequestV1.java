package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpPostPayLockResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpPostPayLockRequestV1 extends AbstractBocomRequest<HmdMicpPostPayLockResponseV1> {

    @Override
    public Class<HmdMicpPostPayLockResponseV1> getResponseClass() {
        return HmdMicpPostPayLockResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpPostPayLockRequestV1Biz.class;
    }

    public static class HmdMicpPostPayLockRequestV1Biz implements BizContent {

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医后付锁定流水号
         */
        @JsonProperty("lock_id")
        private String lockId;

        /**
         * 是否锁定全部
         */
        @JsonProperty("lock_all_flg")
        private String lockAllFlg;

        /**
         * 医后付锁定金额
         */
        @JsonProperty("lock_fee")
        private String lockFee;

        /**
         * 医保地区码
         */
        @JsonProperty("are_no")
        private String areNo;

        /**
         * 医疗机构号
         */
        @JsonProperty("org_no")
        private String orgNo;

        /**
         * 医疗机构名称
         */
        @JsonProperty("hospital_name")
        private String hospitalName;

        /**
         * 请求时间
         */
        @JsonProperty("req_time")
        private String reqTime;

        /**
         * 用户id
         */
        @JsonProperty("use_id")
        private String useId;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 通知地址
         */
        @JsonProperty("notify_url")
        private String notifyUrl;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getLockId() {
            return lockId;
        }

        public void setLockId(String lockId) {
            this.lockId = lockId;
        }

        public String getLockAllFlg() {
            return lockAllFlg;
        }

        public void setLockAllFlg(String lockAllFlg) {
            this.lockAllFlg = lockAllFlg;
        }

        public String getLockFee() {
            return lockFee;
        }

        public void setLockFee(String lockFee) {
            this.lockFee = lockFee;
        }

        public String getAreNo() {
            return areNo;
        }

        public void setAreNo(String areNo) {
            this.areNo = areNo;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getHospitalName() {
            return hospitalName;
        }

        public void setHospitalName(String hospitalName) {
            this.hospitalName = hospitalName;
        }

        public String getReqTime() {
            return reqTime;
        }

        public void setReqTime(String reqTime) {
            this.reqTime = reqTime;
        }

        public String getUseId() {
            return useId;
        }

        public void setUseId(String useId) {
            this.useId = useId;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getNotifyUrl() {
            return notifyUrl;
        }

        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }
    }
}
