package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpPayCheckResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpPayCheckRequestV1 extends AbstractBocomRequest<HmdMicpPayCheckResponseV1> {

    @Override
    public Class<HmdMicpPayCheckResponseV1> getResponseClass() {
        return HmdMicpPayCheckResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpPayCheckRequestV1Biz.class;
    }

    public static class HmdMicpPayCheckRequestV1Biz implements BizContent {

        /**
         * 支付流水号
         */
        @JsonProperty("pay_id")
        private String payId;

        /**
         * 请求时间
         */
        @JsonProperty("req_time")
        private String reqTime;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 用户id
         */
        @JsonProperty("use_id")
        private String useId;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 医疗机构名称
         */
        @JsonProperty("hospital_name")
        private String hospitalName;

        /**
         * 医保地区码
         */
        @JsonProperty("are_no")
        private String areNo;

        /**
         * 医疗机构号
         */
        @JsonProperty("org_no")
        private String orgNo;

        /**
         * 支付金额
         */
        @JsonProperty("pay_fee")
        private String payFee;

        /**
         * 订单类型
         */
        @JsonProperty("bill_type")
        private String billType;

        /**
         * 订单详情
         */
        @JsonProperty("bill_detail")
        private String billDetail;

        /**
         * 业务票据编号
         */
        @JsonProperty("bill_no")
        private String billNo;

        /**
         * 交易类型
         */
        @JsonProperty("pay_type")
        private String payType;

        /**
         * 交易类型-细分
         */
        @JsonProperty("pay_type_detail")
        private String payTypeDetail;

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getReqTime() {
            return reqTime;
        }

        public void setReqTime(String reqTime) {
            this.reqTime = reqTime;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getUseId() {
            return useId;
        }

        public void setUseId(String useId) {
            this.useId = useId;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getHospitalName() {
            return hospitalName;
        }

        public void setHospitalName(String hospitalName) {
            this.hospitalName = hospitalName;
        }

        public String getAreNo() {
            return areNo;
        }

        public void setAreNo(String areNo) {
            this.areNo = areNo;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getPayFee() {
            return payFee;
        }

        public void setPayFee(String payFee) {
            this.payFee = payFee;
        }

        public String getBillType() {
            return billType;
        }

        public void setBillType(String billType) {
            this.billType = billType;
        }

        public String getBillDetail() {
            return billDetail;
        }

        public void setBillDetail(String billDetail) {
            this.billDetail = billDetail;
        }

        public String getBillNo() {
            return billNo;
        }

        public void setBillNo(String billNo) {
            this.billNo = billNo;
        }

        public String getPayType() {
            return payType;
        }

        public void setPayType(String payType) {
            this.payType = payType;
        }

        public String getPayTypeDetail() {
            return payTypeDetail;
        }

        public void setPayTypeDetail(String payTypeDetail) {
            this.payTypeDetail = payTypeDetail;
        }

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }
    }
}
