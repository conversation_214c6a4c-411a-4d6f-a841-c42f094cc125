package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpQryRefundDetailResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryRefundDetailRequestV1 extends AbstractBocomRequest<HmdMicpQryRefundDetailResponseV1> {

    @Override
    public Class<HmdMicpQryRefundDetailResponseV1> getResponseClass() {
        return HmdMicpQryRefundDetailResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpQryRefundDetailRequestV1Biz.class;
    }

    public static class HmdMicpQryRefundDetailRequestV1Biz implements BizContent {

        /**
         * 退款流水号
         */
        @JsonProperty("refund_id")
        private String refundId;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        public String getRefundId() {
            return refundId;
        }

        public void setRefundId(String refundId) {
            this.refundId = refundId;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }
    }
}
