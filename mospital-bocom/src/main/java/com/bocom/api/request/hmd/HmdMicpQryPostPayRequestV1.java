package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpQryPostPayResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryPostPayRequestV1 extends AbstractBocomRequest<HmdMicpQryPostPayResponseV1> {

    @Override
    public Class<HmdMicpQryPostPayResponseV1> getResponseClass() {
        return HmdMicpQryPostPayResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpQryPostPayRequestV1Biz.class;
    }

    public static class HmdMicpQryPostPayRequestV1Biz implements BizContent {

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医疗机构号
         */
        @JsonProperty("org_no")
        private String orgNo;

        /**
         * 医后付锁定流水号
         */
        @JsonProperty("lock_id")
        private String lockId;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getLockId() {
            return lockId;
        }

        public void setLockId(String lockId) {
            this.lockId = lockId;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }
    }
}
