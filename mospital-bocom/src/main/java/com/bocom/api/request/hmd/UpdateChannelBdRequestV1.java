package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.UpdateChannelBdResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class UpdateChannelBdRequestV1 extends AbstractBocomRequest<UpdateChannelBdResponseV1> {

    @Override
    public Class<UpdateChannelBdResponseV1> getResponseClass() {
        return UpdateChannelBdResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return UpdateChannelBdRequestV1Biz.class;
    }

    public static class UpdateChannelBdRequestV1Biz implements BizContent {

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 操作内容
         */
        @JsonProperty("banding")
        private String banding;

        /**
         * 合作机构编号
         */
        @JsonProperty("merchant_id")
        private String merchantId;

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getBanding() {
            return banding;
        }

        public void setBanding(String banding) {
            this.banding = banding;
        }

        public String getMerchantId() {
            return merchantId;
        }

        public void setMerchantId(String merchantId) {
            this.merchantId = merchantId;
        }
    }
}
