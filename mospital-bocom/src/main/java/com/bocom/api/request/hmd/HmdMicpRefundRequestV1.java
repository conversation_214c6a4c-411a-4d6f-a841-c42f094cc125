package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpRefundResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpRefundRequestV1 extends AbstractBocomRequest<HmdMicpRefundResponseV1> {

    @Override
    public Class<HmdMicpRefundResponseV1> getResponseClass() {
        return HmdMicpRefundResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpRefundRequestV1Biz.class;
    }

    public static class HmdMicpRefundRequestV1Biz implements BizContent {

        /**
         * 退款流水号
         */
        @JsonProperty("refund_id")
        private String refundId;

        /**
         * 退款明细信息
         */
        @JsonProperty("refund_detail")
        private String refundDetail;

        /**
         * 请求时间
         */
        @JsonProperty("req_time")
        private String reqTime;

        /**
         * 医保地区码
         */
        @JsonProperty("area_no")
        private String areaNo;

        /**
         * 退款金额
         */
        @JsonProperty("refund_fee")
        private String refundFee;

        /**
         * 支付流水号
         */
        @JsonProperty("pay_id")
        private String payId;

        /**
         * 医疗机构号
         */
        @JsonProperty("org_no")
        private String orgNo;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        public String getRefundId() {
            return refundId;
        }

        public void setRefundId(String refundId) {
            this.refundId = refundId;
        }

        public String getRefundDetail() {
            return refundDetail;
        }

        public void setRefundDetail(String refundDetail) {
            this.refundDetail = refundDetail;
        }

        public String getReqTime() {
            return reqTime;
        }

        public void setReqTime(String reqTime) {
            this.reqTime = reqTime;
        }

        public String getAreaNo() {
            return areaNo;
        }

        public void setAreaNo(String areaNo) {
            this.areaNo = areaNo;
        }

        public String getRefundFee() {
            return refundFee;
        }

        public void setRefundFee(String refundFee) {
            this.refundFee = refundFee;
        }

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }
    }
}
