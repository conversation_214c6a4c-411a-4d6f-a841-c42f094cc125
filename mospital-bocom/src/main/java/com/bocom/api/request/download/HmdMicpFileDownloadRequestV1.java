package com.bocom.api.request.download;


import com.bocom.api.BizContent;
import com.bocom.api.BocomDownloadRequest;
import com.bocom.api.response.download.HmdMicpFileDownloadResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpFileDownloadRequestV1 extends BocomDownloadRequest<HmdMicpFileDownloadResponseV1> {

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<HmdMicpFileDownloadResponseV1> getResponseClass() {
        return HmdMicpFileDownloadResponseV1.class;
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpFileDownloadRequestV1Biz.class;
    }

    public static class HmdMicpFileDownloadRequestV1Biz implements BizContent {

        @JsonProperty("check_date")
        private String checkDate;

        @JsonProperty("org_no")
        private String orgNo;

        @JsonProperty("check_type")
        private String checkType;

        //@FieldDoc(desc="签约渠道")
        @JsonProperty("channel_id")
        private String channelId;

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getCheckDate() {
            return checkDate;
        }

        public void setCheckDate(String checkDate) {
            this.checkDate = checkDate;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getCheckType() {
            return checkType;
        }

        public void setCheckType(String checkType) {
            this.checkType = checkType;
        }


    }
}
