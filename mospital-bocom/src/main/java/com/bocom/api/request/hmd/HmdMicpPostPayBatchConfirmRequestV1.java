package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpPostPayBatchConfirmResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;


public class HmdMicpPostPayBatchConfirmRequestV1 extends AbstractBocomRequest<HmdMicpPostPayBatchConfirmResponseV1> {

    @Override
    public Class<HmdMicpPostPayBatchConfirmResponseV1> getResponseClass() {
        return HmdMicpPostPayBatchConfirmResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpPostPayBatchConfirmRequestV1Biz.class;
    }

    public static class HmdMicpPostPayBatchConfirmRequestV1Biz implements BizContent {

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医疗机构号
         */
        @JsonProperty("org_no")
        private String orgNo;

        /**
         * 结算发起方
         */
        @JsonProperty("initiator")
        private String initiator;

        /**
         * 请求时间
         */
        @JsonProperty("req_time")
        private String reqTime;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        /**
         * "确认列表"
         */
        @JsonProperty("confirm_list")
        private List<Confirm> confirmList;

        public static class Confirm {
            /**
             * 医后付锁定流水号
             */
            @JsonProperty("lock_id")
            private String lockId;

            /**
             * 医后付确认流水号
             */
            @JsonProperty("confirm_id")
            private String confirmId;

            /**
             * 医后付确认金额
             */
            @JsonProperty("confirm_fee")
            private String confirmFee;

            /**
             * 业务票据编号
             */
            @JsonProperty("bill_no")
            private String billNo;

            /**
             * 交易类型
             */
            @JsonProperty("pay_type")
            private String payType;

            /**
             * 交易类型-细分
             */
            @JsonProperty("pay_type_detail")
            private String payTypeDetail;

            /**
             * 支付模式
             */
            @JsonProperty("pay_mode")
            private String payMode;

            /**
             * 医保信用支付协议号
             */
            @JsonProperty("agreement_no")
            private String agreementNo;

            /**
             * 证件号
             */
            @JsonProperty("id_no")
            private String idNo;

            /**
             * 证件类型
             */
            @JsonProperty("id_typ")
            private String idTyp;

            /**
             * 姓名
             */
            @JsonProperty("nme")
            private String nme;

            /**
             * "付费明细"
             */
            @JsonProperty("dtl_list")
            private List<Dtl> dtlList;

            public static class Dtl {
                /**
                 * 业务票据编号
                 */
                @JsonProperty("bill_no")
                private String billNo;

                /**
                 * 金额
                 */
                @JsonProperty("bill_fee")
                private String billFee;

                /**
                 * 发生时间
                 */
                @JsonProperty("bill_time")
                private String billTime;

                /**
                 * 订单类型
                 */
                @JsonProperty("bill_type")
                private String billType;

                /**
                 * 订单详情
                 */
                @JsonProperty("bill_detail")
                private String billDetail;

                /**
                 * 支付场景
                 */
                @JsonProperty("bill_scene")
                private String billScene;

                public String getBillNo() {
                    return billNo;
                }

                public void setBillNo(String billNo) {
                    this.billNo = billNo;
                }

                public String getBillFee() {
                    return billFee;
                }

                public void setBillFee(String billFee) {
                    this.billFee = billFee;
                }

                public String getBillTime() {
                    return billTime;
                }

                public void setBillTime(String billTime) {
                    this.billTime = billTime;
                }

                public String getBillType() {
                    return billType;
                }

                public void setBillType(String billType) {
                    this.billType = billType;
                }

                public String getBillDetail() {
                    return billDetail;
                }

                public void setBillDetail(String billDetail) {
                    this.billDetail = billDetail;
                }

                public String getBillScene() {
                    return billScene;
                }

                public void setBillScene(String billScene) {
                    this.billScene = billScene;
                }
            }

            public String getLockId() {
                return lockId;
            }

            public void setLockId(String lockId) {
                this.lockId = lockId;
            }

            public String getConfirmId() {
                return confirmId;
            }

            public void setConfirmId(String confirmId) {
                this.confirmId = confirmId;
            }

            public String getConfirmFee() {
                return confirmFee;
            }

            public void setConfirmFee(String confirmFee) {
                this.confirmFee = confirmFee;
            }

            public String getBillNo() {
                return billNo;
            }

            public void setBillNo(String billNo) {
                this.billNo = billNo;
            }

            public String getPayType() {
                return payType;
            }

            public void setPayType(String payType) {
                this.payType = payType;
            }

            public String getPayTypeDetail() {
                return payTypeDetail;
            }

            public void setPayTypeDetail(String payTypeDetail) {
                this.payTypeDetail = payTypeDetail;
            }

            public String getPayMode() {
                return payMode;
            }

            public void setPayMode(String payMode) {
                this.payMode = payMode;
            }

            public String getAgreementNo() {
                return agreementNo;
            }

            public void setAgreementNo(String agreementNo) {
                this.agreementNo = agreementNo;
            }

            public String getIdNo() {
                return idNo;
            }

            public void setIdNo(String idNo) {
                this.idNo = idNo;
            }

            public String getIdTyp() {
                return idTyp;
            }

            public void setIdTyp(String idTyp) {
                this.idTyp = idTyp;
            }

            public String getNme() {
                return nme;
            }

            public void setNme(String nme) {
                this.nme = nme;
            }

            public List<Dtl> getDtlList() {
                return dtlList;
            }

            public void setDtlList(List<Dtl> dtlList) {
                this.dtlList = dtlList;
            }
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getInitiator() {
            return initiator;
        }

        public void setInitiator(String initiator) {
            this.initiator = initiator;
        }

        public String getReqTime() {
            return reqTime;
        }

        public void setReqTime(String reqTime) {
            this.reqTime = reqTime;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }

        public List<Confirm> getConfirmList() {
            return confirmList;
        }

        public void setConfirmList(List<Confirm> confirmList) {
            this.confirmList = confirmList;
        }
    }
}
