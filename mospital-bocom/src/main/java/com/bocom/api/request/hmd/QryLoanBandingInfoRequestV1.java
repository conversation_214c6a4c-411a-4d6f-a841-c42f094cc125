package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.QryLoanBandingInfoResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class QryLoanBandingInfoRequestV1 extends AbstractBocomRequest<QryLoanBandingInfoResponseV1> {

    @Override
    public Class<QryLoanBandingInfoResponseV1> getResponseClass() {
        return QryLoanBandingInfoResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return QryLoanBandingInfoRequestV1Biz.class;
    }

    public static class QryLoanBandingInfoRequestV1Biz implements BizContent {

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 合作机构编号
         */
        @JsonProperty("merchant_id")
        private String merchantId;

        /**
         * 业务类型
         */
        @JsonProperty("bus_typ")
        private String busTyp;

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getMerchantId() {
            return merchantId;
        }

        public void setMerchantId(String merchantId) {
            this.merchantId = merchantId;
        }

        public String getBusTyp() {
            return busTyp;
        }

        public void setBusTyp(String busTyp) {
            this.busTyp = busTyp;
        }
    }
}
