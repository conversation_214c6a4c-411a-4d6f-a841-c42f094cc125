package com.bocom.api.request.bhmdmicp;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.bhmdMicp.QryLimitByBranchHnResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class QryLimitByBranchHnRequestV1 extends AbstractBocomRequest<QryLimitByBranchHnResponseV1> {

    @Override
    public Class<QryLimitByBranchHnResponseV1> getResponseClass() {
        return QryLimitByBranchHnResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return QryLimitByBranchHnRequestV1Biz.class;
    }

    public static class QryLimitByBranchHnRequestV1Biz implements BizContent {

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 渠道ID
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 扩展域
         */
        @JsonProperty("extfld")
        private String extfld;

        /**
         * 交易码
         */
        @JsonProperty("bbiprequesthead_txncde")
        private String bbiprequestheadTxncde;

        /**
         * 请求系统标志
         */
        @JsonProperty("bbiprequesthead_reqsyscde")
        private String bbiprequestheadReqsyscde;

        /**
         * 目标子系统码
         */
        @JsonProperty("bbiprequesthead_subsyscde")
        private String bbiprequestheadSubsyscde;

        /**
         * BBIP业务类型
         */
        @JsonProperty("bbiprequesthead_bbipbuscde")
        private String bbiprequestheadBbipbuscde;

        /**
         * 终端号
         */
        @JsonProperty("bbiprequesthead_tlrtmlid")
        private String bbiprequestheadTlrtmlid;

        /**
         * 请求类型
         */
        @JsonProperty("bbiprequesthead_reqtyp")
        private String bbiprequestheadReqtyp;

        /**
         * 授权级别
         */
        @JsonProperty("bbiprequesthead_authlvl")
        private String bbiprequestheadAuthlvl;

        /**
         * 主管1
         */
        @JsonProperty("bbiprequesthead_sup1id")
        private String bbiprequestheadSup1id;

        /**
         * 主管1授权级别
         */
        @JsonProperty("bbiprequesthead_sup1auth")
        private String bbiprequestheadSup1auth;

        /**
         * 主管2
         */
        @JsonProperty("bbiprequesthead_sup2id")
        private String bbiprequestheadSup2id;

        /**
         * 主管2授权级别
         */
        @JsonProperty("bbiprequesthead_sup2auth")
        private String bbiprequestheadSup2auth;

        /**
         * 授权LOG标志
         */
        @JsonProperty("bbiprequesthead_authlog")
        private String bbiprequestheadAuthlog;

        /**
         * 备用
         */
        @JsonProperty("bbiprequesthead_filler")
        private String bbiprequestheadFiller;

        /**
         * 发起交易时间
         */
        @JsonProperty("bbiprequesthead_reqtme")
        private String bbiprequestheadReqtme;

        /**
         * 授权原因码
         */
        @JsonProperty("authresntbl_authresn")
        private String authresntblAuthresn;

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getExtfld() {
            return extfld;
        }

        public void setExtfld(String extfld) {
            this.extfld = extfld;
        }

        public String getBbiprequestheadTxncde() {
            return bbiprequestheadTxncde;
        }

        public void setBbiprequestheadTxncde(String bbiprequestheadTxncde) {
            this.bbiprequestheadTxncde = bbiprequestheadTxncde;
        }

        public String getBbiprequestheadReqsyscde() {
            return bbiprequestheadReqsyscde;
        }

        public void setBbiprequestheadReqsyscde(String bbiprequestheadReqsyscde) {
            this.bbiprequestheadReqsyscde = bbiprequestheadReqsyscde;
        }

        public String getBbiprequestheadSubsyscde() {
            return bbiprequestheadSubsyscde;
        }

        public void setBbiprequestheadSubsyscde(String bbiprequestheadSubsyscde) {
            this.bbiprequestheadSubsyscde = bbiprequestheadSubsyscde;
        }

        public String getBbiprequestheadBbipbuscde() {
            return bbiprequestheadBbipbuscde;
        }

        public void setBbiprequestheadBbipbuscde(String bbiprequestheadBbipbuscde) {
            this.bbiprequestheadBbipbuscde = bbiprequestheadBbipbuscde;
        }

        public String getBbiprequestheadTlrtmlid() {
            return bbiprequestheadTlrtmlid;
        }

        public void setBbiprequestheadTlrtmlid(String bbiprequestheadTlrtmlid) {
            this.bbiprequestheadTlrtmlid = bbiprequestheadTlrtmlid;
        }

        public String getBbiprequestheadReqtyp() {
            return bbiprequestheadReqtyp;
        }

        public void setBbiprequestheadReqtyp(String bbiprequestheadReqtyp) {
            this.bbiprequestheadReqtyp = bbiprequestheadReqtyp;
        }

        public String getBbiprequestheadAuthlvl() {
            return bbiprequestheadAuthlvl;
        }

        public void setBbiprequestheadAuthlvl(String bbiprequestheadAuthlvl) {
            this.bbiprequestheadAuthlvl = bbiprequestheadAuthlvl;
        }

        public String getBbiprequestheadSup1id() {
            return bbiprequestheadSup1id;
        }

        public void setBbiprequestheadSup1id(String bbiprequestheadSup1id) {
            this.bbiprequestheadSup1id = bbiprequestheadSup1id;
        }

        public String getBbiprequestheadSup1auth() {
            return bbiprequestheadSup1auth;
        }

        public void setBbiprequestheadSup1auth(String bbiprequestheadSup1auth) {
            this.bbiprequestheadSup1auth = bbiprequestheadSup1auth;
        }

        public String getBbiprequestheadSup2id() {
            return bbiprequestheadSup2id;
        }

        public void setBbiprequestheadSup2id(String bbiprequestheadSup2id) {
            this.bbiprequestheadSup2id = bbiprequestheadSup2id;
        }

        public String getBbiprequestheadSup2auth() {
            return bbiprequestheadSup2auth;
        }

        public void setBbiprequestheadSup2auth(String bbiprequestheadSup2auth) {
            this.bbiprequestheadSup2auth = bbiprequestheadSup2auth;
        }

        public String getBbiprequestheadAuthlog() {
            return bbiprequestheadAuthlog;
        }

        public void setBbiprequestheadAuthlog(String bbiprequestheadAuthlog) {
            this.bbiprequestheadAuthlog = bbiprequestheadAuthlog;
        }

        public String getBbiprequestheadFiller() {
            return bbiprequestheadFiller;
        }

        public void setBbiprequestheadFiller(String bbiprequestheadFiller) {
            this.bbiprequestheadFiller = bbiprequestheadFiller;
        }

        public String getBbiprequestheadReqtme() {
            return bbiprequestheadReqtme;
        }

        public void setBbiprequestheadReqtme(String bbiprequestheadReqtme) {
            this.bbiprequestheadReqtme = bbiprequestheadReqtme;
        }

        public String getAuthresntblAuthresn() {
            return authresntblAuthresn;
        }

        public void setAuthresntblAuthresn(String authresntblAuthresn) {
            this.authresntblAuthresn = authresntblAuthresn;
        }
    }
}
