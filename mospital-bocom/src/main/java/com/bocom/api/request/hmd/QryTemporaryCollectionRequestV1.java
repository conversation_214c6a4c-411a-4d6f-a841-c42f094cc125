package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.QryTemporaryCollectionResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class QryTemporaryCollectionRequestV1 extends AbstractBocomRequest<QryTemporaryCollectionResponseV1> {

    @Override
    public Class<QryTemporaryCollectionResponseV1> getResponseClass() {
        return QryTemporaryCollectionResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return QryTemporaryCollectionRequestV1Biz.class;
    }

    public static class QryTemporaryCollectionRequestV1Biz implements BizContent {

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医疗机构号
         */
        @JsonProperty("hos_org_no")
        private String hosOrgNo;

        /**
         * 医疗机构名称
         */
        @JsonProperty("hos_org_nme")
        private String hosOrgNme;

        /**
         * 扩展域
         */
        @JsonProperty("extfld")
        private String extfld;

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getHosOrgNo() {
            return hosOrgNo;
        }

        public void setHosOrgNo(String hosOrgNo) {
            this.hosOrgNo = hosOrgNo;
        }

        public String getHosOrgNme() {
            return hosOrgNme;
        }

        public void setHosOrgNme(String hosOrgNme) {
            this.hosOrgNme = hosOrgNme;
        }

        public String getExtfld() {
            return extfld;
        }

        public void setExtfld(String extfld) {
            this.extfld = extfld;
        }
    }
}
