package com.bocom.api.request.download;

import com.bocom.api.BizContent;
import com.bocom.api.BocomDownloadRequest;
import com.bocom.api.response.download.HmdMicpFileDownloadResponseV2;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpFileDownloadRequestV2 extends BocomDownloadRequest<HmdMicpFileDownloadResponseV2> {

    @Override
    public Class<HmdMicpFileDownloadResponseV2> getResponseClass() {
        return HmdMicpFileDownloadResponseV2.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpFileDownloadRequestV2Biz.class;
    }

    public static class HmdMicpFileDownloadRequestV2Biz implements BizContent {

        /**
         * 对账单日�?
         */
        @JsonProperty("check_date")
        private String checkDate;

        /**
         * 扩展�?
         */
        @JsonProperty("ext_fld")
        private String extFld;

        /**
         * 账单类型   PAID-支付成功订单，REFUND-�?款成功订�?
         */
        @JsonProperty("check_type")
        private String checkType;

        /**
         * 医疗机构�?
         */
        @JsonProperty("org_no")
        private String orgNo;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        public String getCheckDate() {
            return checkDate;
        }

        public void setCheckDate(String checkDate) {
            this.checkDate = checkDate;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }

        public String getCheckType() {
            return checkType;
        }

        public void setCheckType(String checkType) {
            this.checkType = checkType;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

    }
}
