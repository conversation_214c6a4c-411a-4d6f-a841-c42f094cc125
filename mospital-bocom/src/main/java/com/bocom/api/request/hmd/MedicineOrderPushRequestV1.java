package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.MedicineOrderPushResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;


public class MedicineOrderPushRequestV1 extends AbstractBocomRequest<MedicineOrderPushResponseV1> {

    @Override
    public Class<MedicineOrderPushResponseV1> getResponseClass() {
        return MedicineOrderPushResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return MedicineOrderPushRequestV1Biz.class;
    }

    public static class MedicineOrderPushRequestV1Biz implements BizContent {

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 城市
         */
        @JsonProperty("city")
        private String city;

        /**
         * 城市编号
         */
        @JsonProperty("city_no")
        private String cityNo;

        /**
         * 订单号
         */
        @JsonProperty("order_no")
        private String orderNo;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 支付流水号
         */
        @JsonProperty("payment_no")
        private String paymentNo;

        /**
         * 订单创建时间
         */
        @JsonProperty("gmt_created")
        private String gmtCreated;

        /**
         * 订单支付时间
         */
        @JsonProperty("gmt_paid")
        private String gmtPaid;

        /**
         * 订单用户支付金额
         */
        @JsonProperty("pay_amount")
        private String payAmount;

        /**
         * 处方图片
         */
        @JsonProperty("recipe_url")
        private String recipeUrl;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        /**
         * "商品明细"
         */
        @JsonProperty("items")
        private List<Items> items;

        public static class Items {
            /**
             * 商品名称
             */
            @JsonProperty("goods_name")
            private String goodsName;

            /**
             * 金额
             */
            @JsonProperty("price")
            private String price;

            /**
             * 数量
             */
            @JsonProperty("quantity")
            private String quantity;

            /**
             * 批准文号
             */
            @JsonProperty("approval_number")
            private String approvalNumber;

            /**
             * 病种名称
             */
            @JsonProperty("disease_name")
            private String diseaseName;

            /**
             * 备注
             */
            @JsonProperty("rmk")
            private String rmk;

            public String getGoodsName() {
                return goodsName;
            }

            public void setGoodsName(String goodsName) {
                this.goodsName = goodsName;
            }

            public String getPrice() {
                return price;
            }

            public void setPrice(String price) {
                this.price = price;
            }

            public String getQuantity() {
                return quantity;
            }

            public void setQuantity(String quantity) {
                this.quantity = quantity;
            }

            public String getApprovalNumber() {
                return approvalNumber;
            }

            public void setApprovalNumber(String approvalNumber) {
                this.approvalNumber = approvalNumber;
            }

            public String getDiseaseName() {
                return diseaseName;
            }

            public void setDiseaseName(String diseaseName) {
                this.diseaseName = diseaseName;
            }

            public String getRmk() {
                return rmk;
            }

            public void setRmk(String rmk) {
                this.rmk = rmk;
            }
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getCityNo() {
            return cityNo;
        }

        public void setCityNo(String cityNo) {
            this.cityNo = cityNo;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getPaymentNo() {
            return paymentNo;
        }

        public void setPaymentNo(String paymentNo) {
            this.paymentNo = paymentNo;
        }

        public String getGmtCreated() {
            return gmtCreated;
        }

        public void setGmtCreated(String gmtCreated) {
            this.gmtCreated = gmtCreated;
        }

        public String getGmtPaid() {
            return gmtPaid;
        }

        public void setGmtPaid(String gmtPaid) {
            this.gmtPaid = gmtPaid;
        }

        public String getPayAmount() {
            return payAmount;
        }

        public void setPayAmount(String payAmount) {
            this.payAmount = payAmount;
        }

        public String getRecipeUrl() {
            return recipeUrl;
        }

        public void setRecipeUrl(String recipeUrl) {
            this.recipeUrl = recipeUrl;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }

        public List<Items> getItems() {
            return items;
        }

        public void setItems(List<Items> items) {
            this.items = items;
        }
    }
}
