package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.QryUserTokenAndCityInfoResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class QryUserTokenAndCityInfoRequestV1 extends AbstractBocomRequest<QryUserTokenAndCityInfoResponseV1> {

    @Override
    public Class<QryUserTokenAndCityInfoResponseV1> getResponseClass() {
        return QryUserTokenAndCityInfoResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return QryUserTokenAndCityInfoRequestV1Biz.class;
    }

    public static class QryUserTokenAndCityInfoRequestV1Biz implements BizContent {

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        /**
         * 城市编号
         */
        @JsonProperty("city_no")
        private String cityNo;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getCityNo() {
            return cityNo;
        }

        public void setCityNo(String cityNo) {
            this.cityNo = cityNo;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }
    }
}
