package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpCancelContractResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpCancelContractRequestV1 extends AbstractBocomRequest<HmdMicpCancelContractResponseV1> {

    @Override
    public Class<HmdMicpCancelContractResponseV1> getResponseClass() {
        return HmdMicpCancelContractResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpCancelContractRequestV1Biz.class;
    }

    public static class HmdMicpCancelContractRequestV1Biz implements BizContent {

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 用户姓名
         */
        @JsonProperty("name")
        private String name;

        /**
         * 渠道ID
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }
    }
}
