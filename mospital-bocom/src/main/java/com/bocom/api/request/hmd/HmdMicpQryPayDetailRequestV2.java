package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpQryPayDetailResponseV2;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryPayDetailRequestV2 extends AbstractBocomRequest<HmdMicpQryPayDetailResponseV2> {

    @Override
    public Class<HmdMicpQryPayDetailResponseV2> getResponseClass() {
        return HmdMicpQryPayDetailResponseV2.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpQryPayDetailRequestV2Biz.class;
    }

    public static class HmdMicpQryPayDetailRequestV2Biz implements BizContent {

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        /**
         * 支付流水号
         */
        @JsonProperty("pay_id")
        private String payId;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }
    }
}
