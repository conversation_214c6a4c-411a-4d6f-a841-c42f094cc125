package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpQryRefundDetailResponseV2;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryRefundDetailRequestV2 extends AbstractBocomRequest<HmdMicpQryRefundDetailResponseV2> {

    @Override
    public Class<HmdMicpQryRefundDetailResponseV2> getResponseClass() {
        return HmdMicpQryRefundDetailResponseV2.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpQryRefundDetailRequestV2Biz.class;
    }

    public static class HmdMicpQryRefundDetailRequestV2Biz implements BizContent {

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        /**
         * 退款流水号
         */
        @JsonProperty("refund_id")
        private String refundId;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }

        public String getRefundId() {
            return refundId;
        }

        public void setRefundId(String refundId) {
            this.refundId = refundId;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }
    }
}
