package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpRefundResponseV3;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpRefundRequestV3 extends AbstractBocomRequest<HmdMicpRefundResponseV3> {

    @Override
    public Class<HmdMicpRefundResponseV3> getResponseClass() {
        return HmdMicpRefundResponseV3.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpRefundRequestV3Biz.class;
    }

    public static class HmdMicpRefundRequestV3Biz implements BizContent {

        /**
         * 退款流水号
         */
        @JsonProperty("refund_id")
        private String refundId;

        /**
         * 支付流水号
         */
        @JsonProperty("pay_id")
        private String payId;

        /**
         * 请求时间
         */
        @JsonProperty("req_time")
        private String reqTime;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医疗机构号
         */
        @JsonProperty("org_no")
        private String orgNo;

        /**
         * 医保地区码
         */
        @JsonProperty("are_no")
        private String areNo;

        /**
         * 退款金额
         */
        @JsonProperty("refund_fee")
        private String refundFee;

        /**
         * 退款明细信息
         */
        @JsonProperty("refund_detail")
        private String refundDetail;

        /**
         * 通知地址
         */
        @JsonProperty("notify_url")
        private String notifyUrl;

        /**
         * 扩展域
         */
        @JsonProperty("ext_fld")
        private String extFld;

        public String getRefundId() {
            return refundId;
        }

        public void setRefundId(String refundId) {
            this.refundId = refundId;
        }

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getReqTime() {
            return reqTime;
        }

        public void setReqTime(String reqTime) {
            this.reqTime = reqTime;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getAreNo() {
            return areNo;
        }

        public void setAreNo(String areNo) {
            this.areNo = areNo;
        }

        public String getRefundFee() {
            return refundFee;
        }

        public void setRefundFee(String refundFee) {
            this.refundFee = refundFee;
        }

        public String getRefundDetail() {
            return refundDetail;
        }

        public void setRefundDetail(String refundDetail) {
            this.refundDetail = refundDetail;
        }

        public String getNotifyUrl() {
            return notifyUrl;
        }

        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }

        public String getExtFld() {
            return extFld;
        }

        public void setExtFld(String extFld) {
            this.extFld = extFld;
        }
    }
}
