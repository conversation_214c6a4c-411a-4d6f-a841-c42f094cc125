package com.bocom.api.request.hmd;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.hmd.HmdMicpPayResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpPayRequestV1 extends AbstractBocomRequest<HmdMicpPayResponseV1> {

    @Override
    public Class<HmdMicpPayResponseV1> getResponseClass() {
        return HmdMicpPayResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return HmdMicpPayRequestV1Biz.class;
    }

    public static class HmdMicpPayRequestV1Biz implements BizContent {

        /**
         * 订单详情
         */
        @JsonProperty("bill_detail")
        private String billDetail;

        /**
         * 业务票据编号
         */
        @JsonProperty("bill_no")
        private String billNo;

        /**
         * 请求时间
         */
        @JsonProperty("req_time")
        private String reqTime;

        /**
         * 医保地区码
         */
        @JsonProperty("area_no")
        private String areaNo;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 订单类型
         */
        @JsonProperty("bill_type")
        private String billType;

        /**
         * 用户id
         */
        @JsonProperty("use_id")
        private String useId;

        /**
         * 支付金额
         */
        @JsonProperty("pay_fee")
        private String payFee;

        /**
         * 支付流水号
         */
        @JsonProperty("pay_id")
        private String payId;

        /**
         * 医疗机构号
         */
        @JsonProperty("org_no")
        private String orgNo;

        /**
         * 签约渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 医疗机构名称
         */
        @JsonProperty("hospital_name")
        private String hospitalName;

        public String getBillDetail() {
            return billDetail;
        }

        public void setBillDetail(String billDetail) {
            this.billDetail = billDetail;
        }

        public String getBillNo() {
            return billNo;
        }

        public void setBillNo(String billNo) {
            this.billNo = billNo;
        }

        public String getReqTime() {
            return reqTime;
        }

        public void setReqTime(String reqTime) {
            this.reqTime = reqTime;
        }

        public String getAreaNo() {
            return areaNo;
        }

        public void setAreaNo(String areaNo) {
            this.areaNo = areaNo;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getBillType() {
            return billType;
        }

        public void setBillType(String billType) {
            this.billType = billType;
        }

        public String getUseId() {
            return useId;
        }

        public void setUseId(String useId) {
            this.useId = useId;
        }

        public String getPayFee() {
            return payFee;
        }

        public void setPayFee(String payFee) {
            this.payFee = payFee;
        }

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getOrgNo() {
            return orgNo;
        }

        public void setOrgNo(String orgNo) {
            this.orgNo = orgNo;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getHospitalName() {
            return hospitalName;
        }

        public void setHospitalName(String hospitalName) {
            this.hospitalName = hospitalName;
        }
    }
}
