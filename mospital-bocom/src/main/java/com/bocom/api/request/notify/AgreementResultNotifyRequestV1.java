package com.bocom.api.request.notify;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.notify.AgreementResultNotifyResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class AgreementResultNotifyRequestV1 extends AbstractBocomRequest<AgreementResultNotifyResponseV1> {

    @Override
    public Class<AgreementResultNotifyResponseV1> getResponseClass() {
        return AgreementResultNotifyResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return AgreementResultNotifyRequestV1Biz.class;
    }

    public static class AgreementResultNotifyRequestV1Biz implements BizContent {

        /**
         * 该参数必输，为通知第三方的URL.
         */
        @JsonProperty("notify_url")
        private String notifyUrl;

        /**
         * 医保信用支付协议号
         */
        @JsonProperty("agreement_no")
        private String agreementNo;

        /**
         * 用户id
         */
        @JsonProperty("use_id")
        private String useId;

        /**
         * 证件号
         */
        @JsonProperty("id_no")
        private String idNo;

        /**
         * 证件类型
         */
        @JsonProperty("id_typ")
        private String idTyp;

        /**
         * 姓名
         */
        @JsonProperty("nme")
        private String nme;

        /**
         * 渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 签约状态
         */
        @JsonProperty("agreement_sts")
        private String agreementSts;

        /**
         * 签约时间
         */
        @JsonProperty("sign_time")
        private String signTime;

        public String getNotifyUrl() {
            return notifyUrl;
        }

        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }

        public String getAgreementNo() {
            return agreementNo;
        }

        public void setAgreementNo(String agreementNo) {
            this.agreementNo = agreementNo;
        }

        public String getUseId() {
            return useId;
        }

        public void setUseId(String useId) {
            this.useId = useId;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getNme() {
            return nme;
        }

        public void setNme(String nme) {
            this.nme = nme;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getAgreementSts() {
            return agreementSts;
        }

        public void setAgreementSts(String agreementSts) {
            this.agreementSts = agreementSts;
        }

        public String getSignTime() {
            return signTime;
        }

        public void setSignTime(String signTime) {
            this.signTime = signTime;
        }

        @Override
        public String toString() {
            return "AgreementResultNotifyRequestV1Biz{" +
                    "notifyUrl='" + notifyUrl + '\'' +
                    ", agreementNo='" + agreementNo + '\'' +
                    ", useId='" + useId + '\'' +
                    ", idNo='" + idNo + '\'' +
                    ", idTyp='" + idTyp + '\'' +
                    ", nme='" + nme + '\'' +
                    ", channelId='" + channelId + '\'' +
                    ", agreementSts='" + agreementSts + '\'' +
                    ", signTime='" + signTime + '\'' +
                    '}';
        }
    }
}
