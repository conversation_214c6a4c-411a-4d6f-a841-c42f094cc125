package com.bocom.api.request.notify;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.notify.PayResultNotifyResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class PayResultNotifyRequestV1 extends AbstractBocomRequest<PayResultNotifyResponseV1> {

    @Override
    public Class<PayResultNotifyResponseV1> getResponseClass() {
        return PayResultNotifyResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return PayResultNotifyRequestV1Biz.class;
    }

    public static class PayResultNotifyRequestV1Biz implements BizContent {

        /**
         * 该参数必输，为通知第三方的URL.
         */
        @JsonProperty("notify_url")
        private String notifyUrl;

        /**
         * 支付流水号
         */
        @JsonProperty("pay_id")
        private String payId;

        /**
         * 渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 支付金额
         */
        @JsonProperty("pay_fee")
        private String payFee;

        /**
         * 支付状态
         */
        @JsonProperty("pay_sts")
        private String paySts;

        /**
         * 银行支付完成时
         * 间
         */
        @JsonProperty("pay_time")
        private String payTime;

        /**
         * 银行支付流水号
         */
        @JsonProperty("pay_sqn")
        private String paySqn;

        /**
         * 优惠金额
         */
        @JsonProperty("discount_fee")
        private String discountFee;

        /**
         * 实付金额
         */
        @JsonProperty("actual_fee")
        private String actualFee;

        public String getNotifyUrl() {
            return notifyUrl;
        }

        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getPayFee() {
            return payFee;
        }

        public void setPayFee(String payFee) {
            this.payFee = payFee;
        }

        public String getPaySts() {
            return paySts;
        }

        public void setPaySts(String paySts) {
            this.paySts = paySts;
        }

        public String getPayTime() {
            return payTime;
        }

        public void setPayTime(String payTime) {
            this.payTime = payTime;
        }

        public String getPaySqn() {
            return paySqn;
        }

        public void setPaySqn(String paySqn) {
            this.paySqn = paySqn;
        }

        public String getDiscountFee() {
            return discountFee;
        }

        public void setDiscountFee(String discountFee) {
            this.discountFee = discountFee;
        }

        public String getActualFee() {
            return actualFee;
        }

        public void setActualFee(String actualFee) {
            this.actualFee = actualFee;
        }


    }
}
