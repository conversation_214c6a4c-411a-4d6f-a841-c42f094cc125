package com.bocom.api.request.notify;

import com.bocom.api.AbstractBocomRequest;
import com.bocom.api.BizContent;
import com.bocom.api.response.notify.RefundResultNotifyResponseV1;
import com.fasterxml.jackson.annotation.JsonProperty;


public class RefundResultNotifyRequestV1 extends AbstractBocomRequest<RefundResultNotifyResponseV1> {

    @Override
    public Class<RefundResultNotifyResponseV1> getResponseClass() {
        return RefundResultNotifyResponseV1.class;
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getMethod() {
        return "POST";
    }

    @Override
    public Class<? extends BizContent> getBizContentClass() {
        return RefundResultNotifyRequestV1Biz.class;
    }

    public static class RefundResultNotifyRequestV1Biz implements BizContent {

        /**
         * 该参数必输，为通知第三方的URL.
         */
        @JsonProperty("notify_url")
        private String notifyUrl;

        /**
         * 退款流水号
         */
        @JsonProperty("refund_id")
        private String refundId;

        /**
         * 渠道
         */
        @JsonProperty("channel_id")
        private String channelId;

        /**
         * 支付流水号
         */
        @JsonProperty("pay_id")
        private String payId;

        /**
         * 退款金额
         */
        @JsonProperty("refund_fee")
        private String refundFee;

        /**
         * 退款状态
         */
        @JsonProperty("refund_sts")
        private String refundSts;

        /**
         * 银行退款完成时间
         */
        @JsonProperty("refund_time")
        private String refundTime;

        /**
         * 银行退款流水号
         */
        @JsonProperty("refund_sqn")
        private String refundSqn;

        /**
         * 优惠退款金额
         */
        @JsonProperty("discount_refund_fee")
        private String discountRefundFee;

        /**
         * 实付退款金额
         */
        @JsonProperty("actual_refund_fee")
        private String actualRefundFee;

        public String getNotifyUrl() {
            return notifyUrl;
        }

        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }

        public String getRefundId() {
            return refundId;
        }

        public void setRefundId(String refundId) {
            this.refundId = refundId;
        }

        public String getChannelId() {
            return channelId;
        }

        public void setChannelId(String channelId) {
            this.channelId = channelId;
        }

        public String getPayId() {
            return payId;
        }

        public void setPayId(String payId) {
            this.payId = payId;
        }

        public String getRefundFee() {
            return refundFee;
        }

        public void setRefundFee(String refundFee) {
            this.refundFee = refundFee;
        }

        public String getRefundSts() {
            return refundSts;
        }

        public void setRefundSts(String refundSts) {
            this.refundSts = refundSts;
        }

        public String getRefundTime() {
            return refundTime;
        }

        public void setRefundTime(String refundTime) {
            this.refundTime = refundTime;
        }

        public String getRefundSqn() {
            return refundSqn;
        }

        public void setRefundSqn(String refundSqn) {
            this.refundSqn = refundSqn;
        }

        public String getDiscountRefundFee() {
            return discountRefundFee;
        }

        public void setDiscountRefundFee(String discountRefundFee) {
            this.discountRefundFee = discountRefundFee;
        }

        public String getActualRefundFee() {
            return actualRefundFee;
        }

        public void setActualRefundFee(String actualRefundFee) {
            this.actualRefundFee = actualRefundFee;
        }


    }
}
