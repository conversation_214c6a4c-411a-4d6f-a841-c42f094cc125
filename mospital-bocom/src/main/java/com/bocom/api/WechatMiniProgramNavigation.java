package com.bocom.api;

import java.util.Locale;

@SuppressWarnings("unused")
public class WechatMiniProgramNavigation {

    private static final String BASE_PATH = "package1/pages/hmHospitalLoan/homepage/homepage";

    public enum EnvVersion {
        TRIAL("wx90306cc2e7ae9567"),
        RELEASE("wxd04128eb6f38a0c7");

        private final String appId;

        EnvVersion(String appId) {
            this.appId = appId;
        }

        public String getAppId() {
            return appId;
        }
    }

    private final String appId;
    private final String envVersion;
    private final String path;

    private static String buildPath(String channelId, String code) {
        return BASE_PATH + "?channelId=" + channelId + "&code=" + code;
    }

    @SuppressWarnings("unused")
    public WechatMiniProgramNavigation(EnvVersion envVersion, String channelId, String code) {
        this.appId = envVersion.getAppId();
        this.envVersion = envVersion.name().toLowerCase(Locale.ENGLISH);
        this.path = buildPath(channelId, code);
    }

    @SuppressWarnings("unused")
    public String getAppId() {
        return appId;
    }

    @SuppressWarnings("unused")
    public String getEnvVersion() {
        return envVersion;
    }

    @SuppressWarnings("unused")
    public String getPath() {
        return path;
    }

}
