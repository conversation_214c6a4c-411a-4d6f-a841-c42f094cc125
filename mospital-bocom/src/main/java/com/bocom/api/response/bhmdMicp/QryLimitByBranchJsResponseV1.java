package com.bocom.api.response.bhmdMicp;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class QryLimitByBranchJsResponseV1 extends BocomResponse {
    /**
     * 额度状态
     */
    @JsonProperty("lmt_sts")
    private String lmtSts;

    /**
     * 总额度
     */
    @JsonProperty("tot_lmt")
    private String totLmt;

    /**
     * 可用额度
     */
    @JsonProperty("available_lmt")
    private String availableLmt;

    /**
     * 扩展域
     */
    @JsonProperty("extfld")
    private String extfld;

    public String getLmtSts() {
        return lmtSts;
    }

    public void setLmtSts(String lmtSts) {
        this.lmtSts = lmtSts;
    }

    public String getTotLmt() {
        return totLmt;
    }

    public void setTotLmt(String totLmt) {
        this.totLmt = totLmt;
    }

    public String getAvailableLmt() {
        return availableLmt;
    }

    public void setAvailableLmt(String availableLmt) {
        this.availableLmt = availableLmt;
    }

    public String getExtfld() {
        return extfld;
    }

    public void setExtfld(String extfld) {
        this.extfld = extfld;
    }
}
