package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpPostPayConfirmResponseV1 extends BocomResponse {
    /**
     * 医后付确认流水号
     */
    @JsonProperty("confirm_id")
    private String confirmId;

    /**
     * 医后付确认金额
     */
    @JsonProperty("confirm_fee")
    private String confirmFee;

    /**
     * 确认银行流水号
     */
    @JsonProperty("confirm_sqn")
    private String confirmSqn;

    /**
     * 医后付确认时间
     */
    @JsonProperty("confirm_time")
    private String confirmTime;

    /**
     * 医后付确认状态
     */
    @JsonProperty("confirm_sts")
    private String confirmSts;

    /**
     * 优惠金额
     */
    @JsonProperty("discount_fee")
    private String discountFee;

    /**
     * 实付金额
     */
    @JsonProperty("actual_fee")
    private String actualFee;

    /**
     * 优惠失败原因
     */
    @JsonProperty("discount_fail_reason")
    private String discountFailReason;

    /**
     * 扩展域
     */
    @JsonProperty("ext_fld")
    private String extFld;

    public String getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(String confirmId) {
        this.confirmId = confirmId;
    }

    public String getConfirmFee() {
        return confirmFee;
    }

    public void setConfirmFee(String confirmFee) {
        this.confirmFee = confirmFee;
    }

    public String getConfirmSqn() {
        return confirmSqn;
    }

    public void setConfirmSqn(String confirmSqn) {
        this.confirmSqn = confirmSqn;
    }

    public String getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getConfirmSts() {
        return confirmSts;
    }

    public void setConfirmSts(String confirmSts) {
        this.confirmSts = confirmSts;
    }

    public String getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(String discountFee) {
        this.discountFee = discountFee;
    }

    public String getActualFee() {
        return actualFee;
    }

    public void setActualFee(String actualFee) {
        this.actualFee = actualFee;
    }

    public String getDiscountFailReason() {
        return discountFailReason;
    }

    public void setDiscountFailReason(String discountFailReason) {
        this.discountFailReason = discountFailReason;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }
}
