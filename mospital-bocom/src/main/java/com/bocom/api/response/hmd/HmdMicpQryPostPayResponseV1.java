package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpQryPostPayResponseV1 extends BocomResponse {
    /**
     * 医后付锁定流水号
     */
    @JsonProperty("lock_id")
    private String lockId;

    /**
     * 锁定银行流水号
     */
    @JsonProperty("lock_sqn")
    private String lockSqn;

    /**
     * 医后付锁定金额
     */
    @JsonProperty("lock_fee")
    private String lockFee;

    /**
     * 医后付锁定时间
     */
    @JsonProperty("lock_time")
    private String lockTime;

    /**
     * 医后付锁定状态
     */
    @JsonProperty("lock_sts")
    private String lockSts;

    /**
     * 医后付确认流水号
     */
    @JsonProperty("confirm_id")
    private String confirmId;

    /**
     * 医后付确认金额
     */
    @JsonProperty("confirm_fee")
    private String confirmFee;

    /**
     * 医后付确认时间
     */
    @JsonProperty("confirm_time")
    private String confirmTime;

    /**
     * 医后付确认状态
     */
    @JsonProperty("confirm_sts")
    private String confirmSts;

    /**
     * 确认银行流水号
     */
    @JsonProperty("confirm_sqn")
    private String confirmSqn;

    /**
     * 取消锁定时间
     */
    @JsonProperty("cancel_time")
    private String cancelTime;

    /**
     * 取消锁定状态
     */
    @JsonProperty("cancel_sts")
    private String cancelSts;

    /**
     * 取消银行流水号
     */
    @JsonProperty("cancel_sqn")
    private String cancelSqn;

    /**
     * 优惠金额
     */
    @JsonProperty("discount_fee")
    private String discountFee;

    /**
     * 实付金额
     */
    @JsonProperty("actual_fee")
    private String actualFee;

    /**
     * 扩展域
     */
    @JsonProperty("ext_fld")
    private String extFld;

    public String getLockId() {
        return lockId;
    }

    public void setLockId(String lockId) {
        this.lockId = lockId;
    }

    public String getLockSqn() {
        return lockSqn;
    }

    public void setLockSqn(String lockSqn) {
        this.lockSqn = lockSqn;
    }

    public String getLockFee() {
        return lockFee;
    }

    public void setLockFee(String lockFee) {
        this.lockFee = lockFee;
    }

    public String getLockTime() {
        return lockTime;
    }

    public void setLockTime(String lockTime) {
        this.lockTime = lockTime;
    }

    public String getLockSts() {
        return lockSts;
    }

    public void setLockSts(String lockSts) {
        this.lockSts = lockSts;
    }

    public String getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(String confirmId) {
        this.confirmId = confirmId;
    }

    public String getConfirmFee() {
        return confirmFee;
    }

    public void setConfirmFee(String confirmFee) {
        this.confirmFee = confirmFee;
    }

    public String getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getConfirmSts() {
        return confirmSts;
    }

    public void setConfirmSts(String confirmSts) {
        this.confirmSts = confirmSts;
    }

    public String getConfirmSqn() {
        return confirmSqn;
    }

    public void setConfirmSqn(String confirmSqn) {
        this.confirmSqn = confirmSqn;
    }

    public String getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(String cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getCancelSts() {
        return cancelSts;
    }

    public void setCancelSts(String cancelSts) {
        this.cancelSts = cancelSts;
    }

    public String getCancelSqn() {
        return cancelSqn;
    }

    public void setCancelSqn(String cancelSqn) {
        this.cancelSqn = cancelSqn;
    }

    public String getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(String discountFee) {
        this.discountFee = discountFee;
    }

    public String getActualFee() {
        return actualFee;
    }

    public void setActualFee(String actualFee) {
        this.actualFee = actualFee;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }
}
