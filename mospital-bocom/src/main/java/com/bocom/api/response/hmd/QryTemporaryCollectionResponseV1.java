package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class QryTemporaryCollectionResponseV1 extends BocomResponse {
    /**
     * 暂收总金额
     */
    @JsonProperty("temporary_collection")
    private String temporaryCollection;

    /**
     * 待清算金额
     */
    @JsonProperty("undetermined_settle_amt")
    private String undeterminedSettleAmt;

    /**
     * 退款备用金
     */
    @JsonProperty("refund_reserve_fund")
    private String refundReserveFund;

    /**
     * 扩展域
     */
    @JsonProperty("extfld")
    private String extfld;

    public String getTemporaryCollection() {
        return temporaryCollection;
    }

    public void setTemporaryCollection(String temporaryCollection) {
        this.temporaryCollection = temporaryCollection;
    }

    public String getUndeterminedSettleAmt() {
        return undeterminedSettleAmt;
    }

    public void setUndeterminedSettleAmt(String undeterminedSettleAmt) {
        this.undeterminedSettleAmt = undeterminedSettleAmt;
    }

    public String getRefundReserveFund() {
        return refundReserveFund;
    }

    public void setRefundReserveFund(String refundReserveFund) {
        this.refundReserveFund = refundReserveFund;
    }

    public String getExtfld() {
        return extfld;
    }

    public void setExtfld(String extfld) {
        this.extfld = extfld;
    }
}
