package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpPostPayCancelResponseV1 extends BocomResponse {
    /**
     * 医后付锁定流水号
     */
    @JsonProperty("lock_id")
    private String lockId;

    /**
     * 取消银行流水号
     */
    @JsonProperty("cancel_sqn")
    private String cancelSqn;

    /**
     * 取消锁定时间
     */
    @JsonProperty("cancel_time")
    private String cancelTime;

    /**
     * 取消锁定状态
     */
    @JsonProperty("cancel_sts")
    private String cancelSts;

    /**
     * 取消锁定金额
     */
    @JsonProperty("cancel_fee")
    private String cancelFee;

    /**
     * 扩展域
     */
    @JsonProperty("ext_fld")
    private String extFld;

    public String getLockId() {
        return lockId;
    }

    public void setLockId(String lockId) {
        this.lockId = lockId;
    }

    public String getCancelSqn() {
        return cancelSqn;
    }

    public void setCancelSqn(String cancelSqn) {
        this.cancelSqn = cancelSqn;
    }

    public String getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(String cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getCancelSts() {
        return cancelSts;
    }

    public void setCancelSts(String cancelSts) {
        this.cancelSts = cancelSts;
    }

    public String getCancelFee() {
        return cancelFee;
    }

    public void setCancelFee(String cancelFee) {
        this.cancelFee = cancelFee;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }
}
