package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpQryRefundDetailResponseV1 extends BocomResponse {
    /**
     * 交易号
     */
    @JsonProperty("trans_code")
    private String transCode;

    /**
     * 请求时间
     */
    @JsonProperty("reqTme")
    private String reqtme;

    /**
     * 接出方式
     */
    @JsonProperty("transport_name")
    private String transportName;

    /**
     * 交易渠道
     */
    @JsonProperty("chn")
    private String chn;

    /**
     * 分行号
     */
    @JsonProperty("bk")
    private String bk;

    /**
     * 跟踪号
     */
    @JsonProperty("trace_no")
    private String traceNo;

    /**
     * 版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 退款流水号
     */
    @JsonProperty("refund_id")
    private String refundId;

    /**
     * 机构号
     */
    @JsonProperty("br")
    private String br;

    /**
     * 目标系统码
     */
    @JsonProperty("sys_cde")
    private String sysCde;

    /**
     * 柜员号
     */
    @JsonProperty("tlr")
    private String tlr;

    /**
     * 授权原因码表
     */
    @JsonProperty("authResnTbl")
    private String authresntbl;

    /**
     * 签约渠道
     */
    @JsonProperty("channel_id")
    private String channelId;

    /**
     * 请求流水号
     */
    @JsonProperty("reqJrnNo")
    private String reqjrnno;

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getReqtme() {
        return reqtme;
    }

    public void setReqtme(String reqtme) {
        this.reqtme = reqtme;
    }

    public String getTransportName() {
        return transportName;
    }

    public void setTransportName(String transportName) {
        this.transportName = transportName;
    }

    public String getChn() {
        return chn;
    }

    public void setChn(String chn) {
        this.chn = chn;
    }

    public String getBk() {
        return bk;
    }

    public void setBk(String bk) {
        this.bk = bk;
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getBr() {
        return br;
    }

    public void setBr(String br) {
        this.br = br;
    }

    public String getSysCde() {
        return sysCde;
    }

    public void setSysCde(String sysCde) {
        this.sysCde = sysCde;
    }

    public String getTlr() {
        return tlr;
    }

    public void setTlr(String tlr) {
        this.tlr = tlr;
    }

    public String getAuthresntbl() {
        return authresntbl;
    }

    public void setAuthresntbl(String authresntbl) {
        this.authresntbl = authresntbl;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getReqjrnno() {
        return reqjrnno;
    }

    public void setReqjrnno(String reqjrnno) {
        this.reqjrnno = reqjrnno;
    }
}
