package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class QryLoanBandingInfoResponseV1 extends BocomResponse {
    /**
     * 贷款产品名称
     */
    @JsonProperty("ln_prd_nme")
    private String lnPrdNme;

    /**
     * 医保信用支付协议号
     */
    @JsonProperty("agreement_no")
    private String agreementNo;

    /**
     * 签约状态
     */
    @JsonProperty("agreement_sts")
    private String agreementSts;

    /**
     * 总额度
     */
    @JsonProperty("tot_lmt")
    private String totLmt;

    /**
     * 还款日
     */
    @JsonProperty("per_rep_dte")
    private String perRepDte;

    /**
     * 已用额度
     */
    @JsonProperty("amt1")
    private String amt1;

    /**
     * 可用额度
     */
    @JsonProperty("hus_amt")
    private String husAmt;

    /**
     * 额度状态
     */
    @JsonProperty("lmt_sts")
    private String lmtSts;

    /**
     * 放款总金额
     */
    @JsonProperty("mln_tot_amt")
    private String mlnTotAmt;

    /**
     * 下一还款日
     */
    @JsonProperty("nxt_int_dte")
    private String nxtIntDte;

    /**
     * 应还款金额
     */
    @JsonProperty("rpy_amt")
    private String rpyAmt;

    /**
     * 分期后年利率
     */
    @JsonProperty("int_rat")
    private String intRat;

    /**
     * 分期期限
     */
    @JsonProperty("ln_trm")
    private String lnTrm;

    /**
     * 逾期笔数
     */
    @JsonProperty("ovd_cnt")
    private String ovdCnt;

    /**
     * 还款方式
     */
    @JsonProperty("rep_mde")
    private String repMde;

    /**
     * 还款卡号
     */
    @JsonProperty("cus_ac")
    private String cusAc;

    /**
     * 是否绑定
     */
    @JsonProperty("is_banding")
    private String isBanding;

    /**
     * 额度有效期
     */
    @JsonProperty("crd_due_dte")
    private String crdDueDte;

    /**
     * 备注
     */
    @JsonProperty("rmk")
    private String rmk;

    public String getLnPrdNme() {
        return lnPrdNme;
    }

    public void setLnPrdNme(String lnPrdNme) {
        this.lnPrdNme = lnPrdNme;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getAgreementSts() {
        return agreementSts;
    }

    public void setAgreementSts(String agreementSts) {
        this.agreementSts = agreementSts;
    }

    public String getTotLmt() {
        return totLmt;
    }

    public void setTotLmt(String totLmt) {
        this.totLmt = totLmt;
    }

    public String getPerRepDte() {
        return perRepDte;
    }

    public void setPerRepDte(String perRepDte) {
        this.perRepDte = perRepDte;
    }

    public String getAmt1() {
        return amt1;
    }

    public void setAmt1(String amt1) {
        this.amt1 = amt1;
    }

    public String getHusAmt() {
        return husAmt;
    }

    public void setHusAmt(String husAmt) {
        this.husAmt = husAmt;
    }

    public String getLmtSts() {
        return lmtSts;
    }

    public void setLmtSts(String lmtSts) {
        this.lmtSts = lmtSts;
    }

    public String getMlnTotAmt() {
        return mlnTotAmt;
    }

    public void setMlnTotAmt(String mlnTotAmt) {
        this.mlnTotAmt = mlnTotAmt;
    }

    public String getNxtIntDte() {
        return nxtIntDte;
    }

    public void setNxtIntDte(String nxtIntDte) {
        this.nxtIntDte = nxtIntDte;
    }

    public String getRpyAmt() {
        return rpyAmt;
    }

    public void setRpyAmt(String rpyAmt) {
        this.rpyAmt = rpyAmt;
    }

    public String getIntRat() {
        return intRat;
    }

    public void setIntRat(String intRat) {
        this.intRat = intRat;
    }

    public String getLnTrm() {
        return lnTrm;
    }

    public void setLnTrm(String lnTrm) {
        this.lnTrm = lnTrm;
    }

    public String getOvdCnt() {
        return ovdCnt;
    }

    public void setOvdCnt(String ovdCnt) {
        this.ovdCnt = ovdCnt;
    }

    public String getRepMde() {
        return repMde;
    }

    public void setRepMde(String repMde) {
        this.repMde = repMde;
    }

    public String getCusAc() {
        return cusAc;
    }

    public void setCusAc(String cusAc) {
        this.cusAc = cusAc;
    }

    public String getIsBanding() {
        return isBanding;
    }

    public void setIsBanding(String isBanding) {
        this.isBanding = isBanding;
    }

    public String getCrdDueDte() {
        return crdDueDte;
    }

    public void setCrdDueDte(String crdDueDte) {
        this.crdDueDte = crdDueDte;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }
}
