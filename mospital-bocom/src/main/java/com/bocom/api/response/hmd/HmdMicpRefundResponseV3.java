package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpRefundResponseV3 extends BocomResponse {
    /**
     * 退款状态
     */
    @JsonProperty("refund_sts")
    private String refundSts;

    /**
     * 退款金额
     */
    @JsonProperty("refund_fee")
    private String refundFee;

    /**
     * 退款流水号
     */
    @JsonProperty("refund_id")
    private String refundId;

    /**
     * 优惠退款金额
     */
    @JsonProperty("discount_refund_fee")
    private String discountRefundFee;

    /**
     * 实付退款金额
     */
    @JsonProperty("actual_refund_fee")
    private String actualRefundFee;

    /**
     * 退款时间
     */
    @JsonProperty("refund_time")
    private String refundTime;

    /**
     * 银行退款流水号
     */
    @JsonProperty("refund_sqn")
    private String refundSqn;

    /**
     * 扩展域
     */
    @JsonProperty("extfld")
    private String extfld;

    public String getRefundSts() {
        return refundSts;
    }

    public void setRefundSts(String refundSts) {
        this.refundSts = refundSts;
    }

    public String getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(String refundFee) {
        this.refundFee = refundFee;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getDiscountRefundFee() {
        return discountRefundFee;
    }

    public void setDiscountRefundFee(String discountRefundFee) {
        this.discountRefundFee = discountRefundFee;
    }

    public String getActualRefundFee() {
        return actualRefundFee;
    }

    public void setActualRefundFee(String actualRefundFee) {
        this.actualRefundFee = actualRefundFee;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getRefundSqn() {
        return refundSqn;
    }

    public void setRefundSqn(String refundSqn) {
        this.refundSqn = refundSqn;
    }

    public String getExtfld() {
        return extfld;
    }

    public void setExtfld(String extfld) {
        this.extfld = extfld;
    }
}
