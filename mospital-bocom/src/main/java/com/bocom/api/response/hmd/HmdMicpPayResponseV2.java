package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpPayResponseV2 extends BocomResponse {
    /**
     * 交易号
     */
    @JsonProperty("trans_code")
    private String transCode;

    /**
     * 请求时间
     */
    @JsonProperty("reqTme")
    private String reqtme;

    /**
     * 交易类型-细分
     */
    @JsonProperty("pay_type_detail")
    private String payTypeDetail;

    /**
     * 接出方式
     */
    @JsonProperty("transport_name")
    private String transportName;

    /**
     * 分行号
     */
    @JsonProperty("bk")
    private String bk;

    /**
     * 业务票据编号
     */
    @JsonProperty("bill_no")
    private String billNo;

    /**
     * 跟踪号
     */
    @JsonProperty("trace_no")
    private String traceNo;

    /**
     * 机构号
     */
    @JsonProperty("br")
    private String br;

    /**
     * 医保信用支付协议号
     */
    @JsonProperty("agreement_no")
    private String agreementNo;

    /**
     * 订单类型
     */
    @JsonProperty("bill_type")
    private String billType;

    /**
     * 目标系统码
     */
    @JsonProperty("sys_cde")
    private String sysCde;

    /**
     * 交易类型 1-线下 2-线上
     */
    @JsonProperty("pay_type")
    private String payType;

    /**
     * 医疗机构号
     */
    @JsonProperty("org_no")
    private String orgNo;

    /**
     * 授权原因码表
     */
    @JsonProperty("authResnTbl")
    private String authresntbl;

    /**
     * 医疗机构名称
     */
    @JsonProperty("hospital_name")
    private String hospitalName;

    /**
     * 交易渠道
     */
    @JsonProperty("chn")
    private String chn;

    /**
     * 订单详情
     */
    @JsonProperty("bill_detail")
    private String billDetail;

    /**
     * 扩展域
     */
    @JsonProperty("ext_fld")
    private String extFld;

    /**
     * 版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 请求时间
     */
    @JsonProperty("req_time")
    private String reqTime;

    /**
     * 医保地区码
     */
    @JsonProperty("area_no")
    private String areaNo;

    /**
     * 用户id
     */
    @JsonProperty("use_id")
    private String useId;

    /**
     * 支付金额
     */
    @JsonProperty("pay_fee")
    private String payFee;

    /**
     * 柜员号
     */
    @JsonProperty("tlr")
    private String tlr;

    /**
     * 支付流水号
     */
    @JsonProperty("pay_id")
    private String payId;

    /**
     * 签约渠道
     */
    @JsonProperty("channel_id")
    private String channelId;

    /**
     * 请求流水号
     */
    @JsonProperty("reqJrnNo")
    private String reqjrnno;

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getReqtme() {
        return reqtme;
    }

    public void setReqtme(String reqtme) {
        this.reqtme = reqtme;
    }

    public String getPayTypeDetail() {
        return payTypeDetail;
    }

    public void setPayTypeDetail(String payTypeDetail) {
        this.payTypeDetail = payTypeDetail;
    }

    public String getTransportName() {
        return transportName;
    }

    public void setTransportName(String transportName) {
        this.transportName = transportName;
    }

    public String getBk() {
        return bk;
    }

    public void setBk(String bk) {
        this.bk = bk;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getBr() {
        return br;
    }

    public void setBr(String br) {
        this.br = br;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getSysCde() {
        return sysCde;
    }

    public void setSysCde(String sysCde) {
        this.sysCde = sysCde;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getOrgNo() {
        return orgNo;
    }

    public void setOrgNo(String orgNo) {
        this.orgNo = orgNo;
    }

    public String getAuthresntbl() {
        return authresntbl;
    }

    public void setAuthresntbl(String authresntbl) {
        this.authresntbl = authresntbl;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getChn() {
        return chn;
    }

    public void setChn(String chn) {
        this.chn = chn;
    }

    public String getBillDetail() {
        return billDetail;
    }

    public void setBillDetail(String billDetail) {
        this.billDetail = billDetail;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getReqTime() {
        return reqTime;
    }

    public void setReqTime(String reqTime) {
        this.reqTime = reqTime;
    }

    public String getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(String areaNo) {
        this.areaNo = areaNo;
    }

    public String getUseId() {
        return useId;
    }

    public void setUseId(String useId) {
        this.useId = useId;
    }

    public String getPayFee() {
        return payFee;
    }

    public void setPayFee(String payFee) {
        this.payFee = payFee;
    }

    public String getTlr() {
        return tlr;
    }

    public void setTlr(String tlr) {
        this.tlr = tlr;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getReqjrnno() {
        return reqjrnno;
    }

    public void setReqjrnno(String reqjrnno) {
        this.reqjrnno = reqjrnno;
    }
}
