package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class UpdateChannelBdResponseV1 extends BocomResponse {
    /**
     * 医保信用支付协议号
     */
    @JsonProperty("agreement_no")
    private String agreementNo;

    /**
     * 签约状态
     */
    @JsonProperty("agreement_sts")
    private String agreementSts;

    /**
     * 签约渠道
     */
    @JsonProperty("channel_id")
    private String channelId;

    /**
     * 是否绑定
     */
    @JsonProperty("is_banding")
    private String isBanding;

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getAgreementSts() {
        return agreementSts;
    }

    public void setAgreementSts(String agreementSts) {
        this.agreementSts = agreementSts;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getIsBanding() {
        return isBanding;
    }

    public void setIsBanding(String isBanding) {
        this.isBanding = isBanding;
    }
}
