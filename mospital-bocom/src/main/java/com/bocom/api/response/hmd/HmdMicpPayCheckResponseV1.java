package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpPayCheckResponseV1 extends BocomResponse {
    /**
     * 支付状态
     */
    @JsonProperty("limit_flg")
    private String limitFlg;

    /**
     * 扩展域
     */
    @JsonProperty("ext_fld")
    private String extFld;

    public String getLimitFlg() {
        return limitFlg;
    }

    public void setLimitFlg(String limitFlg) {
        this.limitFlg = limitFlg;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }
}
