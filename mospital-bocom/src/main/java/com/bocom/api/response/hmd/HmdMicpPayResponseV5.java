package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpPayResponseV5 extends BocomResponse {
    /**
     * 支付状态
     */
    @JsonProperty("pay_sts")
    private String paySts;

    /**
     * 支付时间
     */
    @JsonProperty("pay_time")
    private String payTime;

    /**
     * 银行支付流水号
     */
    @JsonProperty("pay_sqn")
    private String paySqn;

    /**
     * 支付流水号
     */
    @JsonProperty("pay_id")
    private String payId;

    /**
     * 优惠金额
     */
    @JsonProperty("discount_fee")
    private String discountFee;

    /**
     * 实付金额
     */
    @JsonProperty("actual_fee")
    private String actualFee;

    /**
     * 支付金额
     */
    @JsonProperty("pay_fee")
    private String payFee;

    /**
     * 优惠失败原因
     */
    @JsonProperty("discount_fail_reason")
    private String discountFailReason;

    /**
     * 扩展域
     */
    @JsonProperty("ext_fld")
    private String extFld;

    public String getPaySts() {
        return paySts;
    }

    public void setPaySts(String paySts) {
        this.paySts = paySts;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getPaySqn() {
        return paySqn;
    }

    public void setPaySqn(String paySqn) {
        this.paySqn = paySqn;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(String discountFee) {
        this.discountFee = discountFee;
    }

    public String getActualFee() {
        return actualFee;
    }

    public void setActualFee(String actualFee) {
        this.actualFee = actualFee;
    }

    public String getPayFee() {
        return payFee;
    }

    public void setPayFee(String payFee) {
        this.payFee = payFee;
    }

    public String getDiscountFailReason() {
        return discountFailReason;
    }

    public void setDiscountFailReason(String discountFailReason) {
        this.discountFailReason = discountFailReason;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }
}
