package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;


public class HmdMicpQryPayDetailResponseV2 extends BocomResponse {
    //@FieldDoc(desc = "支付流水号")
    @JsonProperty("pay_id")
    private String payId;

    //@FieldDoc(desc = "支付金额")
    @JsonProperty("pay_fee")
    private String payFee;

    //@FieldDoc(desc = "支付状态")
    @JsonProperty("pay_sts")
    private String paySts;

    //@FieldDoc(desc = "支付时间")
    @JsonProperty("pay_time")
    private String payTime;

    //@FieldDoc(desc = "银行支付流水号")
    @JsonProperty("pay_sqn")
    private String paySqn;

    //@FieldDoc(desc = "医保信用支付协议号")
    @JsonProperty("agreement_no")
    private String agreementNo;

    //@FieldDoc(desc = "医疗机构号")
    @JsonProperty("org_no")
    private String orgNo;

    //@FieldDoc(desc = "业务票据编号")
    @JsonProperty("bill_no")
    private String billNo;

    //@FieldDoc(desc = "优惠金额")
    @JsonProperty("discount_fee")
    private String discountFee;

    //@FieldDoc(desc = "实付金额")
    @JsonProperty("actual_fee")
    private String actualFee;

    //@FieldDoc(desc = "扩展域")
    @JsonProperty("ext_fld")
    private String extFld;


    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }

    public String getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(String discountFee) {
        this.discountFee = discountFee;
    }

    public String getActualFee() {
        return actualFee;
    }

    public void setActualFee(String actualFee) {
        this.actualFee = actualFee;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getOrgNo() {
        return orgNo;
    }

    public void setOrgNo(String orgNo) {
        this.orgNo = orgNo;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getPayFee() {
        return payFee;
    }

    public void setPayFee(String payFee) {
        this.payFee = payFee;
    }

    public String getPaySts() {
        return paySts;
    }

    public void setPaySts(String paySts) {
        this.paySts = paySts;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getPaySqn() {
        return paySqn;
    }

    public void setPaySqn(String paySqn) {
        this.paySqn = paySqn;
    }

    @Override
    public String toString() {
        return "HmdMicpQryPayDetailResponseV2 [payId=" + payId + ", payFee=" + payFee + ", paySts=" + paySts + ", payTime="
                + payTime + ", paySqn=" + paySqn + ", agreementNo=" + agreementNo + ", orgNo=" + orgNo + ", billNo="
                + billNo + ", discountFee=" + discountFee + ", actualFee=" + actualFee + ", extFld=" + extFld + "]";
    }

}
