package com.bocom.api.response.download;

import com.bocom.api.BocomApiException;
import com.bocom.api.BocomResponse;
import com.bocom.api.utils.BocomDigest;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;

public class HmdMicpFileDownloadResponseV2 extends BocomResponse {

    private static final Logger log = LoggerFactory.getLogger(HmdMicpFileDownloadResponseV2.class);

    @JsonProperty("file_hashcode")
    private String fileHashcode;

    @JsonProperty("file_name")
    private String fileName;

    private String filePath;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileHashcode() {
        return fileHashcode;
    }

    public void setFileHashcode(String fileHashcode) {
        this.fileHashcode = fileHashcode;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public boolean isSuccess() {
        if (!super.isSuccess()) {
            return false;
        }
        String tempFileName = fileName + ".TMP";
        File file = new File(filePath, tempFileName);
        try {
            String realFileHashcode = BocomDigest.fileDigest(file.getAbsolutePath());
            if (!realFileHashcode.equals(this.fileHashcode)) {
                Files.delete(file.toPath());
                throw new BocomApiException("Bocom fileHashCode digest not passed.");
            }
            return file.renameTo(new File(filePath, fileName));
        } catch (Exception e) {
            log.error("Download file failed.", e);
            return false;
        }
    }
}
