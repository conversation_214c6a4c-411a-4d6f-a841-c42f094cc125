package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpQryRefundDetailResponseV2 extends BocomResponse {
    //@FieldDoc(desc = "退款流水号")
    @JsonProperty("refund_id")
    private String refundId;

    //@FieldDoc(desc = "支付流水号")
    @JsonProperty("pay_id")
    private String payId;

    //@FieldDoc(desc = "退款金额")
    @JsonProperty("refund_fee")
    private String refundFee;

    //@FieldDoc(desc = "退款状态")
    @JsonProperty("refund_sts")
    private String refundSts;

    //@FieldDoc(desc = "退款时间")
    @JsonProperty("refund_time")
    private String refundTime;

    //@FieldDoc(desc = "银行退款流水号")
    @JsonProperty("refund_sqn")
    private String refundSqn;

    //@FieldDoc(desc = "优惠退款金额")
    @JsonProperty("discount_refund_fee")
    private String discountRefundFee;

    //@FieldDoc(desc = "实付退款金额")
    @JsonProperty("actual_refund_fee")
    private String actualRefundFee;

    //@FieldDoc(desc = "扩展域")
    @JsonProperty("ext_fld")
    private String extFld;

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }

    public String getDiscountRefundFee() {
        return discountRefundFee;
    }

    public void setDiscountRefundFee(String discountRefundFee) {
        this.discountRefundFee = discountRefundFee;
    }

    public String getActualRefundFee() {
        return actualRefundFee;
    }

    public void setActualRefundFee(String actualRefundFee) {
        this.actualRefundFee = actualRefundFee;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(String refundFee) {
        this.refundFee = refundFee;
    }

    public String getRefundSts() {
        return refundSts;
    }

    public void setRefundSts(String refundSts) {
        this.refundSts = refundSts;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getRefundSqn() {
        return refundSqn;
    }

    public void setRefundSqn(String refundSqn) {
        this.refundSqn = refundSqn;
    }

    @Override
    public String toString() {
        return "HmdMicpQryRefundDetailResponseV2 [refundId=" + refundId + ", payId=" + payId + ", refundFee=" + refundFee
                + ", refundSts=" + refundSts + ", refundTime=" + refundTime + ", refundSqn=" + refundSqn
                + ", discountRefundFee=" + discountRefundFee + ", actualRefundFee=" + actualRefundFee + ", extFld=" + extFld
                + "]";
    }


}
