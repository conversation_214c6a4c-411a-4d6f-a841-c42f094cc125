package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpQryAgreementResponseV3 extends BocomResponse {


    //@FieldDoc(desc="医保信用支付协议号")
    @JsonProperty("agreement_no")
    private String agreementNo;

    //@FieldDoc(desc="签约渠道")
    @JsonProperty("channel_id")
    private String channelId;

    //@FieldDoc(desc="用户id")
    @JsonProperty("use_id")
    private String useId;

    //@FieldDoc(desc="签约状态")
    @JsonProperty("agreement_sts")
    private String agreementSts;

    //@FieldDoc(desc="信用额度")
    @JsonProperty("credit_amt")
    private String creditAmt;

    //@FieldDoc(desc="签约时间")
    @JsonProperty("sign_time")
    private String signTime;

    //@FieldDoc(desc="优惠标志")
    @JsonProperty("discount_flag")
    private String discountFlag;

    //@FieldDoc(desc="优惠简称")
    @JsonProperty("discount_name")
    private String discountName;

    //@FieldDoc(desc="优惠简介")
    @JsonProperty("discount_detail")
    private String discountDetail;

    //@FieldDoc(desc="优惠链接")
    @JsonProperty("discount_url")
    private String discountUrl;

    //@FieldDoc(desc = "扩展域")
    @JsonProperty("ext_fld")
    private String extFld;


    public String getDiscountFlag() {
        return discountFlag;
    }

    public void setDiscountFlag(String discountFlag) {
        this.discountFlag = discountFlag;
    }

    public String getDiscountName() {
        return discountName;
    }

    public void setDiscountName(String discountName) {
        this.discountName = discountName;
    }

    public String getDiscountDetail() {
        return discountDetail;
    }

    public void setDiscountDetail(String discountDetail) {
        this.discountDetail = discountDetail;
    }

    public String getDiscountUrl() {
        return discountUrl;
    }

    public void setDiscountUrl(String discountUrl) {
        this.discountUrl = discountUrl;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }

    public String getAgreementNo() {
        return this.agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getChannelId() {
        return this.channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getAgreementSts() {
        return this.agreementSts;
    }

    public void setAgreementSts(String agreementSts) {
        this.agreementSts = agreementSts;
    }

    public String getSignTime() {
        return this.signTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }

    public String getUseId() {
        return useId;
    }

    public void setUseId(String useId) {
        this.useId = useId;
    }

    public String getCreditAmt() {
        return creditAmt;
    }

    public void setCreditAmt(String creditAmt) {
        this.creditAmt = creditAmt;
    }

    @Override
    public String toString() {
        return "HmdMicpQryAgreementResponseV2 [agreementNo=" + agreementNo + ", channelId=" + channelId + ", useId="
                + useId + ", agreementSts=" + agreementSts + ", creditAmt=" + creditAmt + ", signTime=" + signTime
                + ", discountFlag=" + discountFlag + ", discountName=" + discountName + ", discountDetail="
                + discountDetail + ", discountUrl=" + discountUrl + ", extFld=" + extFld + "]";
    }


}
