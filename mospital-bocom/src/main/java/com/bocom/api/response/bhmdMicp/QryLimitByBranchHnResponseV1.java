package com.bocom.api.response.bhmdMicp;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class QryLimitByBranchHnResponseV1 extends BocomResponse {
    /**
     * 额度状态
     */
    @JsonProperty("lmt_sts")
    private String lmtSts;

    /**
     * 总额度
     */
    @JsonProperty("tot_lmt")
    private String totLmt;

    /**
     * 可用额度
     */
    @JsonProperty("available_lmt")
    private String availableLmt;

    /**
     * 扩展域
     */
    @JsonProperty("extfld")
    private String extfld;

    /**
     * BBIP业务处理返回流水号
     */
    @JsonProperty("bbipresponsehead_txnsqn")
    private String bbipresponseheadTxnsqn;

    /**
     * 返回待授权LOG标志
     */
    @JsonProperty("bbipresponsehead_oauthlog")
    private String bbipresponseheadOauthlog;

    /**
     * 返回待授权级别
     */
    @JsonProperty("bbipresponsehead_oauthlvl")
    private String bbipresponseheadOauthlvl;

    /**
     * 返回待授权主管1
     */
    @JsonProperty("bbipresponsehead_osup1id")
    private String bbipresponseheadOsup1id;

    /**
     * 返回待授权主管2
     */
    @JsonProperty("bbipresponsehead_osup2id")
    private String bbipresponseheadOsup2id;

    /**
     * 返回授权原因列表
     */
    @JsonProperty("bbipresponsehead_oauthresnlst")
    private String bbipresponseheadOauthresnlst;

    /**
     * 记账会计日期
     */
    @JsonProperty("bbipresponsehead_acdte")
    private String bbipresponseheadAcdte;

    /**
     * 记账系统流水号
     */
    @JsonProperty("bbipresponsehead_acojrnno")
    private String bbipresponseheadAcojrnno;

    /**
     * 记账会计流水号
     */
    @JsonProperty("bbipresponsehead_acovchno")
    private String bbipresponseheadAcovchno;

    /**
     * 第三方系统标识号
     */
    @JsonProperty("bbipresponsehead_tpid")
    private String bbipresponseheadTpid;

    /**
     * 第三方流水号
     */
    @JsonProperty("bbipresponsehead_tpjrnno")
    private String bbipresponseheadTpjrnno;

    /**
     * 第三方对账日期
     */
    @JsonProperty("bbipresponsehead_tprcndte")
    private String bbipresponseheadTprcndte;

    /**
     * 第三方交易时间
     */
    @JsonProperty("bbipresponsehead_tprsptme")
    private String bbipresponseheadTprsptme;

    /**
     * BBIP处理返回时间
     */
    @JsonProperty("bbipresponsehead_rsptme")
    private String bbipresponseheadRsptme;

    public String getLmtSts() {
        return lmtSts;
    }

    public void setLmtSts(String lmtSts) {
        this.lmtSts = lmtSts;
    }

    public String getTotLmt() {
        return totLmt;
    }

    public void setTotLmt(String totLmt) {
        this.totLmt = totLmt;
    }

    public String getAvailableLmt() {
        return availableLmt;
    }

    public void setAvailableLmt(String availableLmt) {
        this.availableLmt = availableLmt;
    }

    public String getExtfld() {
        return extfld;
    }

    public void setExtfld(String extfld) {
        this.extfld = extfld;
    }

    public String getBbipresponseheadTxnsqn() {
        return bbipresponseheadTxnsqn;
    }

    public void setBbipresponseheadTxnsqn(String bbipresponseheadTxnsqn) {
        this.bbipresponseheadTxnsqn = bbipresponseheadTxnsqn;
    }

    public String getBbipresponseheadOauthlog() {
        return bbipresponseheadOauthlog;
    }

    public void setBbipresponseheadOauthlog(String bbipresponseheadOauthlog) {
        this.bbipresponseheadOauthlog = bbipresponseheadOauthlog;
    }

    public String getBbipresponseheadOauthlvl() {
        return bbipresponseheadOauthlvl;
    }

    public void setBbipresponseheadOauthlvl(String bbipresponseheadOauthlvl) {
        this.bbipresponseheadOauthlvl = bbipresponseheadOauthlvl;
    }

    public String getBbipresponseheadOsup1id() {
        return bbipresponseheadOsup1id;
    }

    public void setBbipresponseheadOsup1id(String bbipresponseheadOsup1id) {
        this.bbipresponseheadOsup1id = bbipresponseheadOsup1id;
    }

    public String getBbipresponseheadOsup2id() {
        return bbipresponseheadOsup2id;
    }

    public void setBbipresponseheadOsup2id(String bbipresponseheadOsup2id) {
        this.bbipresponseheadOsup2id = bbipresponseheadOsup2id;
    }

    public String getBbipresponseheadOauthresnlst() {
        return bbipresponseheadOauthresnlst;
    }

    public void setBbipresponseheadOauthresnlst(String bbipresponseheadOauthresnlst) {
        this.bbipresponseheadOauthresnlst = bbipresponseheadOauthresnlst;
    }

    public String getBbipresponseheadAcdte() {
        return bbipresponseheadAcdte;
    }

    public void setBbipresponseheadAcdte(String bbipresponseheadAcdte) {
        this.bbipresponseheadAcdte = bbipresponseheadAcdte;
    }

    public String getBbipresponseheadAcojrnno() {
        return bbipresponseheadAcojrnno;
    }

    public void setBbipresponseheadAcojrnno(String bbipresponseheadAcojrnno) {
        this.bbipresponseheadAcojrnno = bbipresponseheadAcojrnno;
    }

    public String getBbipresponseheadAcovchno() {
        return bbipresponseheadAcovchno;
    }

    public void setBbipresponseheadAcovchno(String bbipresponseheadAcovchno) {
        this.bbipresponseheadAcovchno = bbipresponseheadAcovchno;
    }

    public String getBbipresponseheadTpid() {
        return bbipresponseheadTpid;
    }

    public void setBbipresponseheadTpid(String bbipresponseheadTpid) {
        this.bbipresponseheadTpid = bbipresponseheadTpid;
    }

    public String getBbipresponseheadTpjrnno() {
        return bbipresponseheadTpjrnno;
    }

    public void setBbipresponseheadTpjrnno(String bbipresponseheadTpjrnno) {
        this.bbipresponseheadTpjrnno = bbipresponseheadTpjrnno;
    }

    public String getBbipresponseheadTprcndte() {
        return bbipresponseheadTprcndte;
    }

    public void setBbipresponseheadTprcndte(String bbipresponseheadTprcndte) {
        this.bbipresponseheadTprcndte = bbipresponseheadTprcndte;
    }

    public String getBbipresponseheadTprsptme() {
        return bbipresponseheadTprsptme;
    }

    public void setBbipresponseheadTprsptme(String bbipresponseheadTprsptme) {
        this.bbipresponseheadTprsptme = bbipresponseheadTprsptme;
    }

    public String getBbipresponseheadRsptme() {
        return bbipresponseheadRsptme;
    }

    public void setBbipresponseheadRsptme(String bbipresponseheadRsptme) {
        this.bbipresponseheadRsptme = bbipresponseheadRsptme;
    }
}
