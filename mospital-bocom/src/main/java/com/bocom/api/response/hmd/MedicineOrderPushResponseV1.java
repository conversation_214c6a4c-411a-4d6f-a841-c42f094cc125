package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class MedicineOrderPushResponseV1 extends BocomResponse {
    /**
     * 返回编码
     */
    @JsonProperty("errno")
    private String errno;

    /**
     * 返回描述
     */
    @JsonProperty("error")
    private String error;

    /**
     * 返回数据类型
     */
    @JsonProperty("data_type")
    private String dataType;

    /**
     * 返回数据
     */
    @JsonProperty("data")
    private String data;

    /**
     * 扩展域
     */
    @JsonProperty("ext_fld")
    private String extFld;

    public String getErrno() {
        return errno;
    }

    public void setErrno(String errno) {
        this.errno = errno;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }
}
