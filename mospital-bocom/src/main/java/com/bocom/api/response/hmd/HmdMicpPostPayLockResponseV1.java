package com.bocom.api.response.hmd;

import com.bocom.api.BocomResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

public class HmdMicpPostPayLockResponseV1 extends BocomResponse {
    /**
     * 医后付锁定流水号
     */
    @JsonProperty("lock_id")
    private String lockId;

    /**
     * 锁定银行流水号
     */
    @JsonProperty("lock_sqn")
    private String lockSqn;

    /**
     * 医后付锁定金额
     */
    @JsonProperty("lock_fee")
    private String lockFee;

    /**
     * 医后付锁定时间
     */
    @JsonProperty("lock_time")
    private String lockTime;

    /**
     * 医后付锁定状态
     */
    @JsonProperty("lock_sts")
    private String lockSts;

    /**
     * 扩展域
     */
    @JsonProperty("ext_fld")
    private String extFld;

    public String getLockId() {
        return lockId;
    }

    public void setLockId(String lockId) {
        this.lockId = lockId;
    }

    public String getLockSqn() {
        return lockSqn;
    }

    public void setLockSqn(String lockSqn) {
        this.lockSqn = lockSqn;
    }

    public String getLockFee() {
        return lockFee;
    }

    public void setLockFee(String lockFee) {
        this.lockFee = lockFee;
    }

    public String getLockTime() {
        return lockTime;
    }

    public void setLockTime(String lockTime) {
        this.lockTime = lockTime;
    }

    public String getLockSts() {
        return lockSts;
    }

    public void setLockSts(String lockSts) {
        this.lockSts = lockSts;
    }

    public String getExtFld() {
        return extFld;
    }

    public void setExtFld(String extFld) {
        this.extFld = extFld;
    }
}
