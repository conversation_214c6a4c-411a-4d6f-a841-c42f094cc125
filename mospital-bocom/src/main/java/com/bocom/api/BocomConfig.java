package com.bocom.api;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import org.mospital.jackson.ConfigLoader;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BocomConfig {

    private String myPrivateKey;
    private String apigwPublicKey;
    private String bocomPrdKey;
    private String appId;
    private String apigwUrlAddress;
    private String channelId;
    private String hospitalName;
    private String areaNo;
    private String orgNo;
    private String notifyUrl;
    private String refundNotifyUrl;
    private String downloadPath;
    private String codeGeneratorKey;

    public static final BocomConfig me;

    static {
        try {
            me = ConfigLoader.INSTANCE.loadConfig("bocom.yaml", BocomConfig.class, BocomConfig.class);
        } catch (Exception e) {
            throw new IllegalStateException("Failed to load Bocom configuration", e);
        }
    }

    public String getMyPrivateKey() {
        return myPrivateKey;
    }

    public void setMyPrivateKey(String myPrivateKey) {
        this.myPrivateKey = myPrivateKey;
    }

    public String getApigwPublicKey() {
        return apigwPublicKey;
    }

    public void setApigwPublicKey(String apigwPublicKey) {
        this.apigwPublicKey = apigwPublicKey;
    }

    public String getBocomPrdKey() {
        return bocomPrdKey;
    }

    public void setBocomPrdKey(String bocomPrdKey) {
        this.bocomPrdKey = bocomPrdKey;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getApigwUrlAddress() {
        return apigwUrlAddress;
    }

    public void setApigwUrlAddress(String apigwUrlAddress) {
        this.apigwUrlAddress = apigwUrlAddress;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(String areaNo) {
        this.areaNo = areaNo;
    }

    public String getOrgNo() {
        return orgNo;
    }

    public void setOrgNo(String orgNo) {
        this.orgNo = orgNo;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getRefundNotifyUrl() {
        return refundNotifyUrl;
    }

    public void setRefundNotifyUrl(String refundNotifyUrl) {
        this.refundNotifyUrl = refundNotifyUrl;
    }

    public String getDownloadPath() {
        return downloadPath;
    }

    public void setDownloadPath(String downloadPath) {
        this.downloadPath = downloadPath;
    }

    public String getCodeGeneratorKey() {
        return codeGeneratorKey;
    }

    public void setCodeGeneratorKey(String codeGeneratorKey) {
        this.codeGeneratorKey = codeGeneratorKey;
    }
}
