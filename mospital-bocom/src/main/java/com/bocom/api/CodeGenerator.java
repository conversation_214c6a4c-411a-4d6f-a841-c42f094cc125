package com.bocom.api;

import javax.crypto.*;
import javax.crypto.spec.DESedeKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;

public class CodeGenerator {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(CodeGenerator.class);

    private CodeGenerator() {
    }

    public static class EncryptionException extends RuntimeException {
        public EncryptionException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    private static final String ALGORITHM = "DESede";

    private static final String TRANSFORMATION = "DESede/ECB/PKCS5Padding";

    private static String encrypt(String rawText) {
        try {
            String codeGeneratorKey = BocomConfig.me.getCodeGeneratorKey();
            byte[] keyBytes = codeGeneratorKey.getBytes(StandardCharsets.UTF_8);
            if (keyBytes.length < 24) {
                throw new IllegalArgumentException("Key length is less than 24 bytes: " + codeGeneratorKey);
            }

            DESedeKeySpec keySpec = new DESedeKeySpec(keyBytes);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            SecretKey secretKey = keyFactory.generateSecret(keySpec);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            byte[] encrypted = cipher.doFinal(rawText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (IllegalArgumentException | InvalidKeyException | NoSuchAlgorithmException | InvalidKeySpecException |
                 BadPaddingException | IllegalBlockSizeException | NoSuchPaddingException e) {
            log.error("Encryption failed", e);
            throw new EncryptionException("Encryption failed", e);
        }
    }

    public static String generateCode(String idCardNo, String name, String mobile) {
        String rawText = String.format("%s|%s|%s", idCardNo, name, mobile);
        return encrypt(rawText);
    }

}
