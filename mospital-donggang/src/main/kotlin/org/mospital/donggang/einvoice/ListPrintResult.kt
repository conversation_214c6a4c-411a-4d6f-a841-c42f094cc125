package org.mospital.donggang.einvoice

import org.mospital.common.jaxb.JAXBKit
import jakarta.xml.bind.annotation.*

@XmlRootElement(name = "Voucher")
@XmlAccessorType(XmlAccessType.FIELD)
class ListPrintResult {

    companion object {
        fun fromResponse(response: Response): ListPrintResult =
            if (response.isOk()) {
                try {
                    JAXBKit.unmarshal(ListPrintResult::class.java, response.decryptContent()).apply {
                        this.ok = true
                        this.msg = response.resultMsg
                    }
                } catch (e: Exception) {
                    ListPrintResult().apply {
                        this.ok = false
                        this.msg = e.message ?: "解析XML失败"
                    }
                }
            } else {
                ListPrintResult().apply {
                    this.ok = false
                    this.msg = response.resultMsg
                }
            }
    }

    /**
     * 页码
     */
    @field:XmlElement(name = "Page")
    var pageNumber: Int = 0

    /**
     * 总记录数
     */
    @field:XmlElement(name = "Total")
    var totalCount: Int = 0

    @field:[XmlElementWrapper(name = "EInvoiceDatas") XmlElement(name = "EInvoiceData")]
    var records: MutableList<EInvoice> = mutableListOf()

    var ok = false
    var msg: String = ""

    @XmlRootElement(name = "EInvoiceData")
    @XmlAccessorType(XmlAccessType.FIELD)
    class EInvoice() {

        /**
         * 业务流水号
         */
        @field:XmlElement(name = "SerialNumber")
        var serialNumber: String = ""

        /**
         * 开票点编码
         */
        @field:XmlElement(name = "PlaceCode")
        var placeCode: String = ""

        /**
         * 业务代码：01=门诊，02=住院
         */
        @field:XmlElement(name = "BusinessCode")
        var businessCode: String = ""

        /**
         * 业务发生时间，格式yyyyMMddHHmmss
         */
        @field:XmlElement(name = "BusinessDate")
        var businessTime: String = ""

        /**
         * 票据来源：01=接口开具，02=系统开具
         */
        @field:XmlElement(name = "EinvoiceSourceCode")
        var sourceCode: String = ""

        /**
         * 票据种类
         */
        @field:XmlElement(name = "EinvoiceTypeCode")
        var typeCode: String = ""

        /**
         * 开票类型: 0=正常票据，1=红票，2=被红冲票据，3=正常票据+被红冲票据
         */
        @field:XmlElement(name = "EinvoiceMode")
        var mode: String = ""

        /**
         * 打印状态: 0=未打印，1=已打印纸票
         */
        @field:XmlElement(name = "PrintStatus")
        var printStatus: String = ""

        /**
         * 电子票据代码
         */
        @field:XmlElement(name = "EinvoiceCode")
        var code: String = ""

        /**
         * 电子票据号码
         */
        @field:XmlElement(name = "EinvoiceNumber")
        var number: String = ""

        /**
         * 校验码
         */
        @field:XmlElement(name = "RandomNumber")
        var randomNumber: String = ""

        /**
         * 开票时间，格式yyyyMMddHHmmss
         */
        @field:XmlElement(name = "IssueTime")
        var issueTime: String = ""

        /**
         * 开票时间，格式yyyyMMddHHmmss
         */
        @field:XmlElement(name = "PdfUrl")
        var pdfUrl: String = ""

        /**
         * 原电子票据代码
         * 要红冲的票据的代码，红票显示该项
         */
        @field:XmlElement(name = "RelatedInvoiceCode")
        var relatedCode: String = ""

        /**
         * 原电子票据号码
         * 要红冲的票据的号码，红票显示该项
         */
        @field:XmlElement(name = "RelatedInvoiceNumber")
        var relatedNumber: String = ""

        /**
         * 纸质票据代码
         */
        @field:XmlElement(name = "BillBatchCode")
        var billBatchCode: String = ""

        /**
         * 纸质票据号码
         */
        @field:XmlElement(name = "BillNo")
        var billNo: String = ""

        /**
         * 总金额
         */
        @field:XmlElement(name = "TotalAmount")
        var totalAmount: String = ""

        /**
         * 交款人类型: 1=个人，2=单位
         */
        @field:XmlElement(name = "PayerPartyType")
        var payerType: String = ""

        /**
         * 交款人代码: 单位一般为统一社会信用代码，个人一般为身份证号
         */
        @field:XmlElement(name = "PayerPartyCode")
        var payerCode: String = ""

        /**
         * 交款人姓名: 单位为单位名称，个人为个人姓名
         */
        @field:XmlElement(name = "PayerPartyName")
        var payerName: String = ""

        /**
         * 开票单位名称
         */
        @field:XmlElement(name = "InvoicingPartyName")
        var invoicingPartyName: String = ""

        /**
         * 开票人
         */
        @field:XmlElement(name = "HandlingPerson")
        var handlingPerson: String = ""

        /**
         * 窗口号
         */
        @field:XmlElement(name = "WindowNumber")
        var windowNumber: String = ""

        /**
         * 打印次数
         */
        @field:XmlElement(name = " PrintingTimes")
        var printingTimes: String = ""
    }
}
