package org.mospital.donggang.einvoice

import org.mospital.common.jaxb.JAXBKit
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Voucher")
@XmlAccessorType(XmlAccessType.FIELD)
class ListForm() {

    /**
     * 业务发生时间起，格式yyyyMMddHHmmss
     */
    @field:XmlElement(name = "BusinessDateBgn")
    var businessBeginTime: String = ""

    /**
     * 业务发生时间起，格式yyyyMMddHHmmss
     */
    @field:XmlElement(name = "BusinessDateEnd")
    var businessEndTime: String = ""

    /**
     * 开票发生时间起，格式yyyyMMddHHmmss
     */
    @field:XmlElement(name = "IssueTimeBgn")
    var issueBeginTime: String = ""

    /**
     * 开票发生时间起，格式yyyyMMddHHmmss
     */
    @field:XmlElement(name = "IssueTimeEnd")
    var issueEndTime: String = ""

    /**
     * 开票点编码
     */
    @field:XmlElement(name = "PlaceCode")
    var placeCode: String = ""

    /**
     * 窗口号
     */
    @field:XmlElement(name = "WindowNumber")
    var windowNumber: String = ""

    /**
     * 业务代码：01=门诊，02=住院
     */
    @field:XmlElement(name = "BusinessCode")
    var businessCode: String = ""

    /**
     * 票据来源：01=接口开具，02=系统开具
     */
    @field:XmlElement(name = "EinvoiceSourceCode")
    var sourceCode: String = ""

    /**
     * 票据种类
     */
    @field:XmlElement(name = "EinvoiceTypeCode")
    var typeCode: String = ""

    /**
     * 开票类型: 0=正常票据，1=红票，2=被红冲票据，3=正常票据+被红冲票据
     */
    @field:XmlElement(name = "EinvoiceMode")
    var mode: String = "3"

    /**
     * 打印状态: 0=未打印，1=已打印纸票
     */
    @field:XmlElement(name = "PrintStatus")
    var printStatus: String = ""

    /**
     * 交款人代码: 单位一般为统一社会信用代码，个人一般为身份证号
     */
    @field:XmlElement(name = "PayerPartyCode")
    var payerCode: String = ""

    /**
     * 交款人姓名: 单位为单位名称，个人为个人姓名
     */
    @field:XmlElement(name = "PayerPartyName")
    var payerName: String = ""

    /**
     * 交款人Id: 能标识唯一患者的ID
     */
    @field:XmlElement(name = "PayerPartyId")
    var payerId: String = ""

    /**
     * 电子票据代码
     */
    @field:XmlElement(name = "EinvoiceCode")
    var code: String = ""

    /**
     * 电子票据号码
     */
    @field:XmlElement(name = "EinvoiceNumber")
    var number: String = ""

    /**
     * 总金额
     */
    @field:XmlElement(name = "TotalAmount")
    var totalAmount: String = ""

    /**
     * 调用方应用账号
     */
    @field:XmlElement(name = "AppId")
    var appId: String = ""

    /**
     * 开票人
     */
    @field:XmlElement(name = "HandlingPerson")
    var handlingPerson: String = ""

    /**
     * 备用字段1
     */
    @field:XmlElement(name = "Back1")
    var ext: String = ""

    /**
     * 页码
     */
    @field:XmlElement(name = "Page")
    var pageNumber: Int = 1

    /**
     * 每页条数
     */
    @field:XmlElement(name = "PageSize")
    var pageSize: Int = 100

    constructor(
        businessBeginTime: LocalDateTime,
        businessEndTime: LocalDateTime = LocalDateTime.now(),
        payerCode: String = "",
        payerId: String = "",
        mode: String = "3",
        pageNumber: Int = 1,
        pageSize: Int = 100,
    ) : this() {
        this.businessBeginTime = businessBeginTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
        this.businessEndTime = businessEndTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
        this.payerCode = payerCode
        this.payerId = payerId
        this.mode = mode
        this.pageNumber = pageNumber
        this.pageSize = pageSize
    }

    fun toXml(): String = JAXBKit.marshal(this)

    fun toRequest(): Request {
        return Request(content = EInvoiceService.encrypt(toXml()))
    }
}
