package org.mospital.donggang.einvoice

import org.mospital.common.jaxb.JAXBKit
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Voucher")
@XmlAccessorType(XmlAccessType.FIELD)
class DetailForm() {

    /**
     * 业务流水号
     */
    @field:XmlElement(name = "SerialNumber")
    var serialNumber: String = ""

    /**
     * 电子票据代码
     */
    @field:XmlElement(name = "EinvoiceCode")
    var code: String = ""

    /**
     * 电子票据号码
     */
    @field:XmlElement(name = "EinvoiceNumber")
    var number: String = ""

    /**
     * 电子票据格式：PDF、IMG
     */
    @field:XmlElement(name = "Format")
    var format: String = ""

    constructor(serialNumber: String, format: String) : this() {
        require(format == "PDF" || format == "IMG") { "format must be PDF or IMG" }
        this.serialNumber = serialNumber
        this.format = format
    }

    fun toXml(): String = JAXBKit.marshal(this)

    fun toRequest(): Request {
        return Request(content = EInvoiceService.encrypt(toXml()))
    }
}
