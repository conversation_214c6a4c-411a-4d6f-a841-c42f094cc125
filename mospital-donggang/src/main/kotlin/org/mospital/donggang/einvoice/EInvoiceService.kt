package org.mospital.donggang.einvoice

import kotlinx.coroutines.runBlocking
import okhttp3.OkHttpClient
import org.dromara.hutool.crypto.Mode
import org.dromara.hutool.crypto.Padding
import org.dromara.hutool.crypto.symmetric.AES
import org.mospital.common.http.HttpClientFactory
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jaxb3.JaxbConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST

@Suppress("unused")
interface EInvoiceService {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(EInvoiceService::class.java)
        private val serviceCache = mutableMapOf<String, EInvoiceService>()

        private fun createService(url: String): EInvoiceService {
            val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = 5_000,
                readTimeout = 10_000
            )
            val retrofit: Retrofit = Retrofit.Builder()
                .baseUrl(url)
                .client(httpClient)
                .addConverterFactory(JaxbConverterFactory.create())
                .build()
            return retrofit.create(EInvoiceService::class.java)
        }

        @Synchronized
        fun getService(url: String): EInvoiceService {
            return serviceCache.getOrPut(url) { createService(url) }
        }

        private val me: EInvoiceService by lazy {
            getService(EInvoiceSeting.URL)
        }

        private fun aes(): AES = AES(Mode.ECB, Padding.PKCS5Padding, EInvoiceSeting.APP_KEY.toByteArray(Charsets.UTF_8))

        fun encrypt(content: String): String {
            return aes().encryptBase64(content)
        }

        fun decrype(content: String): String {
            return aes().decryptStr(content)
        }

        /**
         * 查询票据开具列表
         */
        fun list(form: ListForm): ListResult = runBlocking {
            val response: Response = me.list(form.toRequest())
            ListResult.fromResponse(response)
        }

        /**
         * 获取票据图片或PDF
         */
        fun detail(form: DetailForm): DetailResult = runBlocking {
            val response: Response = me.detail(form.toRequest())
            DetailResult.fromResponse(response)
        }

        /**
         * 获取交款人二维码URL
         */
        fun url(form: UrlForm): UrlResult = runBlocking {
            val response: Response = me.url(form.toRequest())
            UrlResult.fromResponse(response)
        }

        /**
         * 查询票据开具列表（打印）
         */
        fun listPrint(form: ListPrint): ListPrintResult = runBlocking {
            val response: Response = me.listPrint(form.toRequest())
            ListPrintResult.fromResponse(response)
        }
    }

    @POST("einv_forward/api/queryeinv")
    suspend fun list(@Body request: Request): Response

    @POST("einv_forward/api/detaileinvfile")
    suspend fun detail(@Body request: Request): Response

    @POST("einv_forward/api/getpayerewmurl")
    suspend fun url(@Body request: Request): Response

    /**
     * 查询票据开具列表（打印）
     */
    @POST("einv_forward/api/queryeinvprint")
    suspend fun listPrint(@Body request: Request): Response

}
