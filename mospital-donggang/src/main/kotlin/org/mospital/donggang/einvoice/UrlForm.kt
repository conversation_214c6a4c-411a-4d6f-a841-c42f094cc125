package org.mospital.donggang.einvoice

import org.mospital.common.jaxb.JAXBKit
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Voucher")
@XmlAccessorType(XmlAccessType.FIELD)
class UrlForm() {

    /**
     * 交款人ID
     */
    @field:XmlElement(name = "PayerPartyId")
    var payerId: String = ""


    /**
     * 交款人代码: 单位一般为统一社会信用代码，个人一般为身份证号
     */
    @field:XmlElement(name = "PayerPartyCode")
    var payerCode: String = ""

    /**
     * 扫码方式
     * 01：获取的链接打开后需要用户信息验证正确后才能显示详细内容，如验证输入的证件号后四位是否与开票人证件号一致
     * 02：获取的链接打开后直接显示内容，用于在微信公众号中使用，默认为01
     */
    @field:XmlElement(name = "ScanMode")
    var scanMode: String = "01"

    constructor(payerId: String, payerCode: String, scanMode: String = "01") : this() {
        this.payerId = payerId
        this.payerCode = payerCode
        this.scanMode = scanMode
    }

    fun toXml(): String = JAXBKit.marshal(this)

    fun toRequest(): Request {
        return Request(content = EInvoiceService.encrypt(toXml()))
    }

}
