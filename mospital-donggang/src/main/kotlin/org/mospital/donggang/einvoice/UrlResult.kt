package org.mospital.donggang.einvoice

import org.mospital.common.jaxb.JAXBKit
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Voucher")
@XmlAccessorType(XmlAccessType.FIELD)
class UrlResult() {

    companion object {
        fun fromResponse(response: Response): UrlResult =
            if (response.isOk()) {
                try {
                    JAXBKit.unmarshal(UrlResult::class.java, response.decryptContent()).apply {
                        this.ok = true
                        this.msg = response.resultMsg
                    }
                } catch (e: Exception) {
                    UrlResult().apply {
                        this.ok = false
                        this.msg = e.message ?: "解析XML失败"
                    }
                }
            } else {
                UrlResult().apply {
                    this.ok = false
                    this.msg = response.resultMsg
                }
            }
    }

    /**
     * 交款人ID
     */
    @field:XmlElement(name = "EwmUrl")
    var url: String = ""

    var ok = false
    var msg: String = ""


}
