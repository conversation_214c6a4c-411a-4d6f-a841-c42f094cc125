package org.mospital.donggang.einvoice

import org.mospital.jackson.ConfigLoader

object EInvoiceSeting {

    data class Config(
        val url: String,
        val appId: String,
        val appKey: String,
        val agencyCode: String,
        val version: String
    )

    private val config: Config by lazy {
        ConfigLoader.loadConfig("donggang.yaml", Config::class.java)
    }

    val URL: String
        get() = config.url

    val APP_ID: String
        get() = config.appId

    val APP_KEY: String
        get() = config.appKey

    val AGENCY_CODE: String
        get() = config.agencyCode

    val VERSION: String
        get() = config.version

}
