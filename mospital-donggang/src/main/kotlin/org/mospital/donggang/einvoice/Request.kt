package org.mospital.donggang.einvoice

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Interface")
@XmlAccessorType(XmlAccessType.FIELD)
class Request() {

    @field:XmlElement(name = "AppId")
    var appId: String = EInvoiceSeting.APP_ID

    @field:XmlElement(name = "AgencyCode")
    var agencyCode: String = EInvoiceSeting.AGENCY_CODE

    @field:XmlElement(name = "Version")
    var version: String = EInvoiceSeting.VERSION

    @field:XmlElement(name = "Content")
    var content: String = ""

    constructor(content: String) : this() {
        this.content = content
    }

}
