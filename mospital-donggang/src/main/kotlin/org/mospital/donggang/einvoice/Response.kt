package org.mospital.donggang.einvoice

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Interface")
@XmlAccessorType(XmlAccessType.FIELD)
class Response {

    @field:XmlElement(name = "AppId")
    var appId: String = ""

    @field:XmlElement(name = "AgencyCode")
    var agencyCode: String = ""

    @field:XmlElement(name = "ResultCode")
    var resultCode: String = ""

    @field:XmlElement(name = "ResultMsg")
    var resultMsg: String = ""

    @field:XmlElement(name = "Content")
    var content: String = ""

    fun isOk(): Boolean = resultCode == "0000"
    fun decryptContent(): String {
        return EInvoiceService.decrype(content)
    }

}
