package org.mospital.donggang.einvoice

import org.mospital.common.jaxb.JAXBKit
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@Suppress("unused")
@XmlRootElement(name = "Voucher")
@XmlAccessorType(XmlAccessType.FIELD)
class DetailResult {

    companion object {
        fun fromResponse(response: Response): DetailResult =
            if (response.isOk()) {
                try {
                    JAXBKit.unmarshal(DetailResult::class.java, response.decryptContent()).apply {
                        this.ok = true
                        this.msg = response.resultMsg
                    }
                } catch (e: Exception) {
                    DetailResult().apply {
                        this.ok = false
                        this.msg = e.message ?: "解析XML失败"
                    }
                }
            } else {
                DetailResult().apply {
                    this.ok = false
                    this.msg = response.resultMsg
                }
            }
    }


    var ok = false
    var msg: String = ""

    /**
     * 业务流水号
     */
    @field:XmlElement(name = "SerialNumber")
    var serialNumber: String = ""

    /**
     * 开票状态，0=票据信息落地，1=票据生成成功，2=票据生成失败，3=正在调用中
     *
     */
    var status: String = ""

    /**
     * 电子票据代码
     */
    @field:XmlElement(name = "EinvoiceCode")
    var code: String = ""

    /**
     * 电子票据号码
     */
    @field:XmlElement(name = "EinvoiceNumber")
    var number: String = ""

    /**
     * 校验码
     */
    @field:XmlElement(name = "RandomNumber")
    var randomNumber: String = ""

    /**
     * 开票时间，格式yyyyMMddHHmmss
     */
    @field:XmlElement(name = "IssueTime")
    var issueTime: String = ""

    /**
     * 总金额
     */
    @field:XmlElement(name = "TotalAmount")
    var totalAmount: String = ""

    /**
     * 交款人类型: 1=个人，2=单位
     */
    @field:XmlElement(name = "PayerPartyType")
    var payerType: String = ""

    /**
     * 交款人代码: 单位一般为统一社会信用代码，个人一般为身份证号
     */
    @field:XmlElement(name = "PayerPartyCode")
    var payerCode: String = ""

    /**
     * 交款人姓名: 单位为单位名称，个人为个人姓名
     */
    @field:XmlElement(name = "PayerPartyName")
    var payerName: String = ""

    /**
     * 开票单位名称
     */
    @field:XmlElement(name = "InvoicingPartyName")
    var invoicingPartyName: String = ""

    /**
     * 电子票据格式：PDF、IMG
     */
    @field:XmlElement(name = "Format")
    var format: String = ""

    /**
     * Base64编码的电子票据
     */
    @field:XmlElement(name = "LayoutFile")
    var layoutFile: String = ""

}
