package org.mospital.allinpay

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mospital.allinpay.model.PayType
import org.mospital.allinpay.model.QueryOrderForm
import org.mospital.allinpay.model.RefundOrderForm
import org.mospital.allinpay.model.UnifiedOrderForm
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AllinpayServiceTest {

    @Test
    fun testCreateUnifiedOrder() {
        val unifiedOrderResult = runBlocking {
            AllinpayService.me.createUnifiedOrder(form = UnifiedOrderForm().apply {
                this.amount = 100
                this.outTradeNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
                this.payType = PayType.WEIXIN_MA_PAY
                this.body = "测试商品"
                this.remark = "测试商品"
                this.openid = "oQW3E5Vfwjs3lVM-5mtvolzPvXH0"
                this.notifyUrl = ""
                this.subAppId = "wxf665ae6f3f0372c1"
            })
        }
        assertTrue(unifiedOrderResult.isOK())
        assertTrue(unifiedOrderResult.verifySign())
    }

    @Test
    fun testQueryOrder() {
        val outTradeNo = "ZY202307011016506089"
        val transactionId = "230701126452003741"
        val queryOrderResult = runBlocking {
            AllinpayService.me.queryOrder(
                QueryOrderForm(
                    outTradeNo = outTradeNo,
                    transactionId = transactionId
                )
            )
        }
        assertTrue(queryOrderResult.isOK())
        assertTrue(queryOrderResult.verifySign())
        assertEquals(outTradeNo, queryOrderResult.outTradeNo)
        assertEquals(transactionId, queryOrderResult.transactionId)
    }

    @Test
    fun testRefundOrder() {
        val outTradeNo = "ZY202307011016506089"
        val transactionId = "230701126452003741"
        val refundOrderResult = runBlocking {
            AllinpayService.me.refundOrder(
                RefundOrderForm(
                    outTradeNo = outTradeNo,
                    transactionId = transactionId,
                    refundAmount = 100,
                    outRefundNo = outTradeNo + "R",
                )
            )
        }
        assertTrue(refundOrderResult.isOK())
        assertTrue(refundOrderResult.verifySign())
    }

}