package org.mospital.allinpay

import org.mospital.allinpay.model.*
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface AllinpayService {

    companion object {
        private val logger = LoggerFactory.getLogger(AllinpayService::class.java)
        private val serviceCache = mutableMapOf<String, AllinpayService>()

        private fun createService(url: String): AllinpayService {
            val httpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = 2_000,
                readTimeout = 10_000,
                useUuidRequestId = true
            ) {
                it.addInterceptor(SignInterceptor())
            }
            val retrofit = Retrofit.Builder()
                .baseUrl(url)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(JacksonKit.buildMapper()))
                .build()
            return retrofit.create(AllinpayService::class.java)
        }

        @Synchronized
        fun getService(url: String): AllinpayService {
            return serviceCache.getOrPut(url) { createService(url) }
        }

        val me: AllinpayService by lazy {
            getService(AllinpaySetting.URL)
        }
    }

    @FormUrlEncoded
    @POST("apiweb/unitorder/pay")
    suspend fun createUnifiedOrder(@FieldMap form: UnifiedOrderForm): UnifiedOrderResult

    @FormUrlEncoded
    @POST("apiweb/tranx/query")
    suspend fun queryOrder(@FieldMap form: QueryOrderForm): QueryOrderResult

    @FormUrlEncoded
    @POST("apiweb/tranx/refund")
    suspend fun refundOrder(@FieldMap form: RefundOrderForm): RefundOrderResult

}