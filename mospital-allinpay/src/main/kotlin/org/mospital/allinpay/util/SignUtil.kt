package org.mospital.allinpay.util

import java.nio.charset.Charset
import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.PublicKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.util.*

object SignUtil {

    fun rsaSign(data: String, privateKeyString: String, charset: Charset = StandardCharsets.UTF_8): String {
        val privateKey = KeyFactory.getInstance("RSA").generatePrivate(PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKeyString.toByteArray(charset))))
        return rsaSign(data, privateKey, charset)
    }

    fun rsaSign(data: String, privatekey: PrivateKey, charset: Charset = StandardCharsets.UTF_8): String {
        val signature = Signature.getInstance("SHA1WithRSA")
        signature.initSign(privatekey)
        signature.update(data.toByteArray(charset))
        val signedBytes = signature.sign()
        return Base64.getEncoder().encodeToString(signedBytes)
    }

    fun verifyRsaSign(data: String, sign: String, publicKeyString: String, charset: Charset = StandardCharsets.UTF_8): Boolean {
        val publicKey = KeyFactory.getInstance("RSA").generatePublic(X509EncodedKeySpec(Base64.getDecoder().decode(publicKeyString.toByteArray(charset))))
        return verifyRsaSign(data, sign, publicKey, charset)
    }

    fun verifyRsaSign(data: String, sign: String, publicKey: PublicKey, charset: Charset = StandardCharsets.UTF_8): Boolean {
        val signature = Signature.getInstance("SHA1WithRSA")
        signature.initVerify(publicKey)
        signature.update(data.toByteArray(charset))
        return signature.verify(Base64.getDecoder().decode(sign.toByteArray(charset)))
    }

}
