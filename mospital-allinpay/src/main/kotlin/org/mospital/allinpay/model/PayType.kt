package org.mospital.allinpay.model

enum class PayType(val code: String, val description: String) {

    WEIXIN_SCAN_PAY("W01", "微信扫码支付"),
    WEIXIN_JS_PAY("W02", "微信JS支付"),
    WEIXIN_MA_PAY("W06", "微信小程序支付"),
    ALIPAY_SCAN_PAY("A01", "支付宝扫码支付"),
    ALIPAY_JS_PAY("A02", "支付宝JS支付"),
    ALIPAY_APP_PAY("A03", "支付宝APP支付"),
    UNIONPAY_SCAN_PAY("U01", "银联扫码支付"),
    UNIONPAY_JS_PAY("U02", "银联JS支付"),
    CRYPTO_SCAN_PAY("S01", "数字货币扫码支付"),
    CRYPTO_JS_PAY("S03", "数字货币H5");

    companion object {
        fun of(code: String): PayType {
            return values().firstOrNull { it.code == code } ?: throw IllegalArgumentException("Unknown PayType code: $code")
        }
    }

}