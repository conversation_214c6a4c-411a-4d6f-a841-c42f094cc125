package org.mospital.allinpay.model

import org.mospital.allinpay.AllinpaySetting

/**
 * 统一支付表单
 * @see <a href="https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=88">统一支付接口</a>
 */
class UnifiedOrderForm : BaseForm {

    companion object {
        private const val serialVersionUID: Long = 7766602477733932837L

        val DEFAULT_VALID_TIME = 5
        val DEFAULT_LIMIT_PAY = ""
        val DEFAULT_GOODS_TAG = ""
        val DEFAULT_BENEFIT_DETAIL = ""
        val DEFAULT_CHANNEL_STORE_ID = ""
        val DEFAULT_SUB_BRANCH = ""
    }

    constructor(
        orgid: String = AllinpaySetting.ORGID,
        appid: String = AllinpaySetting.APPID,
        cusid: String = AllinpaySetting.CUSID,
        version: String = AllinpaySetting.VERSION
    ) : super(
        orgid = orgid,
        appid = appid,
        cusid = cusid,
        version = version
    ) {
        this.validTime = DEFAULT_VALID_TIME
        this.limitPay = DEFAULT_LIMIT_PAY
        this.goodsTag = DEFAULT_GOODS_TAG
        this.benefitDetail = DEFAULT_BENEFIT_DETAIL
        this.channelStoreId = DEFAULT_CHANNEL_STORE_ID
        this.subBranch = DEFAULT_SUB_BRANCH
        this.extendParams = ""
        this.frontUrl = ""
        this.idno = ""
        this.trueName = ""
        this.asinfo = ""
        this.fqnum = ""
        this.unpid = ""
        this.termInfo = ""
        this.operatorId = ""
    }

    /**
     * 交易金额，单位为分
     */
    var amount: Long
        get() = getOrDefault("trxamt", "0").toLong()
        set(value) {
            put("trxamt", value.toString())
        }

    /**
     * 商户交易单号
     */
    var outTradeNo: String
        get() = getOrDefault("reqsn", "")
        set(value) {
            put("reqsn", value)
        }

    /**
     * 交易方式
     */
    var payType: PayType
        get() = PayType.of(getOrDefault("paytype", ""))
        set(value) {
            put("paytype", value.code)
        }

    /**
     * 订单标题
     */
    var body: String
        get() = getOrDefault("body", "")
        set(value) {
            put("body", value)
        }

    /**
     * 备注
     */
    var remark: String
        get() = getOrDefault("remark", "")
        set(value) {
            put("remark", value)
        }

    /**
     * 有效时间，以分为单位，默认为5分钟
     */
    var validTime: Int
        get() = getOrDefault("validtime", DEFAULT_VALID_TIME.toString()).toInt()
        set(value) {
            put("validtime", value.toString())
        }

    /**
     * 支付平台用户标识
     */
    var openid: String
        get() = getOrDefault("acct", "")
        set(value) {
            put("acct", value)
        }

    /**
     * 交易结果通知地址
     */
    var notifyUrl: String
        get() = getOrDefault("notify_url", "")
        set(value) {
            put("notify_url", value)
        }

    /**
     * 支付限制
     * no_credit=不能使用信用卡支付
     */
    var limitPay: String
        get() = getOrDefault("limit_pay", DEFAULT_LIMIT_PAY)
        set(value) {
            put("limit_pay", value)
        }

    var subAppId: String
        get() = getOrDefault("sub_appid", "")
        set(value) {
            put("sub_appid", value)
        }

    /**
     * 订单优惠标识
     */
    var goodsTag: String
        get() = getOrDefault("goods_tag", DEFAULT_GOODS_TAG)
        set(value) {
            put("goods_tag", value)
        }

    /**
     * 优惠信息
     */
    var benefitDetail: String
        get() = getOrDefault("benefitdetail", DEFAULT_BENEFIT_DETAIL)
        set(value) {
            put("benefitdetail", value)
        }

    /**
     * 渠道门店编号
     */
    var channelStoreId: String
        get() = getOrDefault("chnlstoreid", DEFAULT_CHANNEL_STORE_ID)
        set(value) {
            put("chnlstoreid", value)
        }

    /**
     * 通联系统门店号
     */
    var subBranch: String
        get() = getOrDefault("subbranch", DEFAULT_SUB_BRANCH)
        set(value) {
            put("subbranch", value)
        }

    /**
     * 拓展参数
     */
    var extendParams: String
        get() = getOrDefault("extendparams", "")
        set(value) {
            put("extend_params", value)
        }

    /**
     * 用户下单和调起支付的终端ip地址
     */
    var cusip: String
        get() = getOrDefault("cusip", "")
        set(value) {
            put("cusip", value)
        }

    /**
     * 支付完成跳转
     */
    var frontUrl: String
        get() = getOrDefault("front_url", "")
        set(value) {
            put("front_url", value)
        }

    /**
     * 证件号
     */
    var idno: String
        get() = getOrDefault("idno", "")
        set(value) {
            put("idno", value)
        }

    /**
     * 付款人真实姓名
     */
    var trueName: String
        get() = getOrDefault("truename", "")
        set(value) {
            put("truename", value)
        }

    /**
     * 分账信息
     */
    var asinfo: String
        get() = getOrDefault("asinfo", "")
        set(value) {
            put("asinfo", value)
        }

    /**
     * 分期
     */
    var fqnum: String
        get() = getOrDefault("fqnum", "")
        set(value) {
            put("fqnum", value)
        }

    /**
     * 银联pid
     */
    var unpid: String
        get() = getOrDefault("unpid", "")
        set(value) {
            put("unpid", value)
        }

    /**
     * 终端信息
     */
    var termInfo: String
        get() = getOrDefault("terminfo", "")
        set(value) {
            put("terminfo", value)
        }

    /**
     * 收银员号
     */
    var operatorId: String
        get() = getOrDefault("operatorid", "")
        set(value) {
            put("operatorid", value)
        }
}