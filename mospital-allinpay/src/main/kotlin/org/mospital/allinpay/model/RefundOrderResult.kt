package org.mospital.allinpay.model

/**
 * 退款订单
 * @see <a href="https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=838">统一退款接口</a>
 */
class RefundOrderResult : BaseResult() {

    companion object {
        private const val serialVersionUID: Long = -7205457957149468849L
    }

    /**
     * 通联的退款交易流水号
     */
    val refundTransactionId: String
        get() = getOrDefault("trxid", "")

    /**
     * 商户退款订单号
     */
    val outRefundNo: String
        get() = getOrDefault("reqsn", "")

    /**
     * 交易完成时间
     */
    val finishTime: String
        get() = getOrDefault("fintime", "")


    /**
     * 手续费
     */
    val fee: Int
        get() = getOrDefault("fee", "0").toInt()

    /**
     * 交易类型
     */
    val transactionType: String
        get() = formatTransactionType()

    /**
     * 支付渠道订单号
     */
    val channelTransactionId: String
        get() = getOrDefault("chnltrxid", "")
}