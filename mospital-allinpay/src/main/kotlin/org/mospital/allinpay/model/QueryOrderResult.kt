package org.mospital.allinpay.model

/**
 * 查询订单
 * @see <a href="https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=836">统一查询接口</a>
 */
class QueryOrderResult: BaseResult() {

    companion object {
        private const val serialVersionUID: Long = -7205457957149468849L
    }

    /**
     * 支付平台用户标识
     */
    val openid: String
        get() = getOrDefault("acct", "")

    /**
     * 商户订单号
     */
    val outTradeNo: String
        get() = getOrDefault("reqsn", "")

    /**
     * 通联订单号
     */
    val transactionId: String
        get() = getOrDefault("trxid", "")

    /**
     * 银行卡类型
     */
    val bankType: String
        get() = when (getOrDefault("accttype", "")) {
            "00" -> "借记卡"
            "02" -> "信用卡"
            else -> "其它"
        }

    /**
     * 支付渠道订单号
     */
    val channelTransactionId: String
        get() = getOrDefault("chnltrxid", "")

    /**
     * 交易金额
     */
    val amount: Int
        get() = getOrDefault("trxamt", "0").toInt()

    /**
     * 手续费
     */
    val fee: Int
        get() = getOrDefault("fee", "0").toInt()

    /**
     * 原交易金额
     */
    val initAmount: Int
        get() = getOrDefault("initamt", "0").toInt()

    /**
     * 交易完成时间
     */
    val finishTime: String
        get() = getOrDefault("fintime", "")

    /**
     * 交易类型
     */
    val transactionType: String
        get() = formatTransactionType()

}