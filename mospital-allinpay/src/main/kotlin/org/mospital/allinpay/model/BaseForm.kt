package org.mospital.allinpay.model

import okhttp3.FormBody
import org.mospital.allinpay.AllinpaySetting
import org.mospital.allinpay.util.SignUtil
import org.mospital.common.IdUtil

abstract class BaseForm : HashMap<String, String> {

    companion object {
        private const val serialVersionUID: Long = 999383080782847550L
    }

    constructor(
        orgid: String = AllinpaySetting.ORGID,
        appid: String = AllinpaySetting.APPID,
        cusid: String = AllinpaySetting.CUSID,
        version: String = AllinpaySetting.VERSION,
        signType: SignType = SignType.RSA
    ) : super() {
        this.orgid = orgid
        this.appid = appid
        this.cusid = cusid
        this.version = version
        this.randomString = IdUtil.simpleUUID()
        this.signType = signType
    }

    /**
     * 集团/代理商商户号
     */
    open var orgid: String
        get() = getOrDefault("orgid", AllinpaySetting.APPID)
        set(value) {
            put("appid", value)
        }

    /**
     * 应用ID
     */
    open var appid: String
        get() = getOrDefault("appid", AllinpaySetting.APPID)
        set(value) {
            put("appid", value)
        }

    /**
     * 商户号
     */
    open var cusid: String
        get() = getOrDefault("cusid", AllinpaySetting.CUSID)
        set(value) {
            put("cusid", value)
        }

    /**
     * 版本号
     */
    open var version: String
        get() = getOrDefault("version", AllinpaySetting.VERSION)
        set(value) {
            put("version", value)
        }

    /**
     * 随机字符串
     */
    open var randomString: String
        get() = getOrDefault("randomstr", "")
        set(value) {
            put("randomstr", value)
        }

    /**
     * 签名方式
     */
    open var signType: SignType
        get() = SignType.of(getOrDefault("signtype", ""))
        set(value) {
            put("signtype", value.name)
        }

    open fun sign() {
        val rawText = entries.filter { "sign" != it.key && it.value.isNotEmpty() }
            .sortedBy { it.key }
            .joinToString(separator = "&") { "${it.key}=${it.value}" }
        val sign = when (signType) {
            SignType.RSA -> SignUtil.rsaSign(rawText, AllinpaySetting.RSA_PRIVATE_KEY)
            SignType.SM2 -> throw NotImplementedError()
        }
        put("sign", sign)
    }

    open fun toFormBody(): FormBody {
        val formBodyBuidler = FormBody.Builder()
        forEach { k, v ->
            formBodyBuidler.add(k, v)
        }
        return formBodyBuidler.build()
    }

}
