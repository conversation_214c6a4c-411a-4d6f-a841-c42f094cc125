package org.mospital.allinpay.model

import org.mospital.allinpay.AllinpaySetting

/**
 * 退款订单
 * @see <a href="https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=838">统一退款接口</a>
 */
class RefundOrderForm : BaseForm {

    companion object {
        private const val serialVersionUID: Long = 6290035838953046652L
    }

    constructor(
        orgid: String = AllinpaySetting.ORGID,
        appid: String = AllinpaySetting.APPID,
        cusid: String = AllinpaySetting.CUSID,
        version: String = AllinpaySetting.VERSION,
        outTradeNo: String,
        transactionId: String,
        refundAmount: Int,
        outRefundNo: String,
        remark: String = ""
    ) : super(
        orgid = orgid,
        appid = appid,
        cusid = cusid,
        version = version
    ) {
        if (outTradeNo.isBlank() && transactionId.isBlank()) {
            throw IllegalArgumentException("outTradeNo and transactionId cannot be both blank")
        }
        this.outTradeNo = outTradeNo
        this.transactionId = transactionId
        this.refundAmount = refundAmount
        this.outRefundNo = outRefundNo
        this.remark = remark
    }

    /**
     * 退款金额，单位分
     */
    var refundAmount: Int
        get() = getOrDefault("trxamt", "0").toInt()
        set(value) {
            put("trxamt", value.toString())
        }

    /**
     * 商户退款订单号
     */
    var outRefundNo: String
        get() = getOrDefault("reqsn", "")
        set(value) {
            put("reqsn", value)
        }

    /**
     * 商户订单号
     */
    var outTradeNo: String
        get() = getOrDefault("oldreqsn", "")
        set(value) {
            put("oldreqsn", value)
        }

    /**
     * 通联订单号
     */
    var transactionId: String
        get() = getOrDefault("oldtrxid", "")
        set(value) {
            put("oldtrxid", value)
        }

    /**
     * 备注
     */
    var remark: String
        get() = getOrDefault("remark", "")
        set(value) {
            put("remark", value)
        }


}