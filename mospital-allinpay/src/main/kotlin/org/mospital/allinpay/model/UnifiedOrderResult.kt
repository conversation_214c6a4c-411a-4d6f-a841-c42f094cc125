package org.mospital.allinpay.model

/**
 * 统一支付
 * @see <a href="https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=88">统一支付接口</a>
 */
class UnifiedOrderResult: BaseResult() {

    companion object {
        private const val serialVersionUID: Long = 6997429233057761242L
    }

    /**
     * 商户订单号
     */
    val outTradeNo: String
        get() = getOrDefault("reqsn", "")

    /**
     * 通联订单号
     */
    val transactionId: String
        get() = getOrDefault("trxid", "")

    val payInfo: String
        get() = getOrDefault("payinfo", "")

}