package org.mospital.allinpay.model

import org.slf4j.LoggerFactory
import org.mospital.allinpay.AllinpaySetting
import org.mospital.allinpay.util.SignUtil

abstract class BaseResult: HashMap<String, String>() {

    companion object {
        private const val serialVersionUID: Long = -7325387028311137583L
    }

    private val log = LoggerFactory.getLogger(this::class.java)

    /**
     * 应用ID
     */
    open val appid: String
        get() = getOrDefault("appid", "")

    /**
     * 商户号
     */
    open val cusid: String
        get() = getOrDefault("cusid", "")

    /**
     * 随机字符串
     */
    open val randomString: String
        get() = getOrDefault("randomstr", "")


    open val transactionStatus: String
        get() = getOrDefault("trxstatus", "")

    open val sign: String
        get() = getOrDefault("sign", "")

    open val resultCode: String
        get() = getOrDefault("retcode", "")

    open val resultMessage: String
        get() = getOrDefault("retmsg", "")

    open val errorMessage: String
        get() = getOrDefault("errmsg", "")

    open fun isOK(): Boolean = resultCode == "SUCCESS" && transactionStatus == "0000"

    open fun getMessage(): String {
        if (resultCode != "SUCCESS") {
            return resultMessage
        }

        return when (transactionStatus) {
            "0000" -> "SUCCESS"
            "1001" -> "交易不存在"
            "2000", "2008" -> "交易处理中"
            "3014" -> "交易金额小于应收手续费"
            "3031" -> "校验实名信息失败"
            "3050" -> "交易已被撤销"
            "3088" -> "交易未支付"
            "3089" -> "撤销异常"
            "3099" -> "渠道商户错误"
            "3888" -> "流水号重复"
            else -> errorMessage
        }
    }

    open fun verifySign(): Boolean {
        if (resultCode != "SUCCESS") {
            return false
        }

        val rawText = entries.filter { "sign" != it.key && it.value.isNotEmpty() }
            .sortedBy { it.key }
            .joinToString(separator = "&") { "${it.key}=${it.value}" }
        return try {
            SignUtil.verifyRsaSign(rawText, this.sign, AllinpaySetting.RSA_PUBLIC_KEY)
        } catch (e: Exception) {
            log.warn(e.message, e)
            false
        }
    }

    open fun formatTransactionType(): String {
        return when (getOrDefault("trxcode", "")) {
            "VSP501" -> "微信支付"
            "VSP502" -> "微信支付撤销"
            "VSP503" -> "微信支付退款"
            "VSP505" -> "手机QQ支付"
            "VSP506" -> "手机QQ支付撤销"
            "VSP507" -> "手机QQ支付退款"
            "VSP511" -> "支付宝支付"
            "VSP512" -> "支付宝支付撤销"
            "VSP513" -> "支付宝支付退款"
            "VSP541" -> "扫码支付"
            "VSP542" -> "扫码撤销"
            "VSP543" -> "扫码退货"
            "VSP551" -> "银联扫码支付"
            "VSP552" -> "银联扫码撤销"
            "VSP553" -> "银联扫码退货"
            "VSP907" -> "差错借记调整"
            "VSP908" -> "差错贷记调整"
            "VSP611" -> "数字货币支付"
            "VSP612" -> "数字货币撤销"
            "VSP613" -> "数字货币退货"
            "VSP621" -> "分期支付"
            "VSP622" -> "分期撤销"
            "VSP623" -> "分期退货"
            "300002" -> "充值"
            else -> "其它"
        }
    }

}