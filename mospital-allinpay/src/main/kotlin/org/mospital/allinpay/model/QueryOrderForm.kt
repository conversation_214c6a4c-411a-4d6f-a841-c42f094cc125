package org.mospital.allinpay.model

import org.mospital.allinpay.AllinpaySetting

/**
 * 查询订单
 * @see <a href="https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=836">统一查询接口</a>
 */
class QueryOrderForm : BaseForm {

    companion object {
        private const val serialVersionUID: Long = -8868207567603223301L
    }

    constructor(
        orgid: String = AllinpaySetting.ORGID,
        appid: String = AllinpaySetting.APPID,
        cusid: String = AllinpaySetting.CUSID,
        version: String = AllinpaySetting.VERSION,
        outTradeNo: String,
        transactionId: String
    ) : super(
        orgid = orgid,
        appid = appid,
        cusid = cusid,
        version = version
    ) {
        if (outTradeNo.isBlank() && transactionId.isBlank()) {
            throw IllegalArgumentException("outTradeNo and transactionId cannot be both blank")
        }
        this.outTradeNo = outTradeNo
        this.transactionId = transactionId
    }

    /**
     * 商户订单号
     */
    var outTradeNo: String
        get() = getOrDefault("reqsn", "")
        set(value) {
            put("reqsn", value)
        }

    /**
     * 通联订单号
     */
    var transactionId: String
        get() = getOrDefault("trxid", "")
        set(value) {
            put("trxid", value)
        }

}