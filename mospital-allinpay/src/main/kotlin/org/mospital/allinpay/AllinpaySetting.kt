package org.mospital.allinpay

import org.mospital.jackson.ConfigLoader

object AllinpaySetting {

    private val config: AllinpayConfig by lazy {
        ConfigLoader.loadConfig("allinpay.yaml", AllinpayConfig::class.java)
    }

    val URL: String get() = config.url
    val ORGID: String get() = config.orgid
    val APPID: String get() = config.appid
    val CUSID: String get() = config.cusid
    val VERSION: String get() = config.version
    val RSA_PUBLIC_KEY: String get() = config.rsaPublicKey
    val RSA_PRIVATE_KEY: String get() = config.rsaPrivateKey
    val SM2_PRIVATE_KEY: String get() = config.sm2PrivateKey

}