package org.mospital.allinpay

import okhttp3.Interceptor
import okhttp3.Response
import org.mospital.allinpay.model.BaseForm
import retrofit2.Invocation

class SignInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val invocation: Invocation = request.tag(Invocation::class.java) as Invocation
        val form = invocation.arguments()[0]
        if (form !is BaseForm) {
            return chain.proceed(request)
        }
        form.sign()
        val newRequest = request.newBuilder().post(form.toFormBody()).build()
        return chain.proceed(newRequest)
    }


}