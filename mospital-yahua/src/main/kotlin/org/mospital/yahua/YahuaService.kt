package org.mospital.yahua

import okhttp3.OkHttpClient
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface YahuaService {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(YahuaService::class.java)
        private val serviceCache = mutableMapOf<String, YahuaService>()

        private fun createService(url: String): YahuaService {
            val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = 5_000,
                readTimeout = 10_000,
                useUuidRequestId = true
            )
            val retrofit = Retrofit.Builder()
                .baseUrl(url)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(JacksonKit.buildMapper()))
                .build()
            return retrofit.create(YahuaService::class.java)
        }

        @Synchronized
        fun getService(url: String): YahuaService {
            return serviceCache.getOrPut(url) { createService(url) }
        }

        val me: YahuaService by lazy {
            getService(YahuaSetting.url)
        }

    }

    /**
     * 查询患者队列
     * @param hospitalId 医院ID
     * @param type 查询类型：1=就诊卡号、身份证号，2=门诊号
     * @param patientKey 门诊号、就诊卡号、身份证号等任选一种
     * @return 患者队列
     */
    @POST("/triage/sign/getvisitInfo")
    @FormUrlEncoded
    suspend fun getPatientQueue(
        @Field("hospitalId") hospitalId: String = YahuaSetting.hospitalId,
        @Field("type") type: Int = 1,
        @Field("patientKey") patientKey: String
    ): PatientQueueResponse

}