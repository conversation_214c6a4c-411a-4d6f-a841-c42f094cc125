<!-- Generated by SpecStory -->

# 实现获取住院患者信息接口 (2025-08-07 05:49Z)

_**User**_

阅读文档 @S008.html ，参考 @1.1.8【RC1.1.8S008】获取住院患者信息.md ，实现`获取住院患者信息`接口

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我阅读文档 S008.html，参考 1.1.8【RC1.1.8S008】获取住院患者信息.md，实现"获取住院患者信息"接口。

让我首先理解这些文档：

1. S008.html - 这是一个HTML格式的API文档，描述了获取住院患者信息的接口规范
2. 1.1.8【RC1.1.8S008】获取住院患者信息.md - 这是一个Markdown文档，包含了实际的请求和响应示例

从文档中我可以看到：

接口信息：
- Path: /api/v1/common/in-patient/get/{inpatientNo}
- Method: GET
- 接口描述: 住院患者详细信息查询

路径参数：
- inpatientNo: 患者编号

响应数据包含很多字段，如患者基本信息、住院信息、医生护士信息等。

从示例中看到：
- 请求URL: http://172.16.2.31:7800/roc/curr-web/api/v1/common/in-patient/get/ZY0010001201189
- Headers需要包含 domain 和 key
- 响应是JSON格式，包含code、msg、data字段

现在我需要在这个项目中实现这个接口。首先我需要了解项目结构，找到合适的地方来实现这个接口。

从项目结构来看，这似乎是一个多模块的Maven项目，专门用于集成各种医院系统。有一个 mospital-dongruan-roc 模块，这应该就是我需要在其中实现接口的地方。

我将开始在项目中搜索相关代码，了解现有的实现模式。重点关注控制器、服务层和数据访问层的实现方式。</details></think>

---

我来帮您实现获取住院患者信息接口。首先让我了解一下项目结构和现有的实现模式。

---

<details>
                <summary>Searched codebase "如何在dongruan-roc模块中实现REST API接口" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | Lundefined-48 |
| `mospital-dongruan-roc/doc/API-List.md` | Lundefined-19 |
| `mospital-dongruan-roc/doc/API-List.md` | L43-79 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L44-88 |
| `mospital-dongruan-roc/README.md` | Lundefined-73 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocResponse.kt` | Lundefined-41 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L514-563 |
| `mospital-dongruan-roc/src/test/resources/dongruan-roc.yaml` | Lundefined-16 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L597-645 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L642-687 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L683-731 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocConfig.kt` | Lundefined-30 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L561-601 |
| `mospital-dongruan-roc/doc/ROC-病历-api.html` | L1075-1111 |
| `mospital-dongruan-roc/doc/API-List.md` | L30-45 |

</details>

---

<think><details><summary>Thought Process</summary>现在我了解了项目的结构：

1. 使用Kotlin编写
2. 使用Retrofit作为HTTP客户端库
3. 已经有一个DongruanRocService接口定义了服务
4. 有一个HeaderInterceptor负责自动添加domain和key头部
5. 使用Jackson进行JSON序列化和反序列化

让我先查看现有的DongruanRocService接口定义，看看是否已经有相关的方法定义。</details></think>

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>现在我可以看到，`DongruanRocService`接口已经有很多方法了。我需要查看一下是否已经有获取住院患者信息的方法。从文档可以看到：

1. API-List.md 第47行已经列出了这个接口：
   `| 39 | RC1.1.8S008 | 查询住院患者信息 | GetCommonInPatienInfoMsg | 基础数据 | 调用 | /roc/curr-web/api/curr-web/v1/common/in-patient/get/{inPatientNo} |`

2. 但是在当前的DongruanRocService.kt中，我没有看到对应的方法。我需要添加这个方法。

根据文档，我需要添加两个方法：
1. 原始接口方法：getInPatientInfoRaw
2. 便捷方法：getInPatientInfo

让我先查看一下数据模型文件，了解住院患者信息的数据结构。</details></think>

---

<details>
                <summary>Searched codebase "住院患者信息数据模型或响应结构" • **14** results</summary>
            
| File | Lines |
|------|-------|
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/InPatientInfo.kt` | Lundefined-130 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/InPatientInfo.kt` | L120-262 |
| `mospital-dongruan-roc/README.md` | L117-156 |
| `mospital-dongruan-roc/doc/ROC-公共-api.html` | L1150-1161 |
| `mospital-dongruan-roc/doc/1.1.8【RC1.1.8S008】获取住院患者信息.md` | Lundefined-76 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/InPatientListResponse.kt` | Lundefined-101 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/OutPatientInfo.kt` | Lundefined-148 |
| `mospital-dongruan-roc/doc/S008.html` | L47-57 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/InPatientInfo.kt` | L252-311 |
| `mospital-dongruan-roc/doc/1.1.7【RC1.1.7S007】查询住院患者列表.md` | Lundefined-62 |
| `mospital-dongruan-roc/doc/S008.html` | Lundefined-50 |
| `mospital-dongruan-roc/doc/ROC-患者-api.html` | L1203-1213 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/OutPatientInfo.kt` | L138-283 |
| `mospital-dongruan-roc/doc/ROC-公共-api.html` | L1103-1153 |

</details>

---

<think><details><summary>Thought Process</summary>现在我了解了现有的数据结构。我发现有一个 `InPatientInfo` 类，但它是用于住院患者列表的，可能需要创建一个专门用于获取单个住院患者详细信息的数据类。

让我查看具体的字段名对比：

从 S008.html 和示例 MD 文件可以看到，响应包含的字段和现有的 InPatientInfo 有些不同。我需要创建一个新的数据类来对应API文档中定义的字段。

根据API文档和示例，我需要：

1. 创建一个 `InPatientDetailInfo` 数据类，包含所有相关字段
2. 在 `DongruanRocService` 中添加两个方法：
   - `getInPatientInfoRaw`: 原始接口方法
   - `getInPatientInfo`: 便捷方法

让我先创建数据类，然后添加接口方法。</details></think>

---

我需要根据文档创建获取住院患者信息的接口。让我首先创建一个专门的数据模型类，然后在服务接口中添加相应的方法。

---

## Todo List

- [-] 创建InPatientDetailInfo数据模型类
- [ ] 在DongruanRocService中添加获取住院患者信息的方法
- [ ] 测试接口实现
- [ ] 更新README文档

---



---

<think><details><summary>Thought Process</summary>现在我已经创建了 InPatientDetailInfo 数据模型类。接下来需要在 DongruanRocService 中添加获取住院患者信息的方法。

根据 S008.html 文档，接口的信息是：
- Path: /api/v1/common/in-patient/get/{inpatientNo}
- Method: GET

但在 API-List.md 中，我看到路径稍有不同：
`/roc/curr-web/api/curr-web/v1/common/in-patient/get/{inPatientNo}`

让我查看一下实际使用的示例，从 MD 文件中可以看到实际的 URL：
`http://172.16.2.31:7800/roc/curr-web/api/v1/common/in-patient/get/ZY0010001201189`

所以正确的路径应该是：`/roc/curr-web/api/v1/common/in-patient/get/{inpatientNo}`</details></think>

---

## Todo List

- [x] 创建InPatientDetailInfo数据模型类
- [-] 在DongruanRocService中添加获取住院患者信息的方法
- [ ] 测试接口实现
- [ ] 更新README文档

---

<details><summary>Edit file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt</summary>

undefined

**Chunk 1**
Lines added: 14, lines removed: undefined

```diff
@@ -516,4 +516,18 @@
              }
          }
+ 
+         /**
+          * 获取住院患者详细信息（便捷方法，直接返回 Result）
+          * @param inpatientNo 患者编号
+          * @return 住院患者详细信息
+          */
+         suspend fun getInPatientDetailInfo(inpatientNo: String): Result<InPatientDetailInfo> {
+             return try {
+                 val response = me.getInPatientDetailInfoRaw(inpatientNo)
+                 response.toResult()
+             } catch (e: Exception) {
+                 Result.failure(e)
+             }
+         }
      }
  
```

</details>

---

<think><details><summary>Thought Process</summary>现在我需要在 DongruanRocService 接口中添加原始的 HTTP 接口方法。我需要找到合适的位置添加这个方法。让我查看一下最后的部分，看看在哪里添加原始接口方法比较合适。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt</summary>

undefined

**Chunk 1**
Lines added: 10, lines removed: undefined

```diff
@@ -764,4 +764,14 @@
      ): DongruanRocResponse<InPatientListResponse>
  
+     /**
+      * 【RC1.1.8S008】获取住院患者详细信息（原始接口）
+      * @param inpatientNo 患者编号
+      * @return 住院患者详细信息响应
+      */
+     @GET("/roc/curr-web/api/v1/common/in-patient/get/{inpatientNo}")
+     suspend fun getInPatientDetailInfoRaw(
+         @Path("inpatientNo") inpatientNo: String
+     ): DongruanRocResponse<InPatientDetailInfo>
+ 
  }
  
```

</details>

---

<think><details><summary>Thought Process</summary>现在我需要创建一个测试文件来验证接口实现是否正确。然后更新README文档。让我先检查lint错误。</details></think>

---

## Todo List

- [x] 创建InPatientDetailInfo数据模型类
- [x] 在DongruanRocService中添加获取住院患者信息的方法
- [-] 测试接口实现
- [ ] 更新README文档

---



---

<think><details><summary>Thought Process</summary>很好，没有lint错误。现在我需要创建一个测试来验证接口实现。让我创建一个测试文件。</details></think>

---



---

<think><details><summary>Thought Process</summary>现在让我更新README文档，添加获取住院患者详细信息的使用说明。</details></think>

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>我需要找到合适的位置插入新的功能说明。让我先搜索相关的部分。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/README.md</summary>

undefined

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -14,4 +14,5 @@
  - 门诊患者费用明细查询
  - **住院患者列表查询** (新增)
+ - **住院患者详细信息查询** (新增)
  
  ## 快速开始
```

</details>

---

<think><details><summary>Thought Process</summary>现在我需要在README中添加关于获取住院患者详细信息的使用示例。让我找到合适的位置来添加这个部分。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/README.md</summary>

undefined

**Chunk 1**
Lines added: 64, lines removed: undefined

```diff
@@ -67,4 +67,68 @@
      patientState = "I" // 在院状态
  )
+ ```
+ 
+ ### 获取住院患者详细信息
+ 
+ ```kotlin
+ import org.mospital.dongruan.roc.DongruanRocService
+ 
+ // 根据患者编号获取详细信息
+ val result = DongruanRocService.getInPatientDetailInfo("ZY0010001201189")
+ 
+ if (result.isSuccess) {
+     val patientInfo = result.getOrNull()
+     patientInfo?.let { info ->
+         println("患者基本信息：")
+         println("- 患者ID: ${info.patientId}")
+         println("- 住院流水号: ${info.hisPatientNo}")
+         println("- 住院号: ${info.patientNo}")
+         println("- 卡号: ${info.cardNo}")
+         println("- 姓名: ${info.name}")
+         println("- 性别: ${info.genderCode}")
+         println("- 身份证号: ${info.idenno}")
+         println("- 生日: ${info.birthDate}")
+         println("- 民族: ${info.nationCode}")
+         println("- 联系电话: ${info.homeTel}")
+         println("- 家庭地址: ${info.homeAdd}")
+         println("- 联系人: ${info.linkmanName}")
+         println("- 联系人电话: ${info.linkmanTel}")
+         println("- 联系人关系: ${info.relaName}")
+         
+         println("\n住院信息：")
+         println("- 科室: ${info.deptName} (${info.deptCode})")
+         println("- 床号: ${info.bedNo}")
+         println("- 护理单元: ${info.nurseCellName} (${info.nurseCellCode})")
+         println("- 入院时间: ${info.inDate}")
+         println("- 入院次数: ${info.inTimes}")
+         println("- 在院状态: ${info.inState}")
+         println("- 主诊断: ${info.mainDiagnose}")
+         
+         println("\n医护信息：")
+         println("- 住院医师: ${info.houseDocName} (${info.houseDocCode})")
+         println("- 主治医师: ${info.chargeDocName} (${info.chargeDocCode})")
+         println("- 主任医师: ${info.chiefDocName} (${info.chiefDocCode})")
+         println("- 责任护士: ${info.dutyNurseName} (${info.dutyNurseCode})")
+         
+         println("\n费用信息：")
+         println("- 合同单位: ${info.pactName} (${info.pactCode})")
+         println("- 结算类别: ${info.paykindCode}")
+         println("- 预交金: ${info.prepayCost}")
+         println("- 总费用: ${info.totCost}")
+         println("- 余额: ${info.freeCost}")
+         
+         println("\n健康信息：")
+         println("- 身高: ${info.height}")
+         println("- 体重: ${info.weight}")
+         println("- 血型: ${info.bloodCode}")
+         println("- 血压: ${info.bloodDress}")
+         println("- 过敏标志: ${info.anaphyFlag}")
+         println("- 重大疾病标志: ${info.hepatitisFlag}")
+         println("- 病情标志: ${info.criticalFlag}")
+         println("- 护理级别: ${info.tend}")
+     }
+ } else {
+     println("查询失败: ${result.exceptionOrNull()?.message}")
+ }
  ```
  
```

</details>

---

<think><details><summary>Thought Process</summary>



现在我需要在接口参数说明部分添加获取住院患者详细信息的参数说明。让我找到合适的位置添加。</details></think>

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>



我需要在"接口参数说明"部分添加获取住院患者详细信息的参数说明。让我找到一个合适的地方插入。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/README.md</summary>

undefined

**Chunk 1**
Lines added: 2, lines removed: undefined

```diff
@@ -134,4 +134,6 @@
  
  ### 接口参数说明
+ 
+ #### 查询住院患者列表参数
  
  | 参数名 | 类型 | 必填 | 说明 |
```

**Chunk 2**
Lines added: 6, lines removed: undefined

```diff
@@ -155,4 +157,10 @@
  | outBeginDate | String | 否 | 开始时间（出院） |
  | outEndDate | String | 否 | 结束时间（出院） |
+ 
+ #### 获取住院患者详细信息参数
+ 
+ | 参数名 | 类型 | 必填 | 说明 |
+ |--------|------|------|------|
+ | inpatientNo | String | 是 | 患者编号（住院流水号） |
  
  ### 返回数据结构
```

</details>

---

<think><details><summary>Thought Process</summary>



现在我需要在返回数据结构部分添加住院患者详细信息的数据结构说明。让我找到合适的位置添加。</details></think>

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>



我需要在返回数据结构部分添加住院患者详细信息的数据结构。让我找到合适的位置插入。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/README.md</summary>

undefined

**Chunk 1**
Lines added: 2, lines removed: undefined

```diff
@@ -165,4 +165,6 @@
  
  ### 返回数据结构
+ 
+ #### 住院患者列表响应结构
  
  ```kotlin
```

**Chunk 2**
Lines added: 91, lines removed: undefined

```diff
@@ -187,4 +189,95 @@
      val navigateLastPage: Int?           // 导航最后一页
  )
+ ```
+ 
+ #### 住院患者详细信息结构
+ 
+ ```kotlin
+ data class InPatientDetailInfo(
+     // 患者基本信息
+     val patientId: String?,              // EMR患者ID
+     val hisPatientNo: String?,           // 患者住院流水号
+     val patientNo: String?,              // 患者住院号
+     val cardNo: String?,                 // 患者CARDNO
+     val name: String?,                   // 姓名
+     val genderCode: String?,             // 性别
+     val idenno: String?,                 // 身份证号
+     val birthDate: LocalDateTime?,       // 生日
+     val spellCode: String?,              // 患者姓名拼音码
+     val nationCode: String?,             // 民族
+     val counCode: String?,               // 国籍
+     val dist: String?,                   // 籍贯
+     val birthArea: String?,              // 出生地名称
+     
+     // 联系信息
+     val homeTel: String?,                // 家庭电话
+     val homeAdd: String?,                // 家庭地址
+     val homeZip: String?,                // 家庭邮编
+     val linkmanName: String?,            // 联系人姓名
+     val linkmanTel: String?,             // 联系人电话
+     val linkmanAdd: String?,             // 联系人地址
+     val relaCode: String?,               // 联系人编码关系
+     val relaName: String?,               // 联系人关系
+     
+     // 工作信息
+     val profCode: String?,               // 职业代码
+     val profName: String?,               // 职业名称
+     val workName: String?,               // 单位名称
+     val workTel: String?,                // 单位电话
+     val workZip: String?,                // 单位邮编
+     val mari: String?,                   // 婚姻状况
+     
+     // 住院信息
+     val deptCode: String?,               // 科室代码
+     val deptName: String?,               // 科室名称
+     val bedNo: String?,                  // 床号
+     val nurseCellCode: String?,          // 护理单元代码
+     val nurseCellName: String?,          // 护理单元名称
+     val inDate: LocalDateTime?,          // 入院日期
+     val outDate: LocalDateTime?,         // 出院日期
+     val inTimes: String?,                // 入院次数
+     val inState: String?,                // 在院状态
+     val inAvenue: String?,               // 入院途径
+     val inCircs: String?,                // 入院情况
+     val inSource: String?,               // 入院来源
+     val isNewBorn: String?,              // 是否新生儿
+     val zg: String?,                     // 转归标记
+     
+     // 医护信息
+     val houseDocCode: String?,           // 医师代码(住院)
+     val houseDocName: String?,           // 医师姓名(住院)
+     val chargeDocCode: String?,          // 医师代码(主治)
+     val chargeDocName: String?,          // 医师姓名(主治)
+     val chiefDocCode: String?,           // 医师代码(主任)
+     val chiefDocName: String?,           // 医师姓名(主任)
+     val dutyNurseCode: String?,          // 护士代码(责任)
+     val dutyNurseName: String?,          // 护士姓名(责任)
+     
+     // 诊断信息
+     val mainDiagnose: String?,           // 主诊断
+     val tend: String?,                   // 护理级别
+     val criticalFlag: String?,           // 病情标志
+     
+     // 费用信息
+     val pactCode: String?,               // 合同代码
+     val pactName: String?,               // 合同单位名称
+     val paykindCode: String?,            // 结算类别
+     val medicalType: String?,            // 患者合同类型
+     val mcardNo: String?,                // 医疗证号
+     val prepayCost: String?,             // 预交金
+     val totCost: String?,                // 费用汇总
+     val freeCost: String?,               // 余额(未结)
+     
+     // 健康信息
+     val height: String?,                 // 身高
+     val weight: String?,                 // 体重
+     val bloodCode: String?,              // 血型编码
+     val bloodDress: String?,             // 血压
+     val anaphyFlag: String?,             // 过敏标志
+     val hepatitisFlag: String?,          // 重大疾病标志
+     
+     // 扩展信息
+     val inDeptDate: LocalDateTime?       // 入科时间
+ )
  ```
  
```

</details>

---

<think><details><summary>Thought Process</summary>现在我已经完成了主要的实现和文档更新。让我检查一下最终的TODO状态并完成最后的任务。</details></think>

---

## Todo List

- [x] 创建InPatientDetailInfo数据模型类
- [x] 在DongruanRocService中添加获取住院患者信息的方法
- [x] 测试接口实现
- [x] 更新README文档

---



---

<think><details><summary>Thought Process</summary>



完美！所有任务都已完成，没有lint错误。现在让我创建一个简单的演示示例来展示如何使用新实现的接口。</details></think>

---



---

<think><details><summary>Thought Process</summary>



完美！现在我已经完成了所有的实现工作。让我总结一下完成的内容。</details></think>

---



---

<think><details><summary>Thought Process</summary>好的，所有的lint检查都通过了。现在我可以总结完成的工作。</details></think>

---

## 完成总结

我已经成功实现了获取住院患者信息接口，具体包括以下内容：

### ✅ 已完成的工作

1. **📄 创建数据模型** 
   - 创建了 `InPatientDetailInfo.kt` 数据类
   - 包含66个字段，覆盖患者基本信息、联系信息、住院信息、医护信息、费用信息和健康信息
   - 使用 `@JsonIgnoreProperties(ignoreUnknown = true)` 注解确保兼容性

2. **🔌 实现服务接口**
   - 在 `DongruanRocService.kt` 中添加了两个方法：
     - `getInPatientDetailInfoRaw()`: 原始HTTP接口方法
     - `getInPatientDetailInfo()`: 便捷封装方法，返回 `Result<InPatientDetailInfo>`
   - 使用 `@GET("/roc/curr-web/api/v1/common/in-patient/get/{inpatientNo}")` 注解
   - 自动处理 `domain` 和 `key` HTTP头部

3. **🧪 编写测试代码**
   - 创建了 `InPatientDetailInfoTest.kt` 测试类
   - 包含真实API测试、数据结构测试和异常情况测试
   - 使用环境变量控制测试执行

4. **📖 更新文档**
   - 更新 `README.md` 文档
   - 添加功能特性说明
   - 添加使用示例和参数说明
   - 添加详细的数据结构文档

5. **🎯 演示示例**
   - 创建了 `InPatientDetailDemo.kt` 演示程序
   - 提供完整的使用示例和输出格式
   - 包含错误处理和异常捕获

### 📋 接口特性

- **接口路径**: `/roc/curr-web/api/v1/common/in-patient/get/{inpatientNo}`
- **请求方法**: GET
- **必需参数**: `inpatientNo` (患者编号)
- **响应格式**: JSON
- **自动处理**: domain 和 key 头部认证

### 🚀 使用方式

```kotlin
// 简单调用
val result = DongruanRocService.getInPatientDetailInfo("ZY0010001201189")

if (result.isSuccess) {
    val patientInfo = result.getOrNull()
    // 处理患者信息
} else {
    // 处理错误
}
```

### 📊 数据覆盖

返回的患者详细信息包括：
- 患者基本信息（姓名、性别、身份证等）
- 联系信息（电话、地址、联系人等）
- 住院信息（科室、床号、入院时间等）
- 医护信息（医师、护士信息等）
- 费用信息（预交金、总费用、余额等）
- 健康信息（身高、体重、血型、过敏等）

现在可以根据示例文档测试接口功能了！

---

