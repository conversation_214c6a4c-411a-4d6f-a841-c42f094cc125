<!-- Generated by SpecStory -->

# 实现查询门诊费用明细接口 (2025-08-05 06:51Z)

_**User**_

阅读文档 @S125.html ，参考 @2.1.1【RF2.1.1S125】查询门诊患者费用明细列表.md ，实现`查询门诊费用明细`接口

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我阅读 S125.html 文档，参考 2.1.1【RF2.1.1S125】查询门诊患者费用明细列表.md 文件，实现"查询门诊费用明细"接口。

让我先分析一下这两个文件的内容：

1. S125.html 文件包含了接口的详细规范：
   - 接口路径：/api/v1/fee/detail/query
   - 方法：GET
   - 描述：根据挂号流水号，收费标价，处方号查询
   - 请求参数包括：clinicCode、recipeNo、orderId、payFlag、cardNo、beginDate、endDate等
   - 返回数据结构包括code、data、msg，其中data包含deptAdress、deptCode、deptName、feeList等字段

2. 2.1.1【RF2.1.1S125】查询门诊患者费用明细列表.md 文件包含了：
   - 实际的请求示例（curl命令）
   - 实际的响应示例（JSON格式）

基于这些信息，我需要实现这个接口。根据项目结构，这是一个多模块的Maven项目，看起来是医院系统的集成项目。我需要在 mospital-dongruan-roc 模块中实现这个接口。

让我先了解一下这个模块的结构和现有代码。</details></think>

---

我来帮您实现查询门诊费用明细接口。首先让我了解一下现有的项目结构和代码组织方式。

---

<details>
                <summary>Searched codebase "mospital-dongruan-roc模块的Controller类和服务类实现" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | Lundefined-48 |
| `mospital-dongruan-roc/README.md` | Lundefined-78 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L44-88 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocConfig.kt` | Lundefined-30 |
| `mospital-dongruan-roc/README.md` | L132-185 |
| `mospital-dongruan-roc/README.md` | L549-626 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocResponse.kt` | Lundefined-41 |
| `mospital-dongruan-roc/doc/API-List.md` | Lundefined-19 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L258-303 |
| `mospital-dongruan-roc/src/test/resources/dongruan-roc.yaml` | Lundefined-16 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/OutPatientServiceIntegrationTest.kt` | Lundefined-37 |
| `mospital-dongruan-roc/src/test/resources/README-TESTING.md` | Lundefined-78 |
| `mospital-dongruan-roc/pom.xml` | Lundefined-57 |
| `mospital-dongruan-roc/README.md` | L623-666 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L492-540 |

</details>

---

<think><details><summary>Thought Process</summary>现在我了解了东软ROC模块的基本结构。我看到：

1. 这是一个使用Kotlin编写的模块
2. 使用Retrofit作为HTTP客户端
3. 已经有基础的Service类 `DongruanRocService`
4. 使用Jackson进行JSON序列化
5. 有统一的Header拦截器来添加domain和key

现在我需要查看响应的数据结构定义和现有的接口实现方式，然后实现查询门诊费用明细接口。</details></think>

---

Read file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocResponse.kt

---

<think><details><summary>Thought Process</summary>好的，现在我看到了统一的响应格式。接下来我需要查看现有的接口实现，特别是Service类的完整内容，了解如何定义接口方法。</details></think>

---

Read file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt

---

<think><details><summary>Thought Process</summary>我需要继续查看Service类的接口定义部分，特别是实际的Retrofit接口方法定义。让我查看更多内容来了解接口的定义模式。</details></think>

---

<details>
            <summary>Grep search for "@GET.*fee.*query" • **0** files</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>看起来费用查询接口还没有实现。让我查看现有的接口定义方式，看看Service类中是如何定义Retrofit接口的。</details></think>

---

<details>
            <summary>Grep search for "@GET" • **12** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetIdCardTypesResult.kt` | L12 | `@get:XmlElement(name = "Result")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetIdCardTypesResult.kt` | L18 | `@get:XmlElement(name = "IDCardTypeList")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetIdCardTypesResult.kt` | L23 | `@get:XmlElement(name = "Name")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetIdCardTypesResult.kt` | L26 | `@get:XmlElement(name = "Code")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/CancelSubscriptionResult.kt` | L19 | `@get:XmlElement(name = "hisRefundId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L16 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L23 | `@get:XmlElement(name = "scheduleId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L29 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L35 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L41 | `@get:XmlElement(name = "clinicDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L47 | `@get:XmlElement(name = "clinicTime")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L53 | `@get:XmlElement(name = "clinicWeek")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L59 | `@get:XmlElement(name = "clinicType")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L65 | `@get:XmlElement(name = "clinicNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L68 | `@get:XmlElement(name = "total")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L71 | `@get:XmlElement(name = "clinicTimeQuantum")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L74 | `@get:XmlElement(name = "clinicFee")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L78 | `@get:XmlElement(name = "status")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L16 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L24 | `@get:XmlElement(name = "reportId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L30 | `@get:XmlElement(name = "reportName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L36 | `@get:XmlElement(name = "hisRegisterId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L42 | `@get:XmlElement(name = "realName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L48 | `@get:XmlElement(name = "age")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L54 | `@get:XmlElement(name = "checkType")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L60 | `@get:XmlElement(name = "checkDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L66 | `@get:XmlElement(name = "status")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L72 | `@get:XmlElement(name = "diagnosisCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L78 | `@get:XmlElement(name = "diagnosisName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L15 | `@get:XmlElement(name = "data")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L23 | `@get:XmlElement(name = "realName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L29 | `@get:XmlElement(name = "idCard")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L35 | `@get:XmlElement(name = "phone")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L41 | `@get:XmlElement(name = "sex")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L47 | `@get:XmlElement(name = "birthday")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L53 | `@get:XmlElement(name = "totleAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L59 | `@get:XmlElement(name = "usedAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L65 | `@get:XmlElement(name = "remainAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L71 | `@get:XmlElement(name = "status")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L77 | `@get:XmlElement(name = "inhospitalDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L83 | `@get:XmlElement(name = "leaveDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L89 | `@get:XmlElement(name = "inpatientArea")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L95 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L101 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L107 | `@get:XmlElement(name = "hisNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L113 | `@get:XmlElement(name = "inpatientNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L119 | `@get:XmlElement(name = "serialNumber")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L17 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L22 | `@get:XmlElement(name = "date")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L25 | `@get:XmlElement(name = "type")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L28 | `@get:XmlElement(name = "channel")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L31 | `@get:XmlElement(name = "hisNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L34 | `@get:XmlElement(name = "invoiceNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L37 | `@get:XmlElement(name = "amount")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L40 | `@get:XmlElement(name = "channelNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L13 | `@get:XmlElement(name = "realName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L17 | `@get:XmlElement(name = "admission_number")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L21 | `@get:XmlElement(name = "serialNumber")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L25 | `@get:XmlElement(name = "prepayAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L29 | `@get:XmlElement(name = "usedAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L33 | `@get:XmlElement(name = "remainAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L37 | `@get:XmlElement(name = "HouseDeptName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L16 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L23 | `@get:XmlElement(name = "scheduleId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L29 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L35 | `@get:XmlElement(name = "doctorName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L41 | `@get:XmlElement(name = "positionId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L47 | `@get:XmlElement(name = "positionName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L53 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L59 | `@get:XmlElement(name = "clinicDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L65 | `@get:XmlElement(name = "clinicTime")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L71 | `@get:XmlElement(name = "clinicWeek")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L77 | `@get:XmlElement(name = "clinicType")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L83 | `@get:XmlElement(name = "clinicNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L90 | `@get:XmlElement(name = "total")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L97 | `@get:XmlElement(name = "clinicTimeQuantum")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L104 | `@get:XmlElement(name = "clinicFee")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L111 | `@get:XmlElement(name = "status")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L16 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L23 | `@get:XmlElement(name = "doctorName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L29 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L36 | `@get:XmlElement(name = "jobTitleId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L39 | `@get:XmlElement(name = "oper_date")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L46 | `@get:XmlElement(name = "doctorAvatar")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L52 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L58 | `@get:XmlElement(name = "departmentName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L64 | `@get:XmlElement(name = "hospitalCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L70 | `@get:XmlElement(name = "hospitalName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L77 | `@get:XmlElement(name = "doctorSummary")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L84 | `@get:XmlElement(name = "doctorSpecialty")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L90 | `@get:XmlElement(name = "valid_state")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L96 | `@get:XmlElement(name = "IdNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/BaseResult.kt` | L8 | `@get:XmlElement(name = "result")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/BaseResult.kt` | L11 | `@get:XmlElement(name = "message")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L15 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L23 | `@get:XmlElement(name = "item_name")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L29 | `@get:XmlElement(name = "item_price")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L35 | `@get:XmlElement(name = "sys_class")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L41 | `@get:XmlElement(name = "item_grade")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/SubscriptionHistoryResult.kt` | L15 | `@get:XmlElement(name = "content")` |

</details>

---

<think><details><summary>Thought Process</summary>这些结果似乎是XML相关的，不是Retrofit的@GET注解。让我专门在dongruan-roc模块中查找Retrofit接口定义。</details></think>

---

<details>
            <summary>Grep search for "@GET" • **11** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/request/FindInspectionReportRequest.kt` | L17 | `@get:XmlElement(name = "queryId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/request/FindInspectionReportRequest.kt` | L23 | `@get:XmlElement(name = "startDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/request/FindInspectionReportRequest.kt` | L29 | `@get:XmlElement(name = "endDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/request/FindInspectionReportRequest.kt` | L36 | `@get:XmlElement(name = "reportType")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/CancelSubscriptionResult.kt` | L19 | `@get:XmlElement(name = "hisRefundId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L16 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L23 | `@get:XmlElement(name = "scheduleId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L29 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L35 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L41 | `@get:XmlElement(name = "clinicDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L47 | `@get:XmlElement(name = "clinicTime")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L53 | `@get:XmlElement(name = "clinicWeek")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L59 | `@get:XmlElement(name = "clinicType")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L65 | `@get:XmlElement(name = "clinicNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L68 | `@get:XmlElement(name = "total")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L71 | `@get:XmlElement(name = "clinicTimeQuantum")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L74 | `@get:XmlElement(name = "clinicFee")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryScheduleResult.kt` | L78 | `@get:XmlElement(name = "status")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L16 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L24 | `@get:XmlElement(name = "reportId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L30 | `@get:XmlElement(name = "reportName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L36 | `@get:XmlElement(name = "hisRegisterId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L42 | `@get:XmlElement(name = "realName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L48 | `@get:XmlElement(name = "age")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L54 | `@get:XmlElement(name = "checkType")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L60 | `@get:XmlElement(name = "checkDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L66 | `@get:XmlElement(name = "status")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L72 | `@get:XmlElement(name = "diagnosisCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindInspectionReportResult.kt` | L78 | `@get:XmlElement(name = "diagnosisName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L16 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L23 | `@get:XmlElement(name = "scheduleId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L29 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L35 | `@get:XmlElement(name = "doctorName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L41 | `@get:XmlElement(name = "positionId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L47 | `@get:XmlElement(name = "positionName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L53 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L59 | `@get:XmlElement(name = "clinicDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L65 | `@get:XmlElement(name = "clinicTime")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L71 | `@get:XmlElement(name = "clinicWeek")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L77 | `@get:XmlElement(name = "clinicType")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L83 | `@get:XmlElement(name = "clinicNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L90 | `@get:XmlElement(name = "total")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L97 | `@get:XmlElement(name = "clinicTimeQuantum")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L104 | `@get:XmlElement(name = "clinicFee")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryRealtimeScheduleResult.kt` | L111 | `@get:XmlElement(name = "status")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L15 | `@get:XmlElement(name = "data")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L23 | `@get:XmlElement(name = "realName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L29 | `@get:XmlElement(name = "idCard")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L35 | `@get:XmlElement(name = "phone")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L41 | `@get:XmlElement(name = "sex")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L47 | `@get:XmlElement(name = "birthday")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L53 | `@get:XmlElement(name = "totleAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L59 | `@get:XmlElement(name = "usedAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L65 | `@get:XmlElement(name = "remainAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L71 | `@get:XmlElement(name = "status")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L77 | `@get:XmlElement(name = "inhospitalDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L83 | `@get:XmlElement(name = "leaveDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L89 | `@get:XmlElement(name = "inpatientArea")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L95 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L101 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L107 | `@get:XmlElement(name = "hisNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L113 | `@get:XmlElement(name = "inpatientNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryInHospitalPatientResult.kt` | L119 | `@get:XmlElement(name = "serialNumber")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L13 | `@get:XmlElement(name = "realName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L17 | `@get:XmlElement(name = "admission_number")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L21 | `@get:XmlElement(name = "serialNumber")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L25 | `@get:XmlElement(name = "prepayAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L29 | `@get:XmlElement(name = "usedAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L33 | `@get:XmlElement(name = "remainAmt")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetPatientExpenseResult.kt` | L37 | `@get:XmlElement(name = "HouseDeptName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/AppointmentInfo.kt` | L18 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/AppointmentInfo.kt` | L24 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/AppointmentInfo.kt` | L30 | `@get:XmlElement(name = "clinicDate")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/AppointmentInfo.kt` | L36 | `@get:XmlElement(name = "sourceNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/AppointmentInfo.kt` | L42 | `@get:XmlElement(name = "sourceOrder")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/AppointmentInfo.kt` | L48 | `@get:XmlElement(name = "visitTime")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/AppointmentInfo.kt` | L54 | `@get:XmlElement(name = "havaNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L15 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L23 | `@get:XmlElement(name = "item_name")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L29 | `@get:XmlElement(name = "item_price")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L35 | `@get:XmlElement(name = "sys_class")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/QueryFeeItemInfoResult.kt` | L41 | `@get:XmlElement(name = "item_grade")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L16 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L23 | `@get:XmlElement(name = "doctorName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L29 | `@get:XmlElement(name = "doctorCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L36 | `@get:XmlElement(name = "jobTitleId")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L39 | `@get:XmlElement(name = "oper_date")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L46 | `@get:XmlElement(name = "doctorAvatar")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L52 | `@get:XmlElement(name = "departmentCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L58 | `@get:XmlElement(name = "departmentName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L64 | `@get:XmlElement(name = "hospitalCode")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L70 | `@get:XmlElement(name = "hospitalName")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L77 | `@get:XmlElement(name = "doctorSummary")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L84 | `@get:XmlElement(name = "doctorSpecialty")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L90 | `@get:XmlElement(name = "valid_state")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/FindAllDoctorsResult.kt` | L96 | `@get:XmlElement(name = "IdNo")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L17 | `@get:XmlElement(name = "content")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L22 | `@get:XmlElement(name = "date")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L25 | `@get:XmlElement(name = "type")` |
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-neusoft/src/main/kotlin/org/mospital/neusoft/result/GetClinicBillResult.kt` | L28 | `@get:XmlElement(name = "channel")` |

</details>

---

<think><details><summary>Thought Process</summary>我需要专门在dongruan-roc目录中查找Retrofit的GET接口定义。让我使用更精确的搜索。</details></think>

---

<details>
            <summary>Grep search for "retrofit2\.http\.GET" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L15 | `import retrofit2.http.GET` |

</details>

---

<think><details><summary>Thought Process</summary>好的，我找到了导入语句。现在让我继续查看Service文件的完整内容，特别是包含Retrofit接口定义的部分。</details></think>

---

Read file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt

---

<think><details><summary>Thought Process</summary>非常好！现在我了解了整个项目的结构。我需要查看数据模型的定义来创建费用明细查询的响应模型。让我查看现有的数据模型文件。</details></think>

---

<details>
                <summary>Searched codebase "数据模型 data class 定义" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DrugPriceInfo.kt` | Lundefined-108 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/OutPatientInfo.kt` | Lundefined-148 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocResponse.kt` | Lundefined-41 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/OutpatientRegistrationInfo.kt` | Lundefined-131 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/UndrugPriceInfo.kt` | Lundefined-112 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DoctorScheduleInfo.kt` | Lundefined-135 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/RegisterBillInfo.kt` | Lundefined-112 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/TakeSchemaTo.kt` | Lundefined-107 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/SchemaTimeInfo.kt` | Lundefined-110 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/SchemaDetailInfo.kt` | Lundefined-132 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/OutPatientInfo.kt` | L138-283 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DeptInfo.kt` | Lundefined-30 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/OutPatientInfo.kt` | L276-426 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/OutpatientRegistrationInfo.kt` | L121-273 |
| `mospital-dongruan-roc/doc/ROC-公共-api.html` | L1409-1445 |

</details>

---

<think><details><summary>Thought Process</summary>现在我有了足够的信息来实现查询门诊费用明细接口。基于文档分析，我需要：

1. 创建费用明细数据模型（FeeDetailInfo等）
2. 在DongruanRocService中添加接口定义
3. 添加便捷方法

让我根据HTML文档和MD文件中的响应数据结构来创建数据模型。</details></think>

---

现在我来实现查询门诊费用明细接口。基于您提供的文档，我需要创建相关的数据模型和接口方法。

---



---

<think><details><summary>Thought Process</summary>现在我需要在DongruanRocService中添加查询门诊费用明细的接口定义。我需要添加两个方法：
1. 便捷方法：直接返回Result
2. 原始接口方法：返回DongruanRocResponse

让我读取当前的Service文件并添加这些方法。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt</summary>

undefined

**Chunk 1**
Lines added: 36, lines removed: undefined

```diff
@@ -411,4 +411,40 @@
              }
          }
+ 
+         /**
+          * 查询门诊患者费用明细列表（便捷方法，直接返回 Result）
+          * @param clinicCode 挂号流水号，可选
+          * @param recipeNo 处方号，可选
+          * @param orderId 医嘱id，可选
+          * @param payFlag 收费标识，可选
+          * @param cardNo 门诊号，可选
+          * @param beginDate 收费时间范围From，可选
+          * @param endDate 收费时间范围To，可选
+          * @return 门诊费用明细信息
+          */
+         suspend fun queryFeeDetail(
+             clinicCode: String? = null,
+             recipeNo: String? = null,
+             orderId: String? = null,
+             payFlag: String? = null,
+             cardNo: String? = null,
+             beginDate: String? = null,
+             endDate: String? = null
+         ): Result<FeeDetailInfo> {
+             return try {
+                 val response = me.queryFeeDetailRaw(
+                     clinicCode = clinicCode,
+                     recipeNo = recipeNo,
+                     orderId = orderId,
+                     payFlag = payFlag,
+                     cardNo = cardNo,
+                     beginDate = beginDate,
+                     endDate = endDate
+                 )
+                 response.toResult()
+             } catch (e: Exception) {
+                 Result.failure(e)
+             }
+         }
      }
  
```

</details>

---

<think><details><summary>Thought Process</summary>现在我需要在Service接口定义部分添加原始接口方法。让我在文件末尾添加费用查询的原始接口定义。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt</summary>

undefined

**Chunk 1**
Lines added: 22, lines removed: undefined

```diff
@@ -615,4 +615,26 @@
      ): DongruanRocResponse<OutPatientInfo>
  
+     /**
+      * 【RF2.1.1S125】查询门诊患者费用明细列表（原始接口）
+      * @param clinicCode 挂号流水号
+      * @param recipeNo 处方号
+      * @param orderId 医嘱id
+      * @param payFlag 收费标识
+      * @param cardNo 门诊号
+      * @param beginDate 收费时间范围From
+      * @param endDate 收费时间范围To
+      * @return 门诊费用明细信息响应
+      */
+     @GET("/roc/fee-service/api/v1/fee/detail/query")
+     suspend fun queryFeeDetailRaw(
+         @Query("clinicCode") clinicCode: String? = null,
+         @Query("recipeNo") recipeNo: String? = null,
+         @Query("orderId") orderId: String? = null,
+         @Query("payFlag") payFlag: String? = null,
+         @Query("cardNo") cardNo: String? = null,
+         @Query("beginDate") beginDate: String? = null,
+         @Query("endDate") endDate: String? = null
+     ): DongruanRocResponse<FeeDetailInfo>
+ 
  }
  
```

</details>

---

<think><details><summary>Thought Process</summary>现在我需要检查代码是否有语法错误，然后创建一个测试文件来演示如何使用这个新接口。</details></think>

---



---

<think><details><summary>Thought Process</summary>很好！没有语法错误。现在让我创建一个测试文件来演示如何使用这个新的接口，以及更新README文档。</details></think>

---



---

<think><details><summary>Thought Process</summary>



现在我需要更新README文档，在其中添加关于费用明细查询功能的说明。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/README.md</summary>

undefined

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -9,4 +9,5 @@
  - ✅ 根据号位占号 (RP2.2.3U068)
  - ✅ 获取挂号订单信息 (RP2.1.2S058)
+ - ✅ 查询门诊患者费用明细列表 (RF2.1.1S125)
  - 🚧 其他ROC接口 - 待实现
  
```

</details>

---

<think><details><summary>Thought Process</summary>现在我需要在README中添加关于费用明细查询的使用示例。让我找到合适的位置来添加这些内容。</details></think>

---

Read file: mospital-dongruan-roc/README.md

---

<think><details><summary>Thought Process</summary>



我需要找到README文件中合适的位置来添加费用明细查询的使用示例。让我查看更多内容来确定结构。</details></think>

---

<details>
            <summary>Grep search for "## 使用示例" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-dongruan-roc/README.md` | L33 | `## 使用示例` |

</details>

---

<think><details><summary>Thought Process</summary>



让我查看使用示例部分的结构，然后在适当的位置添加费用明细查询的示例。</details></think>

---

Read file: mospital-dongruan-roc/README.md

---

<think><details><summary>Thought Process</summary>



让我继续查看README的结构，找到一个合适的位置来添加费用明细查询的示例。</details></think>

---

<details>
            <summary>Grep search for "### 获取挂号订单信息" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-dongruan-roc/README.md` | L371 | `### 获取挂号订单信息` |

</details>

---

<think><details><summary>Thought Process</summary>



让我查看获取挂号订单信息部分的内容，然后在其后添加费用明细查询的示例。</details></think>

---

Read file: mospital-dongruan-roc/README.md

---

<think><details><summary>Thought Process</summary>



现在让我创建一个todo来组织任务，然后在获取挂号订单信息部分之后添加费用明细查询的示例。</details></think>

---

## Todo List

- [-] 实现查询门诊费用明细接口
- [x] 创建费用明细数据模型 (FeeDetailInfo, FeeItem)
- [x] 在DongruanRocService中添加查询费用明细的方法
- [x] 创建测试文件演示接口使用
- [-] 更新README文档添加使用示例

---

<think><details><summary>Thought Process</summary>



好的，现在让我在README中的获取挂号订单信息部分之后添加费用明细查询的使用示例。让我找到合适的位置插入。</details></think>

---

Read file: mospital-dongruan-roc/README.md

---

<think><details><summary>Thought Process</summary>



我需要在README中合适的位置插入费用明细查询的使用示例。让我在挂号订单信息部分之后添加费用明细查询示例。</details></think>

---

The string to replace was not found in the file.

---

<think><details><summary>Thought Process</summary>



看起来我需要找到README中正确的位置来插入内容。让我先查看README的结构。</details></think>

---

<details>
            <summary>Grep search for "## 测试运行" • **0** files</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>



让我重新查看README的结构，找到一个合适的位置来添加费用明细查询的示例。</details></think>

---

Read file: mospital-dongruan-roc/README.md

---

<think><details><summary>Thought Process</summary>



我找到了合适的位置。让我在便捷方法部分之后添加费用明细查询的示例。</details></think>

---

The string to replace was not found in the file.

---

<think><details><summary>Thought Process</summary>



让我先查看文件中便捷方法部分的确切内容，然后进行替换。</details></think>

---

<details>
            <summary>Grep search for "hasTransNo" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/IdeaProjects/mospital/mospital-dongruan-roc/README.md` | L514 | `val hasTransNo = bill.hasTransNo              // Boolean` |

</details>

---

<think><details><summary>Thought Process</summary>



让我查看这个部分的确切内容。</details></think>

---

Read file: mospital-dongruan-roc/README.md

---

<think><details><summary>Thought Process</summary>



我找到了正确的位置。让我在这个代码块结束后添加费用明细查询的示例。</details></think>

---

<details><summary>Edit file: mospital-dongruan-roc/README.md</summary>

undefined

**Chunk 1**
Lines added: 124, lines removed: undefined

```diff
@@ -519,4 +519,128 @@
  val typeDesc = bill.typeDescription           // String
  val sourceDesc = bill.sourceDescription       // String
+ ```
+ 
+ ### 查询门诊费用明细
+ 
+ ```kotlin
+ import org.mospital.dongruan.roc.DongruanRocService
+ 
+ // 1. 根据挂号流水号查询费用明细
+ val result1 = DongruanRocService.queryFeeDetail(clinicCode = "16779894")
+ 
+ // 2. 根据处方号查询费用明细
+ val result2 = DongruanRocService.queryFeeDetail(recipeNo = "120797052")
+ 
+ // 3. 根据门诊号查询费用明细
+ val result3 = DongruanRocService.queryFeeDetail(cardNo = "0002805085")
+ 
+ // 4. 根据时间范围查询费用明细
+ val result4 = DongruanRocService.queryFeeDetail(
+     cardNo = "0002805085",
+     beginDate = "2025-07-15",
+     endDate = "2025-07-15"
+ )
+ 
+ // 5. 查询已缴费的费用明细
+ val result5 = DongruanRocService.queryFeeDetail(
+     clinicCode = "16779894",
+     payFlag = "1"  // 1表示已缴费
+ )
+ 
+ // 6. 组合查询条件
+ val result6 = DongruanRocService.queryFeeDetail(
+     clinicCode = "16779894",
+     payFlag = "1",
+     beginDate = "2025-07-15",
+     endDate = "2025-07-15"
+ )
+ 
+ // 处理查询结果
+ result1.fold(
+     onSuccess = { feeDetail ->
+         println("科室信息: ${feeDetail.deptName} (${feeDetail.deptCode})")
+         println("科室地址: ${feeDetail.deptAdress}")
+         println("看诊时间: ${feeDetail.regDate}")
+         println("费用汇总: 自费=${feeDetail.ownCost}, 报销=${feeDetail.pubCost}, 优惠=${feeDetail.payCost}")
+         
+         feeDetail.feeList?.forEach { item ->
+             println("费用明细: ${item.itemName} - 数量:${item.qty} 单价:${item.singlePrice} 金额:${item.ownCost}")
+             if (item.isDrug) {
+                 println("  药品信息: 规格=${item.spesc}, 用法=${item.usageName}, 频次=${item.frequencyName}")
+             }
+             println("  执行科室: ${item.execDpnm}")
+             println("  医生: ${item.docName}")
+             println("  缴费状态: ${if (item.isPaid) "已缴费" else "未缴费"}")
+             println("  缴费时间: ${item.feeDate}")
+             println("  发票号: ${item.invoiceNo}")
+             println("---")
+         }
+         
+         // 统计信息
+         val drugItems = feeDetail.feeList?.filter { it.isDrug } ?: emptyList()
+         val nonDrugItems = feeDetail.feeList?.filter { !it.isDrug } ?: emptyList()
+         println("药品项目数量: ${drugItems.size}")
+         println("非药品项目数量: ${nonDrugItems.size}")
+     },
+     onFailure = { exception ->
+         println("查询失败: ${exception.message}")
+     }
+ )
+ ```
+ 
+ ### 费用明细字段说明
+ 
+ #### FeeDetailInfo（费用明细信息）
+ 
+ | 字段 | 类型 | 说明 |
+ |------|------|------|
+ | deptCode | String? | 科室编码 |
+ | deptName | String? | 科室名称 |
+ | deptAdress | String? | 科室地址 |
+ | regDate | LocalDateTime? | 看诊时间 |
+ | payCost | BigDecimal? | 优惠费用 |
+ | pubCost | BigDecimal? | 报销费用 |
+ | ownCost | BigDecimal? | 自费费用 |
+ | feeList | List&lt;FeeItem&gt;? | 费用明细列表 |
+ 
+ #### FeeItem（费用明细项目）
+ 
+ | 字段 | 类型 | 说明 |
+ |------|------|------|
+ | recipeNo | String? | 处方号 |
+ | sequenceNo | Int? | 处方内项目流水号 |
+ | itemCode | String? | 项目编码 |
+ | itemName | String? | 项目名称 |
+ | qty | BigDecimal? | 数量 |
+ | singlePrice | BigDecimal? | 单价 |
+ | payCost | BigDecimal? | 优惠费用 |
+ | pubCost | BigDecimal? | 报销费用 |
+ | ownCost | BigDecimal? | 自费费用 |
+ | unit | String? | 单位 |
+ | payFlag | String? | 缴费标记 |
+ | feeDate | LocalDateTime? | 缴费时间 |
+ | drugFlag | String? | 药品标记 |
+ | docName | String? | 医生名称 |
+ | execDpnm | String? | 执行科室名称 |
+ | invoiceNo | String? | 发票号 |
+ | pactName | String? | 合同单位名称 |
+ 
+ #### 费用明细便捷方法
+ 
+ ```kotlin
+ // 判断是否为药品
+ val isDrug = feeItem.isDrug                   // Boolean
+ 
+ // 判断是否已缴费  
+ val isPaid = feeItem.isPaid                   // Boolean
+ 
+ // 判断是否已确认执行
+ val isConfirmed = feeItem.isConfirmed         // Boolean
+ 
+ // 判断是否已取消
+ val isCanceled = feeItem.isCanceled           // Boolean
+ 
+ // 计算总费用
+ val totalCost = feeItem.totalCost             // BigDecimal?
  ```
  
```

</details>

---

<think><details><summary>Thought Process</summary>



现在我需要检查所有修改后的文件是否有错误，并更新TODO状态。让我检查代码是否有问题。</details></think>

---



---

<think><details><summary>Thought Process</summary>



这只是一个HTML文件的警告，不是我们新代码的问题。让我最后更新TODO状态，并创建一个简单的使用示例文档。</details></think>

---

## Todo List

- [x] 实现查询门诊费用明细接口
- [x] 创建费用明细数据模型 (FeeDetailInfo, FeeItem)
- [x] 在DongruanRocService中添加查询费用明细的方法
- [x] 创建测试文件演示接口使用
- [x] 更新README文档添加使用示例

---

<think><details><summary>Thought Process</summary>



现在我来创建一个简单的使用示例文档，总结这个实现。</details></think>

---



---

<think><details><summary>Thought Process</summary>



现在让我总结一下实现的内容。</details></think>

---

## 实现总结

我已经成功实现了查询门诊费用明细接口（RF2.1.1S125）。以下是具体实现的内容：

### ✅ 完成的任务

1. **数据模型创建** (`FeeDetailInfo.kt`)
   - `FeeDetailInfo`: 费用明细主信息（科室、费用汇总等）
   - `FeeItem`: 费用明细项目（药品、检查、治疗等具体项目）
   - 提供便捷方法：`isDrug`、`isPaid`、`isConfirmed`、`totalCost` 等

2. **接口实现** (`DongruanRocService.kt`)
   - 添加便捷方法：`queryFeeDetail()` - 直接返回 `Result<FeeDetailInfo>`
   - 添加原始接口：`queryFeeDetailRaw()` - 返回 `DongruanRocResponse<FeeDetailInfo>`
   - 支持多种查询条件：挂号流水号、处方号、门诊号、时间范围等

3. **测试文件** (`FeeDetailServiceTest.kt`)
   - 提供6个不同场景的测试用例
   - 演示各种查询方式和数据处理方法

4. **文档更新**
   - 更新 `README.md` 添加费用明细查询功能和使用示例
   - 创建详细的使用指南文档 (`费用明细查询接口使用指南.md`)

### 🎯 核心功能特性

- **多查询条件支持**: 挂号流水号、处方号、门诊号、医嘱ID、缴费状态、时间范围
- **详细数据结构**: 包含50+个字段，覆盖费用、医生、科室、药品等信息
- **便捷数据处理**: 提供药品判断、缴费状态、费用计算等便捷方法
- **类型安全**: 使用 Kotlin 的强类型系统和 Result 模式进行错误处理
- **统一架构**: 遵循项目现有的设计模式和代码风格

### 📋 使用示例

```kotlin
// 简单查询
val result = DongruanRocService.queryFeeDetail(clinicCode = "16779894")

// 处理结果
result.fold(
    onSuccess = { feeDetail ->
        println("科室: ${feeDetail.deptName}")
        println("总费用: ${feeDetail.ownCost}")
        
        feeDetail.feeList?.forEach { item ->
            println("${item.itemName}: ${item.ownCost}元")
            if (item.isDrug) {
                println("  药品规格: ${item.spesc}")
            }
        }
    },
    onFailure = { exception ->
        println("查询失败: ${exception.message}")
    }
)
```

### 📁 新增文件

1. `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/FeeDetailInfo.kt`
2. `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/FeeDetailServiceTest.kt` 
3. `mospital-dongruan-roc/doc/费用明细查询接口使用指南.md`

### 📝 修改文件

1. `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` - 添加接口方法
2. `mospital-dongruan-roc/README.md` - 更新功能列表和使用示例

费用明细查询接口现已完全实现并可投入使用。所有代码都通过了语法检查，遵循项目现有的代码规范和架构设计。

---

