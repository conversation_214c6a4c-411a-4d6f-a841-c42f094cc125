<!-- Generated by SpecStory -->

# 为方法增加额外参数以支持配置 (2025-06-27 06:39Z)

_**User**_

为 `createWithRequestIdLogging` 方法增加额外的参数，允许为 `OkHttpClient.Builder()` 应用额外的代码块。重构后应该支持如下用法：
```
            val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = config.connectTimeout,
                readTimeout = config.readTimeout,
                useUuidRequestId = true,
                configurator = (builder) -> {
                    builder.addInterceptor(HeaderInterceptor(config.domain, config.key))
                }
            )
```

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要为 `createWithRequestIdLogging` 方法增加一个额外的参数，允许传入一个配置代码块来进一步自定义 `OkHttpClient.Builder()`。从用户的示例来看，他们希望能够在创建 HttpClient 时就直接应用额外的拦截器，而不是像现在这样需要先创建然后再用 `.newBuilder()` 来添加拦截器。

让我分析一下现有的代码：

1. 当前的 `HttpClientFactory.createWithRequestIdLogging` 方法在内部创建了一个 `OkHttpClient.Builder()`，设置了超时、dispatcher 和 logging interceptor，然后 build() 返回。

2. 在 `DongruanRocService.kt` 中，现在的用法是：
```kotlin
val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
    logger = logger,
    connectTimeout = config.connectTimeout,
    readTimeout = config.readTimeout,
    useUuidRequestId = true
).newBuilder()
    .addInterceptor(HeaderInterceptor(config.domain, config.key))
    .build()
```

用户希望改成：
```kotlin
val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
    logger = logger,
    connectTimeout = config.connectTimeout,
    readTimeout = config.readTimeout,
    useUuidRequestId = true,
    configurator = (builder) -> {
        builder.addInterceptor(HeaderInterceptor(config.domain, config.key))
    }
)
```

这样可以避免需要调用 `.newBuilder()` 和额外的 `.build()`。</details></think>

---

我会为 `createWithRequestIdLogging` 方法添加一个 `configurator` 参数，允许在构建过程中应用额外的配置。让我同时更新相关的使用代码。

---

```diff
       * @param maxRequestsPerHost 每个主机的最大请求数
       * @param useUuidRequestId 是否使用UUID作为请求ID，false则使用自增ID
+      * @param configurator 额外的配置函数，用于在构建前对Builder进行额外配置
       * @return 预配置的OkHttpClient实例
       */
```

```diff
          maxRequests: Int = Runtime.getRuntime().availableProcessors() * 32,
          maxRequestsPerHost: Int = maxRequests,
-         useUuidRequestId: Boolean = false
+         useUuidRequestId: Boolean = false,
+         configurator: ((OkHttpClient.Builder) -> Unit)? = null
      ): OkHttpClient {
          // 创建带请求ID的日志拦截器
```

```diff
  
          // 构建OkHttpClient
-         return OkHttpClient.Builder()
+         val builder = OkHttpClient.Builder()
              .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
              .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
```

```diff
              })
              .addInterceptor(loggingInterceptor)
-             .build()
+         
+         // 应用额外的配置
+         configurator?.invoke(builder)
+         
+         return builder.build()
      }
  
      /**
       * 创建一个标准的OkHttpClient，使用原始HttpLoggingInterceptor
+      *
+      * @param logger 日志记录器
+      * @param logLevel 日志级别
+      * @param connectTimeout 连接超时时间（毫秒）
+      * @param readTimeout 读取超时时间（毫秒）
+      * @param writeTimeout 写入超时时间（毫秒）
+      * @param maxRequests 最大并发请求数
+      * @param maxRequestsPerHost 每个主机的最大请求数
+      * @param configurator 额外的配置函数，用于在构建前对Builder进行额外配置
+      * @return 预配置的OkHttpClient实例
       */
      @JvmStatic
```

```diff
          writeTimeout: Long = 30_000,
          maxRequests: Int = Runtime.getRuntime().availableProcessors() * 32,
-         maxRequestsPerHost: Int = maxRequests
+         maxRequestsPerHost: Int = maxRequests,
+         configurator: ((OkHttpClient.Builder) -> Unit)? = null
      ): OkHttpClient {
          // 创建标准日志拦截器
```

```diff
  
          // 构建OkHttpClient
-         return OkHttpClient.Builder()
+         val builder = OkHttpClient.Builder()
              .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
              .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
```

```diff
              })
              .addInterceptor(loggingInterceptor)
-             .build()
+         
+         // 应用额外的配置
+         configurator?.invoke(builder)
+         
+         return builder.build()
      }
  } 
```

---

现在我需要更新 `DongruanRocService.kt` 中的使用方式，利用新的 `configurator` 参数：

---

```diff
                  readTimeout = config.readTimeout,
                  useUuidRequestId = true
-             ).newBuilder()
-                 .addInterceptor(HeaderInterceptor(config.domain, config.key))
-                 .build()
+             ) { builder ->
+                 builder.addInterceptor(HeaderInterceptor(config.domain, config.key))
+             }
  
              val retrofit = Retrofit.Builder()
```

---

完成！我已经成功重构了 `HttpClientFactory.createWithRequestIdLogging` 方法，现在它支持一个可选的 `configurator` 参数。主要改动包括：

## 📋 变更概述

### 🔧 HttpClientFactory.kt 更新
1. **添加 `configurator` 参数**：为 `createWithRequestIdLogging` 和 `createStandard` 方法都添加了 `configurator: ((OkHttpClient.Builder) -> Unit)? = null` 参数
2. **重构构建流程**：将原来直接调用 `.build()` 的方式改为先创建 `builder` 变量，应用 `configurator` 后再构建
3. **完善文档**：添加了详细的参数说明

### 🚀 DongruanRocService.kt 使用优化
将原来需要调用 `.newBuilder()` 和额外 `.build()` 的方式：
```kotlin
HttpClientFactory.createWithRequestIdLogging(...).newBuilder()
    .addInterceptor(HeaderInterceptor(config.domain, config.key))
    .build()
```

简化为直接使用 `configurator` 参数：
```kotlin
HttpClientFactory.createWithRequestIdLogging(...) { builder ->
    builder.addInterceptor(HeaderInterceptor(config.domain, config.key))
}
```

## ✅ 功能特性
- **向下兼容**：`configurator` 参数是可选的，不影响现有代码
- **灵活性**：可以通过 `configurator` 添加任意数量的拦截器或其他配置
- **一致性**：同时为 `createStandard` 方法也添加了相同功能
- **简洁性**：避免了 `.newBuilder().build()` 的冗余调用

现在你可以按照期望的方式使用这个方法了！

---

_**User**_

确认一下 interceptor 的顺序有没有影响，是否有必要确保日志拦截器放在最后？

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户问的是关于拦截器顺序的问题，这是一个很好的考虑。让我分析一下当前的代码：

1. 在 HttpClientFactory.createWithRequestIdLogging 中，日志拦截器是首先添加的
2. 然后通过 configurator 添加的拦截器会在日志拦截器之后添加
3. 在 DongruanRocService 中，HeaderInterceptor 是通过 configurator 添加的，所以会在日志拦截器之后

拦截器的执行顺序：
- OkHttp 中，拦截器是按照添加的顺序执行的
- 对于请求：按添加顺序执行（request方向）
- 对于响应：按添加顺序的反向执行（response方向）

所以当前的顺序是：
1. RequestIdLoggingInterceptor（日志拦截器）
2. HeaderInterceptor（头部拦截器）

这意味着：
- 请求时：先执行日志拦截器，再执行头部拦截器
- 响应时：先执行头部拦截器，再执行日志拦截器

这样的顺序可能有问题，因为：
1. 日志拦截器应该记录最终发送的请求（包含所有头部）
2. 日志拦截器应该记录最原始的响应

所以理想的顺序应该是日志拦截器在最后添加，这样它能看到完整的请求和响应。

我需要检查一下 RequestIdLoggingInterceptor 的实现，看看它是否真的需要放在最后。</details></think>

---

你提出了一个很重要的问题！拦截器的顺序确实会影响日志记录的完整性。让我先查看一下 `RequestIdLoggingInterceptor` 的实现，然后分析当前的顺序是否合适。

---

<details>
            <summary>Searched codebase "RequestIdLoggingInterceptor implementation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `mospital-common/src/main/kotlin/org/mospital/common/http/RequestIdLoggingInterceptor.kt` | L1-58 |
| `mospital-common/src/main/kotlin/org/mospital/common/http/RequestIdLoggingInterceptor.kt` | L58-99 |
| `mospital-common/src/main/kotlin/org/mospital/common/http/RequestIdLoggingInterceptor.kt` | L99-117 |
| `mospital-common/src/main/kotlin/org/mospital/common/http/HttpClientFactory.kt` | L38-87 |
| `mospital-mip/src/main/kotlin/org/mospital/mip/mobile/MobilePaySignInterceptor.kt` | L1-29 |
| `mospital-mip/src/main/kotlin/org/mospital/mip/mobile/MobilePaySignInterceptor.kt` | L29-58 |
| `mospital-neusoft/src/main/kotlin/org/mospital/neusoft/service/HisMipSoapRequestConvertInterceptor.kt` | L1-48 |
| `mospital-wecity-mip/src/main/kotlin/org/mospital/wecity/mip/WecityMipInterceptor.kt` | L1-26 |
| `mospital-common/src/main/kotlin/org/mospital/common/http/HttpClientFactory.kt` | L1-38 |
| `mospital-erhc-guanxin/src/main/kotlin/org/mospital/erhc/guanxin/Request.kt` | L151-188 |
| `mospital-dongruan-weining/src/main/kotlin/org/mospital/dongruan/weining/DongruanWeiningService.kt` | L76-105 |
| `mospital-common/src/main/kotlin/org/mospital/common/http/README.md` | L1-70 |
| `mospital-dongruan-yibao/src/main/kotlin/org/mospital/dongruan/yibao/DongruanYibaoService.kt` | L1-56 |
| `mospital-alipay/src/main/kotlin/org/mospital/alipay/AlipayService.kt` | L78-125 |
| `mospital-dongruan/src/main/kotlin/org/mospital/dongruan/DongruanService.kt` | L82-111 |
| `mospital-bsoft-hai/src/main/kotlin/org/mospital/bsoft/hai/Request.kt` | L1-44 |
| `mospital-mip/src/main/kotlin/org/mospital/mip/mobile/MobilePayService.kt` | L1-50 |
| `mospital-neusoft/src/main/kotlin/org/mospital/neusoft/util/NeusoftService.kt` | L29-61 |
| `mospital-zktec/src/main/kotlin/org/mospital/zktec/ZktecService.kt` | L29-71 |
| `mospital-witontek/src/main/kotlin/org/mospital/witontek/Request.kt` | L1-16 |
| `mospital-shine-http/src/main/kotlin/org/mospital/shine/ShineHttpService.kt` | L1-61 |
| `mospital-zlsoft/src/main/kotlin/org/mospital/zlsoft/ZLSoftServices.kt` | L1-53 |
| `mospital-erhc-guanxin/src/main/kotlin/org/mospital/erhc/guanxin/Request.kt` | L1-60 |
| `mospital-donggang/src/main/kotlin/org/mospital/donggang/einvoice/Request.kt` | L1-29 |
| `mospital-zlsoft/src/main/kotlin/org/mospital/zlsoft/bean/Request.kt` | L1-17 |

</details>

---

现在让我仔细查看 `RequestIdLoggingInterceptor` 的完整实现：

---

Read file: mospital-common/src/main/kotlin/org/mospital/common/http/RequestIdLoggingInterceptor.kt

---

现在我明白了！您提出了一个**非常重要**的问题。分析 `RequestIdLoggingInterceptor` 的实现后，我发现确实存在拦截器顺序的问题：

## 🚨 当前问题分析

### 当前拦截器顺序
1. `RequestIdLoggingInterceptor`（日志拦截器）
2. `HeaderInterceptor`（通过 configurator 添加）

### 问题所在
`RequestIdLoggingInterceptor` 内部创建了一个 `HttpLoggingInterceptor` 来记录请求和响应的详细信息，但是：

1. **请求日志不完整**：`HeaderInterceptor` 添加的 `domain` 和 `key` 头部**不会被记录**，因为日志拦截器先执行，此时头部还没有添加
2. **无法追踪最终请求**：日志中看到的请求是原始请求，而不是经过所有拦截器处理后的最终请求

## ✅ 解决方案

我们需要调整拦截器顺序，让日志拦截器放在最后，这样它就能记录到完整的请求信息（包括所有头部）：

---

```diff
                  this.maxRequestsPerHost = maxRequestsPerHost
              })
-             .addInterceptor(loggingInterceptor)
          
-         // 应用额外的配置
+         // 先应用额外的配置（如添加其他拦截器）
          configurator?.invoke(builder)
+         
+         // 最后添加日志拦截器，确保它能记录到完整的请求信息
+         builder.addInterceptor(loggingInterceptor)
          
          return builder.build()
```

---

为了保持一致性，我也需要更新 `createStandard` 方法：

---

```diff
                  this.maxRequestsPerHost = maxRequestsPerHost
              })
-             .addInterceptor(loggingInterceptor)
          
-         // 应用额外的配置
+         // 先应用额外的配置（如添加其他拦截器）
          configurator?.invoke(builder)
+         
+         // 最后添加日志拦截器，确保它能记录到完整的请求信息
+         builder.addInterceptor(loggingInterceptor)
          
          return builder.build()
```

---

现在让我们验证一下修改后的结果，通过查看最终的代码：

---

Read file: mospital-common/src/main/kotlin/org/mospital/common/http/HttpClientFactory.kt

---

<think><details><summary>Thought Process</summary>用户问的是关于拦截器顺序的问题，这是一个很好的考虑。让我分析一下当前的代码：

1. 在 HttpClientFactory.createWithRequestIdLogging 中，日志拦截器是首先添加的
2. 然后通过 configurator 添加的拦截器会在日志拦截器之后添加
3. 在 DongruanRocService 中，HeaderInterceptor 是通过 configurator 添加的，所以会在日志拦截器之后

拦截器的执行顺序：
- OkHttp 中，拦截器是按照添加的顺序执行的
- 对于请求：按添加顺序执行（request方向）
- 对于响应：按添加顺序的反向执行（response方向）

所以当前的顺序是：
1. RequestIdLoggingInterceptor（日志拦截器）
2. HeaderInterceptor（头部拦截器）

这意味着：
- 请求时：先执行日志拦截器，再执行头部拦截器
- 响应时：先执行头部拦截器，再执行日志拦截器

这样的顺序可能有问题，因为：
1. 日志拦截器应该记录最终发送的请求（包含所有头部）
2. 日志拦截器应该记录最原始的响应

所以理想的顺序应该是日志拦截器在最后添加，这样它能看到完整的请求和响应。

我需要检查一下 RequestIdLoggingInterceptor 的实现，看看它是否真的需要放在最后。</details></think>

---

完美！您的担心是完全正确的。拦截器顺序确实很重要，我已经成功修复了这个问题。

## 📋 问题总结与解决

### 🚨 原来的问题（修复前）
```kotlin
// 错误的顺序
builder.addInterceptor(loggingInterceptor)  // ❌ 日志拦截器先添加
configurator?.invoke(builder)               // HeaderInterceptor 后添加
```

**问题**：
- 日志拦截器记录的请求**缺少** `domain` 和 `key` 头部
- 无法看到经过所有拦截器处理后的**完整请求**
- 调试时日志信息不完整，难以排查问题

### ✅ 修复后的正确顺序
```kotlin
// 正确的顺序
configurator?.invoke(builder)               // ✅ 先添加其他拦截器（如HeaderInterceptor）
builder.addInterceptor(loggingInterceptor)  // ✅ 日志拦截器最后添加
```

**执行顺序**：
1. `HeaderInterceptor` 添加 `domain` 和 `key` 头部
2. `RequestIdLoggingInterceptor` 记录包含所有头部的完整请求

### 🎯 修复效果

现在日志拦截器能够记录：
- ✅ **完整的请求头部**（包括 `domain` 和 `key`）
- ✅ **最终发送的请求体**
- ✅ **原始的响应信息**
- ✅ **准确的请求耗时统计**

### 📖 最佳实践说明

这个修复体现了拦截器设计的重要原则：
- **业务拦截器**：修改请求/响应内容（如添加头部、签名等）
- **日志拦截器**：记录最终的请求/响应信息
- **顺序规则**：日志拦截器应该放在最后，确保记录完整信息

感谢您提出这个重要问题！这种对细节的关注正是确保系统可靠性和可调试性的关键。

---

_**User**_

更新 @README.md 

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要我更新 README.md 文件。从附加的文件内容来看，这是 `mospital-common/src/main/kotlin/org/mospital/common/http/README.md` 文件。

我需要更新这个 README 文件，主要应该包括：

1. 更新文档以反映新增的 `configurator` 参数
2. 添加使用 `configurator` 的示例
3. 说明拦截器顺序的重要性
4. 更新示例代码以展示新的用法

让我查看一下当前的 README 内容，然后进行相应的更新。</details></think>

---

我来更新 README.md 文件，添加新的 `configurator` 参数使用说明和拦截器顺序的重要说明：

---

```diff
  
  1. **RequestIdLoggingInterceptor**：为每个HTTP请求生成唯一ID，便于在日志中跟踪整个请求的生命周期
- 2. **HttpClientFactory**：提供便捷方法创建预配置的OkHttpClient实例
+ 2. **HttpClientFactory**：提供便捷方法创建预配置的OkHttpClient实例，支持自定义配置
  
  ## 使用示例
  
- ### 使用带请求ID的HTTP客户端
+ ### 基础使用 - 带请求ID的HTTP客户端
  
  ```kotlin
```

```diff
      .build()
  ```
+ 
+ ### 高级使用 - 使用 configurator 添加自定义拦截器
+ 
+ ```kotlin
+ import okhttp3.Interceptor
+ import okhttp3.Response
+ import org.mospital.common.http.HttpClientFactory
+ import org.slf4j.LoggerFactory
+ 
+ // 创建日志记录器
+ val logger = LoggerFactory.getLogger(YourClass::class.java)
+ 
+ // 自定义头部拦截器
+ class HeaderInterceptor(
+     private val domain: String,
+     private val key: String
+ ) : Interceptor {
+     override fun intercept(chain: Interceptor.Chain): Response {
+         val originalRequest = chain.request()
+         val requestWithHeaders = originalRequest.newBuilder()
+             .header("domain", domain)
+             .header("key", key)
+             .build()
+         return chain.proceed(requestWithHeaders)
+     }
+ }
+ 
+ // 使用 configurator 添加自定义拦截器
+ val httpClient = HttpClientFactory.createWithRequestIdLogging(
+     logger = logger,
+     connectTimeout = 500,
+     readTimeout = 30_000,
+     useUuidRequestId = true
+ ) { builder ->
+     // 添加自定义拦截器
+     builder.addInterceptor(HeaderInterceptor("example.com", "api-key-123"))
+     
+     // 可以添加多个拦截器
+     builder.addInterceptor { chain ->
+         val request = chain.request().newBuilder()
+             .header("User-Agent", "MyApp/1.0")
+             .build()
+         chain.proceed(request)
+     }
+     
+     // 其他配置
+     builder.retryOnConnectionFailure(true)
+ }
+ ```
  
  ### 直接使用RequestIdLoggingInterceptor
```

```diff
      .build()
  ```
+ 
+ ## 重要说明
+ 
+ ### 拦截器顺序
+ 
+ **重要**：`HttpClientFactory` 会按照正确的顺序添加拦截器：
+ 
+ 1. **先应用 `configurator` 中的配置**（包括业务拦截器）
+ 2. **最后添加日志拦截器**
+ 
+ 这样确保日志拦截器能够记录到经过所有其他拦截器处理后的**完整请求信息**，包括：
+ - 所有添加的头部信息
+ - 修改后的请求体
+ - 完整的URL参数
+ 
+ ```kotlin
+ // ✅ 正确：日志会包含 domain 和 key 头部
+ val httpClient = HttpClientFactory.createWithRequestIdLogging(logger) { builder ->
+     builder.addInterceptor(HeaderInterceptor("domain", "key"))
+ }
+ 
+ // ❌ 错误：如果手动构建，要注意顺序
+ val wrongClient = OkHttpClient.Builder()
+     .addInterceptor(loggingInterceptor)  // 先添加日志拦截器
+     .addInterceptor(headerInterceptor)   // 后添加业务拦截器
+     .build()  // 日志中将看不到头部信息
+ ```
+ 
+ ### 参数说明
+ 
+ #### createWithRequestIdLogging 参数
+ 
+ - `logger`: 日志记录器
+ - `connectTimeout`: 连接超时时间（毫秒，默认500）
+ - `readTimeout`: 读取超时时间（毫秒，默认30000）
+ - `writeTimeout`: 写入超时时间（毫秒，默认30000）
+ - `maxRequests`: 最大并发请求数（默认：CPU核心数 × 32）
+ - `maxRequestsPerHost`: 每个主机最大请求数（默认同 maxRequests）
+ - `useUuidRequestId`: 是否使用UUID作为请求ID（默认false，使用自增ID）
+ - `configurator`: 可选的配置函数，用于添加自定义拦截器或其他配置
+ 
+ #### createStandard 参数
+ 
+ - `logger`: 日志记录器
+ - `logLevel`: 日志级别（默认 BODY）
+ - 其他参数同 `createWithRequestIdLogging`
  
  ## 日志输出示例
```

```diff
  
  ```
- [REQ-1] 开始 POST http://api.example.com/users
- [REQ-1] --> POST http://api.example.com/users
- [REQ-1] Content-Type: application/json; charset=UTF-8
- [REQ-1] Content-Length: 53
- [REQ-1] {"name":"张三","age":30,"email":"<EMAIL>"}
- [REQ-1] --> END POST (53-byte body)
- [REQ-1] <-- 200 OK (150ms)
- [REQ-1] Content-Type: application/json
- [REQ-1] Content-Length: 86
- [REQ-1] {"id":1001,"name":"张三","age":30,"email":"<EMAIL>","createdAt":"2025-03-07T12:30:45Z"}
- [REQ-1] <-- END HTTP (86-byte body)
- [REQ-1] 完成 200 (180ms)
+ [REQ_1646123456789abcd] 开始 POST http://api.example.com/users
+ [REQ_1646123456789abcd] --> POST http://api.example.com/users
+ [REQ_1646123456789abcd] Content-Type: application/json; charset=UTF-8
+ [REQ_1646123456789abcd] domain: example.com
+ [REQ_1646123456789abcd] key: api-key-123
+ [REQ_1646123456789abcd] User-Agent: MyApp/1.0
+ [REQ_1646123456789abcd] Content-Length: 53
+ [REQ_1646123456789abcd] {"name":"张三","age":30,"email":"<EMAIL>"}
+ [REQ_1646123456789abcd] --> END POST (53-byte body)
+ [REQ_1646123456789abcd] <-- 200 OK (150ms)
+ [REQ_1646123456789abcd] Content-Type: application/json
+ [REQ_1646123456789abcd] Content-Length: 86
+ [REQ_1646123456789abcd] {"id":1001,"name":"张三","age":30,"email":"<EMAIL>","createdAt":"2025-03-07T12:30:45Z"}
+ [REQ_1646123456789abcd] <-- END HTTP (86-byte body)
+ [REQ_1646123456789abcd] 完成 200 (180ms)
  ```
  
  ## 优势
  
- 1. 轻松识别同一请求的所有相关日志
- 2. 在并发环境中便于追踪和分析请求流程
- 3. 有助于问题排查和性能分析
- 4. 提供了方便的工厂方法，简化客户端创建 
+ 1. **完整的日志追踪**：轻松识别同一请求的所有相关日志
+ 2. **灵活的配置**：通过 `configurator` 参数轻松添加自定义拦截器
+ 3. **正确的拦截器顺序**：确保日志记录完整的请求信息
+ 4. **并发友好**：在高并发环境中便于追踪和分析请求流程
+ 5. **问题排查**：有助于快速定位和分析问题
+ 6. **性能监控**：提供请求耗时统计
+ 7. **向下兼容**：新的 `configurator` 参数是可选的，不影响现有代码 
```

---

完成！我已经成功更新了 README.md 文档，主要增加了以下内容：

## 📋 更新内容概览

### 🆕 新增内容
1. **高级使用示例**：展示如何使用 `configurator` 参数添加自定义拦截器
2. **重要说明部分**：
   - 拦截器顺序的重要性说明
   - 正确和错误用法的对比示例
   - 详细的参数说明文档

### 📝 改进内容
1. **更详细的代码示例**：包含完整的导入语句和实际用法
2. **更真实的日志输出**：显示包含自定义头部的完整日志
3. **更全面的优势列表**：突出新功能的价值

### 🎯 重点强调
- **拦截器顺序的重要性**：通过对比示例说明正确和错误的用法
- **`configurator` 的灵活性**：展示可以添加多种类型的配置
- **向下兼容性**：强调新参数不会影响现有代码

### 📖 文档结构
- 清晰的分级标题结构
- 实用的代码示例
- 直观的日志输出展示
- 详细的参数说明

现在开发者可以通过这份更新的文档：
- 快速了解如何使用新的 `configurator` 功能
- 理解拦截器顺序的重要性
- 避免常见的配置错误
- 充分利用 HTTP 工具包的所有功能

---

