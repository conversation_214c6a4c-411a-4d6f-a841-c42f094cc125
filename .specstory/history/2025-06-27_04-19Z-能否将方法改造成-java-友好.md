<!-- Generated by SpecStory -->

# 能否将方法改造成 Java 友好 (2025-06-27 04:19Z)

_**User**_

这个方法能改造成 Java 友好的方法吗？

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想知道如何将这个 Kotlin 的 suspend 方法改造成 Java 友好的方法。让我分析一下这个方法：

1. 这是一个 suspend 方法，使用了 Kotlin 协程
2. 返回类型是 Result<List<DrugPriceInfo>>
3. 有多个可选参数（nullable）

要让它 Java 友好，需要考虑：
1. suspend 函数在 Java 中调用比较复杂，需要传入 Continuation 参数
2. Kotlin 的 Result 类型在 Java 中使用也不太方便
3. 可选参数在 Java 中没有对应概念

可能的改造方案：
1. 创建一个非 suspend 的重载方法，返回 CompletableFuture 或类似的 Java 异步类型
2. 使用 Java 友好的返回类型
3. 处理可选参数问题

让我先查看一下这个类的完整结构，了解现有的实现。</details></think>

---

我来查看一下这个类的完整结构，然后为您提供 Java 友好的改造方案。

---

Read file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt

---

现在让我查看一下相关的数据类和响应类型：

---

<details>
            <summary>Searched codebase "DongruanRocResponse DrugPriceInfo toResult" • **21** results</summary>
        
| File | Lines |
|------|-------|
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DrugPriceInfo.kt` | L1-105 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L83-115 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DrugPriceInfo.kt` | L105-131 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/DongruanRocServiceTest.kt` | L1-59 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/DongruanRocServiceTest.kt` | L59-100 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L42-83 |
| `mospital-dongruan-roc/doc/1.1.12【RC1.1.12S012】查询药品物价项目列表.md` | L1-60 |
| `mospital-dongruan-roc/README.md` | L79-156 |
| `mospital-dongruan-roc/doc/ROC-公共-api.html` | L1211-1217 |
| `mospital-dongruan-roc/doc/ROC-公共-api.html` | L1414-1443 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocResponse.kt` | L1-42 |
| `mospital-dongruan-roc/doc/1.1.13【RC1.1.13S013】查询非药品物价项目列表.md` | L1-51 |
| `mospital-dongruan-roc/doc/ROC-公共-api.html` | L1365-1414 |
| `mospital-dongruan-roc/doc/ROC-公共-api.html` | L1515-1521 |
| `mospital-dongruan-roc/doc/1.1.13【RC1.1.13S013】查询非药品物价项目列表.md` | L51-70 |
| `mospital-dongruan-roc/doc/ROC-费用-api.html` | L1788-1794 |
| `mospital-dongruan-roc/doc/ROC-医嘱-api.html` | L3618-3624 |
| `mospital-dongruan-roc/doc/ROC-费用-api.html` | L663-669 |
| `mospital-dongruan-roc/doc/ROC-医嘱-api.html` | L3975-3981 |
| `mospital-dongruan-roc/doc/ROC-费用-api.html` | L1192-1203 |
| `mospital-dongruan-roc/doc/ROC-医嘱-api.html` | L3834-3840 |

</details>

---

现在我来查找 `CompletableFuture` 和 `kotlinx.coroutines` 相关的依赖：

---

Read file: mospital-dongruan-roc/pom.xml

---

好的，我已经分析了现有代码结构。这个方法确实可以改造成 Java 友好的方法。我来为您提供几种改造方案：

## 改造方案

要将这个 Kotlin suspend 方法改造成 Java 友好的方法，我推荐以下几种方案：

### 方案一：添加 CompletableFuture 版本（推荐）

```kotlin:mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt
// ... 现有代码 ...

companion object {
    // ... 现有代码 ...
    
    /**
     * 查询药品物价项目列表（Java友好版本，返回 CompletableFuture）
     * @param vagueName 项目名称，可选
     * @param simpleSpell 拼音码，可选  
     * @param identifyKey 项目编码，可选
     * @param sysClassList 系统类别，可选
     * @return 药品物价列表的 CompletableFuture
     */
    @JvmStatic
    @JvmOverloads
    fun queryDrugPriceListAsync(
        vagueName: String? = null,
        simpleSpell: String? = null,
        identifyKey: String? = null,
        sysClassList: String? = null
    ): CompletableFuture<List<DrugPriceInfo>> {
        return CoroutineScope(Dispatchers.IO).async {
            queryDrugPriceList(
                vagueName = vagueName,
                simpleSpell = simpleSpell,
                identifyKey = identifyKey,
                sysClassList = sysClassList
            ).getOrThrow()
        }.asCompletableFuture()
    }
    
    /**
     * 查询药品物价项目列表（Java友好版本，带异常处理）
     * @param vagueName 项目名称，可选
     * @param simpleSpell 拼音码，可选
     * @param identifyKey 项目编码，可选
     * @param sysClassList 系统类别，可选
     * @param callback 回调接口
     */
    @JvmStatic
    @JvmOverloads
    fun queryDrugPriceListWithCallback(
        vagueName: String? = null,
        simpleSpell: String? = null,
        identifyKey: String? = null,
        sysClassList: String? = null,
        callback: DrugPriceQueryCallback
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = queryDrugPriceList(
                    vagueName = vagueName,
                    simpleSpell = simpleSpell,
                    identifyKey = identifyKey,
                    sysClassList = sysClassList
                )
                result.fold(
                    onSuccess = { drugList -> callback.onSuccess(drugList) },
                    onFailure = { exception -> callback.onFailure(exception) }
                )
            } catch (e: Exception) {
                callback.onFailure(e)
            }
        }
    }
}

/**
 * Java 友好的回调接口
 */
interface DrugPriceQueryCallback {
    fun onSuccess(drugList: List<DrugPriceInfo>)
    fun onFailure(exception: Throwable)
}
```

需要添加的导入：

```kotlin:mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt
import kotlinx.coroutines.*
import kotlinx.coroutines.future.asCompletableFuture
import java.util.concurrent.CompletableFuture
```

### 方案二：创建专门的 Java 工具类

创建一个新的 Java 工具类：

```kotlin:mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocJavaAPI.kt
package org.mospital.dongruan.roc

import kotlinx.coroutines.*
import kotlinx.coroutines.future.asCompletableFuture
import java.util.concurrent.CompletableFuture

/**
 * 东软ROC系统集成平台Java友好API
 * 专门为Java调用者提供便捷的接口
 */
object DongruanRocJavaAPI {

    /**
     * 查询药品物价项目列表
     * @param vagueName 项目名称，可选（传null表示不限制）
     * @param simpleSpell 拼音码，可选（传null表示不限制）
     * @param identifyKey 项目编码，可选（传null表示不限制）
     * @param sysClassList 系统类别，可选（传null表示不限制）
     * @return 药品物价列表的 CompletableFuture
     */
    @JvmStatic
    fun queryDrugPriceList(
        vagueName: String?,
        simpleSpell: String?,
        identifyKey: String?,
        sysClassList: String?
    ): CompletableFuture<List<DrugPriceInfo>> {
        return CoroutineScope(Dispatchers.IO).async {
            DongruanRocService.queryDrugPriceList(
                vagueName = vagueName,
                simpleSpell = simpleSpell,
                identifyKey = identifyKey,
                sysClassList = sysClassList
            ).getOrThrow()
        }.asCompletableFuture()
    }

    /**
     * 查询所有药品物价项目列表（无过滤条件）
     * @return 药品物价列表的 CompletableFuture
     */
    @JvmStatic
    fun queryAllDrugPriceList(): CompletableFuture<List<DrugPriceInfo>> {
        return queryDrugPriceList(null, null, null, null)
    }

    /**
     * 按名称查询药品物价项目列表
     * @param vagueName 项目名称
     * @return 药品物价列表的 CompletableFuture
     */
    @JvmStatic
    fun queryDrugPriceListByName(vagueName: String): CompletableFuture<List<DrugPriceInfo>> {
        return queryDrugPriceList(vagueName, null, null, null)
    }

    /**
     * 按拼音码查询药品物价项目列表
     * @param simpleSpell 拼音码
     * @return 药品物价列表的 CompletableFuture
     */
    @JvmStatic
    fun queryDrugPriceListBySpell(simpleSpell: String): CompletableFuture<List<DrugPriceInfo>> {
        return queryDrugPriceList(null, simpleSpell, null, null)
    }

    /**
     * 按项目编码查询药品物价项目列表
     * @param identifyKey 项目编码
     * @return 药品物价列表的 CompletableFuture
     */
    @JvmStatic
    fun queryDrugPriceListByCode(identifyKey: String): CompletableFuture<List<DrugPriceInfo>> {
        return queryDrugPriceList(null, null, identifyKey, null)
    }

    /**
     * 使用回调方式查询药品物价项目列表
     * @param vagueName 项目名称，可选（传null表示不限制）
     * @param simpleSpell 拼音码，可选（传null表示不限制）
     * @param identifyKey 项目编码，可选（传null表示不限制）
     * @param sysClassList 系统类别，可选（传null表示不限制）
     * @param callback 回调接口
     */
    @JvmStatic
    fun queryDrugPriceListWithCallback(
        vagueName: String?,
        simpleSpell: String?,
        identifyKey: String?,
        sysClassList: String?,
        callback: DrugPriceQueryCallback
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = DongruanRocService.queryDrugPriceList(
                    vagueName = vagueName,
                    simpleSpell = simpleSpell,
                    identifyKey = identifyKey,
                    sysClassList = sysClassList
                )
                result.fold(
                    onSuccess = { drugList -> callback.onSuccess(drugList) },
                    onFailure = { exception -> callback.onFailure(exception) }
                )
            } catch (e: Exception) {
                callback.onFailure(e)
            }
        }
    }
}

/**
 * Java 友好的回调接口
 */
interface DrugPriceQueryCallback {
    /**
     * 查询成功时回调
     * @param drugList 药品列表
     */
    fun onSuccess(drugList: List<DrugPriceInfo>)
    
    /**
     * 查询失败时回调
     * @param exception 异常信息
     */
    fun onFailure(exception: Throwable)
}
```

### 方案三：添加同步阻塞版本（谨慎使用）

```kotlin:mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt
companion object {
    // ... 现有代码 ...
    
    /**
     * 查询药品物价项目列表（同步阻塞版本，Java友好）
     * 注意：此方法会阻塞当前线程，不推荐在主线程中使用
     * @param vagueName 项目名称，可选
     * @param simpleSpell 拼音码，可选
     * @param identifyKey 项目编码，可选
     * @param sysClassList 系统类别，可选
     * @return 药品物价列表
     * @throws Exception 查询失败时抛出异常
     */
    @JvmStatic
    @JvmOverloads
    @Throws(Exception::class)
    fun queryDrugPriceListBlocking(
        vagueName: String? = null,
        simpleSpell: String? = null,
        identifyKey: String? = null,
        sysClassList: String? = null
    ): List<DrugPriceInfo> {
        return runBlocking {
            queryDrugPriceList(
                vagueName = vagueName,
                simpleSpell = simpleSpell,
                identifyKey = identifyKey,
                sysClassList = sysClassList
            ).getOrThrow()
        }
    }
}
```

## Java 调用示例

### 使用 CompletableFuture 方式

```java
// 方案一：使用现有服务类的异步方法
CompletableFuture<List<DrugPriceInfo>> future = DongruanRocService.queryDrugPriceListAsync("阿莫西林", null, null, null);

future.thenAccept(drugList -> {
    System.out.println("查询成功，共获取到 " + drugList.size() + " 条药品信息");
    for (DrugPriceInfo drug : drugList) {
        System.out.println("药品：" + drug.getName() + "，价格：" + drug.getPrice());
    }
}).exceptionally(throwable -> {
    System.err.println("查询失败：" + throwable.getMessage());
    return null;
});

// 方案二：使用专门的Java API类
CompletableFuture<List<DrugPriceInfo>> future2 = DongruanRocJavaAPI.queryDrugPriceListByName("阿莫西林");
List<DrugPriceInfo> drugs = future2.get(); // 阻塞等待结果
```

### 使用回调方式

```java
DongruanRocJavaAPI.queryDrugPriceListWithCallback("阿莫西林", null, null, null, new DrugPriceQueryCallback() {
    @Override
    public void onSuccess(List<DrugPriceInfo> drugList) {
        System.out.println("查询成功，共获取到 " + drugList.size() + " 条药品信息");
    }

    @Override
    public void onFailure(Throwable exception) {
        System.err.println("查询失败：" + exception.getMessage());
    }
});
```

### 使用同步阻塞方式

```java
try {
    List<DrugPriceInfo> drugs = DongruanRocService.queryDrugPriceListBlocking("阿莫西林", null, null, null);
    System.out.println("查询成功，共获取到 " + drugs.size() + " 条药品信息");
} catch (Exception e) {
    System.err.println("查询失败：" + e.getMessage());
}
```

## 推荐方案

我推荐**方案二**（创建专门的 Java 工具类），因为：

1. **清晰分离**：将 Java 友好的 API 和 Kotlin 原生 API 分开，避免混乱
2. **类型安全**：明确的参数和返回类型，Java 开发者使用更清晰
3. **方法重载**：提供了多个便捷方法，覆盖常见使用场景
4. **异步支持**：既支持 CompletableFuture 也支持回调模式
5. **文档友好**：专门为 Java 调用者编写的文档和注释

这样改造后，Java 开发者就可以很方便地使用这个 API，而不需要了解 Kotlin 协程的复杂性。

---

