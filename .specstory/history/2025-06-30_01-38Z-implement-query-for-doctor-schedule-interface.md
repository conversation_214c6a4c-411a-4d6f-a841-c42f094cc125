<!-- Generated by SpecStory -->

# Implement query for doctor schedule interface (2025-06-30 01:38Z)

_**User**_

@ROC-患者-api.html  1952-2065行是 `查询科室医生排班列表`接口的文档， @2.3.2【RP2.3.2S079】查询科室医生排班列表.md 是使用curl命令调用  `查询科室医生排班列表`接口 的示例。参考以上信息，和其它接口 @DongruanRocService.kt  ，实现`查询科室医生排班列表`接口。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户让我参考现有的DongruanRocService.kt文件和其他接口，实现一个"查询科室医生排班列表"接口。

根据用户提供的信息：
1. 有一个ROC-患者-api.html文档，1952-2065行是接口文档
2. 有一个2.3.2【RP2.3.2S079】查询科室医生排班列表.md文件，包含了使用curl命令调用的示例
3. 我需要参考现有的DongruanRocService.kt文件中的其他接口来实现这个新接口

首先，我需要查看ROC-患者-api.html文档中的接口规范，以及查看现有的DongruanRocService.kt文件结构。

从已经提供的信息来看：
- 从curl命令可以看到：
  - 接口URL: `/roc/patient-service/api/v1/register/schema/doctor/query`
  - 请求参数: beginTime, endTime, deptCode
  - 需要header: domain, key
  - 返回数据包含医生排班信息

从现有的DongruanRocService.kt代码可以看到：
1. 有HeaderInterceptor来自动添加domain和key header
2. 有便捷方法（companion object中的静态方法）和原始接口方法
3. 使用了DongruanRocResponse<T>作为响应包装
4. 便捷方法会捕获异常并返回Result<T>

我需要：
1. 先查看ROC-患者-api.html文档了解接口详细规范


2. 根据响应数据结构定义数据类
3. 实现原始接口方法
4. 实现便捷方法

接下来，我将仔细检查HTML文档中接口的具体规范，重点关注请求参数、响应结构和数据类型。我会根据文档信息准确定义数据模型和接口方法。</details></think>

---

我需要查看ROC-患者-api.html文档中的接口规范，然后实现对应的接口。让我先查看文档内容。

---

Read file: mospital-dongruan-roc/doc/ROC-患者-api.html

---

<think><details><summary>Thought Process</summary>现在我了解了接口的详细信息。根据文档，我需要实现查询科室医生排班列表接口。

从文档中可以看到：

1. **接口信息**：
   - Path: `/api/v1/register/schema/doctor/query`
   - Method: GET
   - 接口描述: 查询科室医生排班列表

2. **请求参数**：
   - branchCode: 院区编码（可选）
   - docCode: 医生编码（可选）
   - deptCode: 挂号科室编码（可选）
   - schemaType: 排班类型（0：科室 1：医生）（可选）
   - beginTime: 开始时间 yyyy-MM-dd（必须）
   - endTime: 结束时间 yyyy-MM-dd（必须）
   - pactCode: 合同单位:01自费,02:市医保,默认01（可选）
   - filter: 是否过滤当前时间以后的有效排班（可选）
   - limitType: 号源类型（可选）
   - dto: 查询科室医生排班列表入参（可选）

3. **返回数据**是一个DongruanRocResponse<List<DoctorScheduleInfo>>，其中DoctorScheduleInfo包含了很多字段（如医生排班信息）。

从curl示例可以看到，实际的URL是：
`/roc/patient-service/api/v1/register/schema/doctor/query`

现在我需要：
1. 定义DoctorScheduleInfo数据类
2. 在DongruanRocService中添加原始接口方法


3. 在companion object中添加便捷方法

我将检查现有的数据结构，准备实现这个接口的相关方法。接下来需要仔细分析数据类的具体字段和接口的实现细节。</details></think>

---

现在我需要查看现有的数据类结构，然后实现这个新接口。让我先查看一下现有的数据类定义。

---

<details>
            <summary>Searched codebase "DongruanRocResponse DrugPriceInfo UndrugPriceInfo DeptInfo data class" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/UndrugPriceInfo.kt` | L1-113 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DrugPriceInfo.kt` | L1-105 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L169-213 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L85-126 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DrugPriceInfo.kt` | L105-131 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceTest.kt` | L506-551 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DeptInfo.kt` | L1-31 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L126-169 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceTest.kt` | L413-465 |
| `mospital-dongruan-roc/README.md` | L133-201 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceIntegrationTest.kt` | L248-280 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/DrugPriceServiceTest.kt` | L345-376 |
| `mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt` | L45-85 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceIntegrationTest.kt` | L121-154 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceTest.kt` | L465-506 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/DrugPriceServiceTest.kt` | L291-345 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceIntegrationTest.kt` | L89-121 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceTest.kt` | L331-371 |
| `mospital-dongruan-roc/README.md` | L76-133 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceTest.kt` | L132-186 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceIntegrationTest.kt` | L154-182 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceIntegrationTest.kt` | L23-57 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceIntegrationTest.kt` | L182-226 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/UndrugPriceServiceIntegrationTest.kt` | L57-89 |
| `mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/DrugPriceServiceIntegrationTest.kt` | L93-125 |

</details>

---

<think><details><summary>Thought Process</summary>现在我已经了解了现有的数据结构。我需要查看DongruanRocResponse的定义，然后创建DoctorScheduleInfo数据类，最后实现查询科室医生排班列表的接口。

让我先查看DongruanRocResponse的定义：</details></think>

---

现在让我查看DongruanRocResponse的定义，然后基于文档创建医生排班信息的数据类。

---

Tool call timed out after 12s (codebase search)

---

<think><details><summary>Thought Process</summary>查找超时了，让我直接搜索文件。</details></think>

---

<details>
            <summary>Searched codebase "DongruanRocResponse" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<think><details><summary>Thought Process</summary>找到了DongruanRocResponse.kt文件，让我查看它的内容。</details></think>

---

Read file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocResponse.kt

---

<think><details><summary>Thought Process</summary>好的，现在我理解了DongruanRocResponse的结构。接下来我需要根据接口文档创建DoctorScheduleInfo数据类，然后在DongruanRocService中实现查询科室医生排班列表的接口。

根据HTML文档，响应数据包含以下字段：
- beginTime: 排班开始时间 (string, date-time)
- branchCode: 院区编码 (string)
- checkFee: 检查费 (number)
- childPrice: 儿童价 (number)
- deptCode: 出诊科室编码 (string)
- deptName: 出诊科室名称 (string)
- diagFee: 诊查费 (number)
- doctCode: 出诊医生编码 (string)
- doctDeptCode: 出诊医生所属科室编码 (string)
- doctDeptName: 出诊医生所属科室名称 (string)
- doctName: 出诊医生名称 (string)
- endTime: 排班结束时间 (string, date-time)
- noonCode: 午别编码 (string)
- noonName: 午别名称 (string)
- othFee: 附加费 (number)
- regFee: 挂号费 (number)
- regLmt: 现场（线下）挂号限额 (integer)
- regPassed: 线上过时未占 (string)
- reged: 现场（线下）挂号已挂 (integer)
- reglevlCode: 挂号级别编码 (string)
- reglevlName: 挂号级别名称 (string)


- schemaId: 排班唯一标识符 (string)
- schemaType: 排班类型分类 (string)
- seeDate: 具体看诊日期 (string, date-time)
- speLmt: 特殊号源限额 (integer)
- speReged: 特殊号源已挂数 (integer)
- telLmt: 电话预约号源限额 (integer)
- telPassed: 电话预约过时未占 (string)
- telReged: 电话预约已挂数 (integer)
- telReging: 电话预约中的号源 (string)
- totalFee: 总预约费用 (number)
- visitDept: 医生出诊科室 (string)

我将构建医生排班信息数据结构，并在服务接口中实现相关方法。</details></think>

---

现在我了解了整个结构。让我创建医生排班信息的数据类，然后在服务中实现对应的接口。首先创建数据类：

---

```diff
- 
+ package org.mospital.dongruan.roc
+ 
+ import com.fasterxml.jackson.annotation.JsonIgnoreProperties
+ import java.math.BigDecimal
+ 
+ /**
+  * 医生排班信息
+  * 对应接口：【RP2.3.2S079】查询科室医生排班列表
+  */
+ @JsonIgnoreProperties(ignoreUnknown = true)
+ data class DoctorScheduleInfo(
+     /**
+      * 排班开始时间
+      */
+     val beginTime: String?,
+     
+     /**
+      * 院区编码
+      */
+     val branchCode: String?,
+     
+     /**
+      * 检查费
+      */
+     val checkFee: BigDecimal?,
+     
+     /**
+      * 儿童价
+      */
+     val childPrice: BigDecimal?,
+     
+     /**
+      * 出诊科室编码
+      */
+     val deptCode: String?,
+     
+     /**
+      * 出诊科室名称
+      */
+     val deptName: String?,
+     
+     /**
+      * 诊查费
+      */
+     val diagFee: BigDecimal?,
+     
+     /**
+      * 出诊医生编码
+      */
+     val doctCode: String?,
+     
+     /**
+      * 出诊医生所属科室编码
+      */
+     val doctDeptCode: String?,
+     
+     /**
+      * 出诊医生所属科室名称
+      */
+     val doctDeptName: String?,
+     
+     /**
+      * 出诊医生名称
+      */
+     val doctName: String?,
+     
+     /**
+      * 排班结束时间
+      */
+     val endTime: String?,
+     
+     /**
+      * 午别编码
+      */
+     val noonCode: String?,
+     
+     /**
+      * 午别名称
+      */
+     val noonName: String?,
+     
+     /**
+      * 附加费
+      */
+     val othFee: BigDecimal?,
+     
+     /**
+      * 挂号费
+      */
+     val regFee: BigDecimal?,
+     
+     /**
+      * 现场（线下）挂号限额
+      */
+     val regLmt: Int?,
+     
+     /**
+      * 线下过时未占
+      */
+     val regPassed: String?,
+     
+     /**
+      * 现场（线下）挂号已挂
+      */
+     val reged: Int?,
+     
+     /**
+      * 挂号级别编码
+      */
+     val reglevlCode: String?,
+     
+     /**
+      * 挂号级别名称
+      */
+     val reglevlName: String?,
+     
+     /**
+      * 排班ID
+      */
+     val schemaId: String?,
+     
+     /**
+      * 排班类型
+      */
+     val schemaType: String?,
+     
+     /**
+      * 看诊时间
+      */
+     val seeDate: String?,
+     
+     /**
+      * 特诊挂号限额
+      */
+     val speLmt: Int?,
+     
+     /**
+      * 特诊已挂
+      */
+     val speReged: Int?,
+     
+     /**
+      * 来电（线上）挂号限额
+      */
+     val telLmt: Int?,
+     
+     /**
+      * 线上过时未占
+      */
+     val telPassed: String?,
+     
+     /**
+      * 来电（线上）已挂号数
+      */
+     val telReged: Int?,
+     
+     /**
+      * 来电（线上）预约号数
+      */
+     val telReging: Int?,
+     
+     /**
+      * 总费用
+      */
+     val totalFee: BigDecimal?,
+     
+     /**
+      * 出诊诊室
+      */
+     val visitDept: String?
+ ) {
+     /**
+      * 判断排班类型是否为医生排班
+      */
+     val isDoctorSchedule: Boolean
+         get() = schemaType == "1"
+     
+     /**
+      * 判断排班类型是否为科室排班
+      */
+     val isDeptSchedule: Boolean
+         get() = schemaType == "0"
+     
+     /**
+      * 计算剩余号源（现场挂号）
+      */
+     val remainingRegSlots: Int?
+         get() = if (regLmt != null && reged != null) {
+             (regLmt - reged).coerceAtLeast(0)
+         } else null
+     
+     /**
+      * 计算剩余号源（线上挂号）
+      */
+     val remainingTelSlots: Int?
+         get() = if (telLmt != null && telReged != null && telReging != null) {
+             (telLmt - telReged - telReging).coerceAtLeast(0)
+         } else null
+     
+     /**
+      * 计算剩余特诊号源
+      */
+     val remainingSpeSlots: Int?
+         get() = if (speLmt != null && speReged != null) {
+             (speLmt - speReged).coerceAtLeast(0)
+         } else null
+     
+     /**
+      * 判断是否有可用号源（现场挂号）
+      */
+     val hasAvailableRegSlots: Boolean
+         get() = remainingRegSlots?.let { it > 0 } ?: false
+     
+     /**
+      * 判断是否有可用号源（线上挂号）
+      */
+     val hasAvailableTelSlots: Boolean
+         get() = remainingTelSlots?.let { it > 0 } ?: false
+     
+     /**
+      * 判断是否有可用特诊号源
+      */
+     val hasAvailableSpeSlots: Boolean
+         get() = remainingSpeSlots?.let { it > 0 } ?: false
+     
+     /**
+      * 判断是否为上午排班
+      */
+     val isMorningSchedule: Boolean
+         get() = noonCode == "1"
+     
+     /**
+      * 判断是否为下午排班
+      */
+     val isAfternoonSchedule: Boolean
+         get() = noonCode == "2"
+     
+     /**
+      * 判断是否为夜间排班
+      */
+     val isEveningSchedule: Boolean
+         get() = noonCode == "3"
+ } 
```

---

<think><details><summary>Thought Process</summary>现在我需要在DongruanRocService中添加查询科室医生排班列表的接口。根据文档和curl示例，我需要：

1. 添加原始接口方法 `queryDoctorScheduleListRaw`
2. 添加便捷方法 `queryDoctorScheduleList`

根据接口文档，请求参数包括：
- branchCode: 院区编码（可选）
- docCode: 医生编码（可选） 
- deptCode: 挂号科室编码（可选）
- schemaType: 排班类型（0：科室 1：医生）（可选）
- beginTime: 开始时间 yyyy-MM-dd（必须）
- endTime: 结束时间 yyyy-MM-dd（必须）
- pactCode: 合同单位:01自费,02:市医保,默认01（可选）
- filter: 是否过滤当前时间以后的有效排班（可选）
- limitType: 号源类型（可选）
- dto: 查询科室医生排班列表入参（可选）

URL是 `/roc/patient-service/api/v1/register/schema/doctor/query`

让我编辑DongruanRocService.kt文件，添加这些方法。</details></think>

---

现在我需要在DongruanRocService中添加查询科室医生排班列表的接口。让我在现有的Service接口和便捷方法中添加对应的实现：

---



---

<think><details><summary>Thought Process</summary>现在我需要在DongruanRocService接口中添加原始的API方法。我需要找到合适的位置添加这个方法。</details></think>

---

现在我需要在DongruanRocService接口中添加原始的API方法：

---



---

<think><details><summary>Thought Process</summary>



现在我需要检查并验证代码是否正确编译。我需要看看是否有任何导入缺失或语法错误。让我查看当前的DongruanRocService.kt文件的导入部分：</details></think>

---

现在我需要检查是否需要添加DoctorScheduleInfo的导入语句。让我查看DongruanRocService.kt文件的开头部分：

---

Read file: mospital-dongruan-roc/src/main/kotlin/org/mospital/dongruan/roc/DongruanRocService.kt

---

<think><details><summary>Thought Process</summary>



很好，看起来代码结构正确。由于DongruanRocService和DoctorScheduleInfo都在同一个包 `org.mospital.dongruan.roc` 中，所以不需要额外的导入语句。

现在让我验证一下代码是否能正确编译，通过运行编译命令：</details></think>

---

现在让我验证代码是否能正确编译：

---

```bash
cd mospital-dongruan-roc && mvn clean compile -q
```

```
WARNING: A terminally deprecated method in sun.misc.Unsafe has been called
WARNING: sun.misc.Unsafe::staticFieldBase has been called by com.google.inject.i
nternal.aop.HiddenClassDefin
er (file:/opt/homebrew/Cellar/maven/3.9.10/libexec/lib/guice-5.1.0-classes.jar)
                            WARNING: Please consider reporting this to the maint
ainers of class com.google.inject.internal.aop.HiddenCla
ssDefiner
                            WARNING: sun.misc.Unsafe::staticFieldBase will be re
moved in a future release

```

---

<think><details><summary>Thought Process</summary>



编译成功了！没有语法错误。现在让我创建一个简单的测试来验证接口是否工作正常。让我创建一个简单的测试文件：</details></think>

---

很好！编译成功了。现在让我创建一个简单的测试来验证接口实现是否正确。让我创建一个基本的测试文件：

---

```diff
- 
+ package org.mospital.dongruan.roc
+ 
+ import io.mockk.coEvery
+ import io.mockk.coVerify
+ import io.mockk.mockk
+ import kotlinx.coroutines.test.runTest
+ import org.junit.jupiter.api.Test
+ import org.slf4j.Logger
+ import org.slf4j.LoggerFactory
+ import java.math.BigDecimal
+ import java.time.LocalDate
+ import kotlin.test.assertEquals
+ import kotlin.test.assertFalse
+ import kotlin.test.assertNotNull
+ import kotlin.test.assertTrue
+ 
+ /**
+  * 医生排班服务测试
+  */
+ class DoctorScheduleServiceTest {
+     
+     private val logger: Logger = LoggerFactory.getLogger(DoctorScheduleServiceTest::class.java)
+     private val mockService = mockk<DongruanRocService>()
+ 
+     @Test
+     fun testDoctorScheduleInfoDataClassProperties() {
+         // 测试数据类的属性和方法
+         val schedule = createMockSchedule()
+         
+         // 测试基本属性
+         assertEquals("305839", schedule.schemaId)
+         assertEquals("002169", schedule.doctCode)
+         assertEquals("张万里", schedule.doctName)
+         assertEquals("9982", schedule.deptCode)
+         assertEquals("骨科·脊柱微创外科门诊·急救中心", schedule.deptName)
+         assertEquals("1", schedule.noonCode)
+         assertEquals("上午", schedule.noonName)
+         assertEquals("1", schedule.schemaType)
+         assertEquals("ROOT", schedule.branchCode)
+         
+         // 测试计算属性
+         assertTrue(schedule.isDoctorSchedule, "应该为医生排班")
+         assertFalse(schedule.isDeptSchedule, "不应该为科室排班")
+         assertTrue(schedule.isMorningSchedule, "应该为上午排班")
+         assertFalse(schedule.isAfternoonSchedule, "不应该为下午排班")
+         assertFalse(schedule.isEveningSchedule, "不应该为夜间排班")
+         
+         // 测试号源计算
+         assertEquals(30, schedule.remainingRegSlots, "剩余现场号源应该正确")
+         assertEquals(0, schedule.remainingTelSlots, "剩余线上号源应该正确")
+         assertTrue(schedule.hasAvailableRegSlots, "应该有可用现场号源")
+         assertFalse(schedule.hasAvailableTelSlots, "不应该有可用线上号源")
+         
+         logger.info("单元测试 - 数据类属性测试通过")
+     }
+ 
+     @Test
+     fun testDoctorScheduleInfoAfternoonSchedule() {
+         // 测试下午排班
+         val afternoonSchedule = createMockSchedule(
+             noonCode = "2",
+             noonName = "下午"
+         )
+         
+         assertFalse(afternoonSchedule.isMorningSchedule, "不应该为上午排班")
+         assertTrue(afternoonSchedule.isAfternoonSchedule, "应该为下午排班")
+         assertFalse(afternoonSchedule.isEveningSchedule, "不应该为夜间排班")
+         
+         logger.info("单元测试 - 下午排班测试通过")
+     }
+ 
+     @Test
+     fun testDoctorScheduleInfoDeptSchedule() {
+         // 测试科室排班
+         val deptSchedule = createMockSchedule(schemaType = "0")
+         
+         assertFalse(deptSchedule.isDoctorSchedule, "不应该为医生排班")
+         assertTrue(deptSchedule.isDeptSchedule, "应该为科室排班")
+         
+         logger.info("单元测试 - 科室排班测试通过")
+     }
+ 
+     @Test
+     fun testDoctorScheduleInfoNoAvailableSlots() {
+         // 测试无可用号源的情况
+         val fullSchedule = createMockSchedule(
+             regLmt = 30,
+             reged = 30,
+             telLmt = 0,
+             telReged = 0,
+             telReging = 0
+         )
+         
+         assertEquals(0, fullSchedule.remainingRegSlots, "剩余现场号源应该为0")
+         assertEquals(0, fullSchedule.remainingTelSlots, "剩余线上号源应该为0")
+         assertFalse(fullSchedule.hasAvailableRegSlots, "不应该有可用现场号源")
+         assertFalse(fullSchedule.hasAvailableTelSlots, "不应该有可用线上号源")
+         
+         logger.info("单元测试 - 无可用号源测试通过")
+     }
+ 
+     @Test
+     fun testQueryDoctorScheduleListMock() {
+         runTest {
+             // 准备Mock数据
+             val mockScheduleList = listOf(createMockSchedule())
+             val mockResponse = DongruanRocResponse(
+                 code = 200,
+                 msg = "操作成功！",
+                 data = mockScheduleList
+             )
+ 
+             // 配置Mock行为
+             coEvery {
+                 mockService.queryDoctorScheduleListRaw(
+                     beginTime = "2025-07-01",
+                     endTime = "2025-07-15",
+                     deptCode = "9982",
+                     branchCode = null,
+                     docCode = null,
+                     schemaType = null,
+                     pactCode = null,
+                     filter = null,
+                     limitType = null,
+                     dto = null
+                 )
+             } returns mockResponse
+ 
+             // 执行测试
+             val response = mockService.queryDoctorScheduleListRaw(
+                 beginTime = "2025-07-01",
+                 endTime = "2025-07-15",
+                 deptCode = "9982"
+             )
+ 
+             // 验证结果
+             assertTrue(response.isSuccess, "响应应该成功")
+             val scheduleList = response.data!!
+             assertEquals(1, scheduleList.size, "应该返回一条排班记录")
+             
+             val schedule = scheduleList.first()
+             assertEquals("张万里", schedule.doctName)
+             assertEquals("9982", schedule.deptCode)
+             assertEquals("305839", schedule.schemaId)
+             
+             logger.info("单元测试 - 查询医生排班列表Mock测试通过")
+ 
+             // 验证Mock调用
+             coVerify(exactly = 1) {
+                 mockService.queryDoctorScheduleListRaw(
+                     beginTime = "2025-07-01",
+                     endTime = "2025-07-15",
+                     deptCode = "9982",
+                     branchCode = null,
+                     docCode = null,
+                     schemaType = null,
+                     pactCode = null,
+                     filter = null,
+                     limitType = null,
+                     dto = null
+                 )
+             }
+         }
+     }
+ 
+     @Test
+     fun testQueryDoctorScheduleListWithAllParameters() {
+         runTest {
+             // 测试带全部参数的查询
+             val mockScheduleList = listOf(createMockSchedule())
+             val mockResponse = DongruanRocResponse(
+                 code = 200,
+                 msg = "操作成功！",
+                 data = mockScheduleList
+             )
+ 
+             coEvery {
+                 mockService.queryDoctorScheduleListRaw(
+                     beginTime = "2025-07-01",
+                     endTime = "2025-07-15",
+                     branchCode = "ROOT",
+                     docCode = "002169",
+                     deptCode = "9982",
+                     schemaType = "1",
+                     pactCode = "01",
+                     filter = 1,
+                     limitType = "1",
+                     dto = null
+                 )
+             } returns mockResponse
+ 
+             val response = mockService.queryDoctorScheduleListRaw(
+                 beginTime = "2025-07-01",
+                 endTime = "2025-07-15",
+                 branchCode = "ROOT",
+                 docCode = "002169",
+                 deptCode = "9982",
+                 schemaType = "1",
+                 pactCode = "01",
+                 filter = 1,
+                 limitType = "1",
+                 dto = null
+             )
+ 
+             assertTrue(response.isSuccess, "全参数查询应该成功")
+             assertNotNull(response.data, "应该返回数据")
+             
+             logger.info("单元测试 - 全参数查询测试通过")
+         }
+     }
+ 
+     // Helper method for creating mock data
+     private fun createMockSchedule(
+         schemaId: String = "305839",
+         doctCode: String = "002169",
+         doctName: String = "张万里",
+         deptCode: String = "9982",
+         deptName: String = "骨科·脊柱微创外科门诊·急救中心",
+         noonCode: String = "1",
+         noonName: String = "上午",
+         schemaType: String = "1",
+         branchCode: String = "ROOT",
+         regLmt: Int = 30,
+         reged: Int = 0,
+         telLmt: Int = 0,
+         telReged: Int = 0,
+         telReging: Int = 0
+     ): DoctorScheduleInfo = DoctorScheduleInfo(
+         beginTime = "2025-07-04 10:00:00",
+         branchCode = branchCode,
+         checkFee = BigDecimal.ZERO,
+         childPrice = BigDecimal("18"),
+         deptCode = deptCode,
+         deptName = deptName,
+         diagFee = BigDecimal.ZERO,
+         doctCode = doctCode,
+         doctDeptCode = "0006",
+         doctDeptName = "急救中心(A)",
+         doctName = doctName,
+         endTime = "2025-07-04 13:30:00",
+         noonCode = noonCode,
+         noonName = noonName,
+         othFee = BigDecimal.ZERO,
+         regFee = BigDecimal("18"),
+         regLmt = regLmt,
+         regPassed = "0",
+         reged = reged,
+         reglevlCode = "3",
+         reglevlName = "副主任医师门诊",
+         schemaId = schemaId,
+         schemaType = schemaType,
+         seeDate = "2025-07-04 00:00:00",
+         speLmt = null,
+         speReged = 0,
+         telLmt = telLmt,
+         telPassed = "0",
+         telReged = telReged,
+         telReging = telReging,
+         totalFee = BigDecimal("18"),
+         visitDept = "院本部二楼北1诊室"
+     )
+ } 
```

---

<think><details><summary>Thought Process</summary>



现在让我运行测试来验证我们的实现是否正确：</details></think>

---

现在让我运行测试来验证我们的实现：

---

```bash
mvn test -Dtest=DoctorScheduleServiceTest -q
```

```
(base) ~/IdeaProjects/mospital/mospital-dongruan-roc (master ✗) mvn test -Dtest=DoctorScheduleServiceTest -q
aster ✗) mvn test -Dtest=DoctorScheduleServiceTest -q
WARNING: A terminally deprecated method in sun.misc.Unsafe has been called
WARNING: sun.misc.Unsafe::staticFieldBase has been called by com.google.inject.internal.aop.HiddenClassDefin
er (file:/opt/homebrew/Cellar/maven/3.9.10/libexec/lib/guice-5.1.0-classes.jar)
WARNING: Please consider reporting this to the maintainers of class com.google.inject.internal.aop.HiddenCla
ssDefiner
WARNING: sun.misc.Unsafe::staticFieldBase will be removed in a future release
OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classp
ath has been appended
WARNING: A Java agent has been loaded dynamically (/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy
-agent/1.14.6/byte-buddy-agent-1.14.6.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this war
ning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more inform
ation
WARNING: Dynamic loading of agents will be disallowed by default in a future release
2025-06-30 10:46:20.619 [WARN] InliningClassTransformer - Failed to transform class java/lang/Object
java.lang.IllegalArgumentException: Java 24 (68) is not supported by the current version of Byte Buddy which
 officially supports Java 21 (65) - update Byte Buddy or set net.bytebuddy.experimental as a VM property
        at net.bytebuddy.utility.OpenedClassReader.of(OpenedClassReader.java:96)
        at net.bytebuddy.dynamic.scaffold.TypeWriter$Default$ForInlining.create(TypeWriter.java:4011)
        at net.bytebuddy.dynamic.scaffold.TypeWriter$Default.make(TypeWriter.java:2224)
        at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase$UsingTypeWriter.make(DynamicType.java:4055
)
        at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase.make(DynamicType.java:3739)
        at io.mockk.proxy.jvm.transformation.InliningClassTransformer.transform(InliningClassTransformer.kt:
81)
        at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257
)
        at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)
        at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)
        at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses0(Native Method)
        at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses(InstrumentationImpl.java:22
1)
        at io.mockk.proxy.jvm.transformation.JvmInlineInstrumentation.retransform(JvmInlineInstrumentation.k
t:28)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(Retransfor
mInlineInstrumentation.kt:19)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(Retransfor
mInlineInstrumentation.kt:16)
        at io.mockk.proxy.common.transformation.ClassTransformationSpecMap.applyTransformation(ClassTransfor
mationSpecMap.kt:41)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation.execute(RetransformInlineIn
strumentation.kt:16)
        at io.mockk.proxy.jvm.ProxyMaker.inline(ProxyMaker.kt:90)
        at io.mockk.proxy.jvm.ProxyMaker.proxy(ProxyMaker.kt:34)
        at io.mockk.impl.instantiation.JvmMockFactory.newProxy(JvmMockFactory.kt:34)
        at io.mockk.impl.instantiation.AbstractMockFactory.newProxy$default(AbstractMockFactory.kt:24)
        at io.mockk.impl.instantiation.AbstractMockFactory.temporaryMock(AbstractMockFactory.kt:127)
        at io.mockk.impl.recording.states.RecordingState$call$temporaryMock$1.invoke(RecordingState.kt:69)
        at io.mockk.impl.instantiation.JvmAnyValueGenerator$anyValue$2.invoke(JvmAnyValueGenerator.kt:35)
        at io.mockk.impl.instantiation.AnyValueGenerator.anyValue(AnyValueGenerator.kt:34)
        at io.mockk.impl.instantiation.JvmAnyValueGenerator.anyValue(JvmAnyValueGenerator.kt:31)
        at io.mockk.impl.recording.states.RecordingState.call(RecordingState.kt:75)
        at io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
        at io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
        at io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt
:24)
        at io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
        at io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
        at io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.
java:45)
        at org.mospital.dongruan.roc.DongruanRocService$Subclass0.queryDoctorScheduleListRaw(Unknown Source)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1$1.invokeSus
pend(DoctorScheduleServiceTest.kt:115)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1$1.invoke(Do
ctorScheduleServiceTest.kt)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1$1.invoke(Do
ctorScheduleServiceTest.kt)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invokeSuspend(RecordedBlockEvaluator.k
t:27)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invoke(RecordedBlockEvaluator.kt)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invoke(RecordedBlockEvaluator.kt)
        at io.mockk.InternalPlatformDsl$runCoroutine$1.invokeSuspend(InternalPlatformDsl.kt:23)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
        at kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:94)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:70)
        at kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
        at kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
        at io.mockk.InternalPlatformDsl.runCoroutine(InternalPlatformDsl.kt:22)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2.invoke(RecordedBlockEvaluator.kt:27)
        at io.mockk.impl.eval.RecordedBlockEvaluator$enhanceWithRethrow$1.invoke(RecordedBlockEvaluator.kt:7
6)
        at io.mockk.impl.recording.JvmAutoHinter.autoHint(JvmAutoHinter.kt:23)
        at io.mockk.impl.eval.RecordedBlockEvaluator.record(RecordedBlockEvaluator.kt:39)
        at io.mockk.impl.eval.EveryBlockEvaluator.every(EveryBlockEvaluator.kt:30)
        at io.mockk.MockKDsl.internalCoEvery(API.kt:100)
        at io.mockk.MockKKt.coEvery(MockK.kt:169)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1.invokeSuspe
nd(DoctorScheduleServiceTest.kt:114)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1.invoke(Doct
orScheduleServiceTest.kt)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1.invoke(Doct
orScheduleServiceTest.kt)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.k
t:317)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
        at kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestC
oroutineScheduler.kt:99)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(Tes
tBuilders.kt:326)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
        at kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:94)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:70)
        at kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
        at kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:309)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:167)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:15
9)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest.testQueryDoctorScheduleListMock(DoctorSchedul
eServiceTest.kt:104)
        at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:
104)
        at java.base/java.lang.reflect.Method.invoke(Method.java:565)
        at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
        at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
        at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(Invoca
tionInterceptorChain.java:131)
        at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
        at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java
:152)
        at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda
$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExec
utableInvoker.java:94)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(Invoc
ationInterceptorChain.java:106)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.
java:64)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationIntercepto
rChain.java:45)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.j
ava:37)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInv
oker.java:93)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInv
oker.java:87)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethod
TestDescriptor.java:216)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescr
iptor.java:212)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.jav
a:137)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.jav
a:69)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:156)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAl
l(SameThreadHierarchicalTestExecutorService.java:41)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:160)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAl
l(SameThreadHierarchicalTestExecutorService.java:41)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:160)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(S
ameThreadHierarchicalTestExecutorService.java:35)
        at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestE
xecutor.java:57)
        at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEng
ine.java:54)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:201)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:170)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:94)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrch
estrator.java:59)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecuti
onOrchestrator.java:142)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:58)
        at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
        at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
        at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
        at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:
39)
        at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(Classpat
hAlignmentCheckingLauncherInterceptor.java:25)
        at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
        at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
        at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:
184)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvide
r.java:148)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:1
22)
        at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
        at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
        at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
        at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-30 10:46:20.642 [INFO] DoctorScheduleServiceTest - 单元测试 - 查询医生排班列表Mock测试通过
2025-06-30 10:46:20.656 [INFO] DoctorScheduleServiceTest - 单元测试 - 科室排班测试通过
2025-06-30 10:46:20.657 [INFO] DoctorScheduleServiceTest - 单元测试 - 无可用号源测试通过
2025-06-30 10:46:20.658 [INFO] DoctorScheduleServiceTest - 单元测试 - 下午排班测试通过
2025-06-30 10:46:20.659 [INFO] DoctorScheduleServiceTest - 单元测试 - 数据类属性测试通过
2025-06-30 10:46:20.662 [INFO] DoctorScheduleServiceTest - 单元测试 - 全参数查询测试通过
(base) ~/IdeaProjects/mospital/mospital-dongruan-roc (master ✗)
```

---

<think><details><summary>Thought Process</summary>



太好了！测试全部通过了！虽然有一些警告（主要是Java版本兼容性问题），但所有测试都成功运行并通过了。

现在让我最后验证一下整个模块是否能正确编译，然后为用户总结实现的内容：</details></think>

---

太好了！所有测试都通过了！让我最后验证一下整个模块的编译：

---

```bash
mvn clean compile -q
```

```
(base) ~/IdeaProjects/mospital/mospital-dongruan-roc (master ✗) mvn clean compile -q
WARNING: A terminally deprecated method in sun.misc.Unsafe has been called
WARNING: sun.misc.Unsafe::staticFieldBase has been called by com.google.inject.internal.aop.HiddenClassDefin
er (file:/opt/homebrew/Cellar/maven/3.9.10/libexec/lib/guice-5.1.0-classes.jar)
WARNING: Please consider reporting this to the maintainers of class com.google.inject.internal.aop.HiddenCla
ssDefiner
WARNING: sun.misc.Unsafe::staticFieldBase will be removed in a future release
(base) ~/IdeaProjects/mospital/mospital-dongruan-roc (master ✗)
```

---

<think><details><summary>Thought Process</summary>



完美！编译也成功了。

现在让我为用户总结一下我们完成的工作。我已经成功实现了"查询科室医生排班列表"接口，包括：

1. **DoctorScheduleInfo.kt** - 医生排班信息数据类
2. **DongruanRocService.kt** - 在服务接口中添加了原始API方法和便捷方法
3. **DoctorScheduleServiceTest.kt** - 完整的测试用例

让我准备一个总结性的回复：</details></think>

---

## 实现完成 ✅

我已经成功实现了"查询科室医生排班列表"接口，参考了现有接口的模式和文档规范。以下是具体实现内容：

### 📁 新增文件

#### 1. `DoctorScheduleInfo.kt` - 医生排班信息数据类
- 包含接口文档中定义的所有字段（32个字段）
- 提供便捷的计算属性，如：
  - `isDoctorSchedule` / `isDeptSchedule` - 判断排班类型
  - `isMorningSchedule` / `isAfternoonSchedule` / `isEveningSchedule` - 判断时段
  - `remainingRegSlots` / `remainingTelSlots` / `remainingSpeSlots` - 计算剩余号源
  - `hasAvailableRegSlots` / `hasAvailableTelSlots` / `hasAvailableSpeSlots` - 判断是否有可用号源

#### 2. `DoctorScheduleServiceTest.kt` - 完整测试用例
- 数据类属性和计算方法测试
- Mock接口调用测试
- 不同参数组合测试

### 🔧 修改文件

#### `DongruanRocService.kt` - 添加接口方法
**原始接口方法**：
```kotlin
@GET("/roc/patient-service/api/v1/register/schema/doctor/query")
suspend fun queryDoctorScheduleListRaw(
    @Query("beginTime") beginTime: String,
    @Query("endTime") endTime: String,
    @Query("branchCode") branchCode: String? = null,
    @Query("docCode") docCode: String? = null,
    @Query("deptCode") deptCode: String? = null,
    @Query("schemaType") schemaType: String? = null,
    @Query("pactCode") pactCode: String? = null,
    @Query("filter") filter: Int? = null,
    @Query("limitType") limitType: String? = null,
    @Query("dto") dto: String? = null
): DongruanRocResponse<List<DoctorScheduleInfo>>
```

**便捷方法**：
```kotlin
suspend fun queryDoctorScheduleList(
    beginDate: LocalDate,
    endDate: LocalDate,
    branchCode: String? = null,
    docCode: String? = null,
    deptCode: String? = null,
    schemaType: String? = null,
    pactCode: String? = null,
    filter: Boolean? = null,
    limitType: String? = null,
    dto: String? = null
): Result<List<DoctorScheduleInfo>>
```

### 📋 使用示例

```kotlin
// 1. 查询指定科室在特定时间段的所有医生排班
val result1 = DongruanRocService.queryDoctorScheduleList(
    beginDate = LocalDate.of(2025, 7, 1),
    endDate = LocalDate.of(2025, 7, 15),
    deptCode = "9982"
)

// 2. 查询特定医生的排班
val result2 = DongruanRocService.queryDoctorScheduleList(
    beginDate = LocalDate.of(2025, 7, 1),
    endDate = LocalDate.of(2025, 7, 15),
    docCode = "002169",
    schemaType = "1" // 医生排班
)

// 3. 查询有可用号源的排班（线上号源）
val result3 = DongruanRocService.queryDoctorScheduleList(
    beginDate = LocalDate.of(2025, 7, 1),
    endDate = LocalDate.of(2025, 7, 15),
    deptCode = "9982",
    limitType = "1", // 线上号源
    filter = true // 过滤当前时间之后的排班
)

// 处理查询结果
result1.fold(
    onSuccess = { scheduleList ->
        scheduleList.forEach { schedule ->
            println("医生：${schedule.doctName}")
            println("科室：${schedule.deptName}")
            println("时间：${schedule.beginTime} - ${schedule.endTime}")
            println("午别：${schedule.noonName}")
            println("剩余号源：${schedule.remainingRegSlots}（现场）/ ${schedule.remainingTelSlots}（线上）")
            println("费用：挂号费 ${schedule.regFee} + 诊查费 ${schedule.diagFee} = 总费用 ${schedule.totalFee}")
            println("诊室：${schedule.visitDept}")
            println("---")
        }
    },
    onFailure = { exception ->
        println("查询失败：${exception.message}")
    }
)
```

### ✅ 验证结果

- ✅ 编译成功
- ✅ 所有测试用例通过
- ✅ 接口参数与文档规范完全一致
- ✅ 响应数据结构与curl示例匹配
- ✅ 便捷方法提供友好的API调用方式

### 🔍 接口特点

1. **完整性**：支持所有文档中定义的查询参数
2. **类型安全**：使用LocalDate而非字符串处理日期
3. **易用性**：便捷方法自动处理日期格式转换和错误包装
4. **扩展性**：数据类提供丰富的计算属性，便于业务逻辑处理
5. **一致性**：与现有接口保持相同的设计模式

实现已完成，可以直接使用！🎉

---

_**User**_

参考 @DeptServiceIntegrationTest.kt @DrugPriceServiceIntegrationTest.kt ，为 `查询科室医生排班列表` 接口编写集成测试

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户希望我为"查询科室医生排班列表"接口编写集成测试，需要参考现有的两个集成测试文件：
1. DeptServiceIntegrationTest.kt - 科室查询的集成测试
2. DrugPriceServiceIntegrationTest.kt - 药品物价查询的集成测试

从这两个文件的模式来看，集成测试的特点是：

1. 使用 `@EnabledIfSystemProperty(named = "integration.tests.enabled", matches = "true")` 注解，需要设置系统属性才能运行
2. 进行真实的网络调用，需要实际的ROC服务配置
3. 测试不同的参数组合
4. 对返回结果进行验证
5. 使用日志记录测试过程和结果
6. 有适当的错误处理

对于医生排班列表的集成测试，我应该测试以下场景：
1. 基本查询（通过科室编码）
2. 通过医生编码查询
3. 通过排班类型查询（医生排班 vs 科室排班）
4. 通过院区编码查询
5. 使用过滤器查询
6. 使用号源类型查询
7. 全参数查询
8. 长时间范围查询
9. 服务连接测试

我需要创建一个新的集成测试文件：DoctorScheduleServiceIntegrationTest.kt</details></think>

---

我来为"查询科室医生排班列表"接口编写集成测试。参考现有的集成测试模式，我将创建一个完整的集成测试文件。

---



---

<think><details><summary>Thought Process</summary>看起来文件创建失败了，让我重新尝试创建这个文件。</details></think>

---



---

<think><details><summary>Thought Process</summary>看起来文件创建命令没有生效。让我先检查一下文件是否已经存在。</details></think>

---

<details>
            <summary>Searched codebase "DoctorScheduleServiceIntegrationTest" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<think><details><summary>Thought Process</summary>文件确实已经存在了。让我查看一下它的内容。</details></think>

---

Read file: mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/DoctorScheduleServiceIntegrationTest.kt

---

<think><details><summary>Thought Process</summary>看起来文件已经存在且包含了一些内容。让我查看完整的文件内容来了解现在的状态。</details></think>

---

Read file: mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/DoctorScheduleServiceIntegrationTest.kt

---

<think><details><summary>Thought Process</summary>看起来文件已经存在并且内容相当完整了。让我查看最后的部分，然后编译和运行测试来验证实现是否正确。</details></think>

---

Read file: mospital-dongruan-roc/src/test/kotlin/org/mospital/dongruan/roc/DoctorScheduleServiceIntegrationTest.kt

---

<think><details><summary>Thought Process</summary>好的，看起来集成测试文件已经非常完整了！它包含了所有必要的测试用例，参考了现有集成测试的模式。现在让我编译和运行测试来验证实现是否正确。</details></think>

---

```bash
mvn clean compile -q
```

```
(base) ~/IdeaProjects/mospital/mospital-dongruan-roc (master ✗) mvn clean compile -q
WARNING: A terminally deprecated method in sun.misc.Unsafe has been called
WARNING: sun.misc.Unsafe::staticFieldBase has been called by com.google.inject.internal.aop.HiddenClassDefin
er (file:/opt/homebrew/Cellar/maven/3.9.10/libexec/lib/guice-5.1.0-classes.jar)
WARNING: Please consider reporting this to the maintainers of class com.google.inject.internal.aop.HiddenCla
ssDefiner
WARNING: sun.misc.Unsafe::staticFieldBase will be removed in a future release
(base) ~/IdeaProjects/mospital/mospital-dongruan-roc (master ✗)
```

---

<think><details><summary>Thought Process</summary>编译成功了。现在让我运行单元测试来验证整个实现的正确性。</details></think>

---

```bash
mvn test -Dtest=DoctorScheduleServiceTest -q
```

```
(base) ~/IdeaProjects/mospital/mospital-dongruan-roc (master ✗) mvn test -Dtest=DoctorScheduleServiceTest -q
                                                                mvn test -Dtest=DoctorScheduleServiceTest -q
 
WARNING: A terminally deprecated method in sun.misc.Unsafe has been called
WARNING: sun.misc.Unsafe::staticFieldBase has been called by com.google.inject.internal.aop.HiddenClassDefin
er (file:/opt/homebrew/Cellar/maven/3.9.10/libexec/lib/guice-5.1.0-classes.jar)
WARNING: Please consider reporting this to the maintainers of class com.google.inject.internal.aop.HiddenCla
ssDefiner
WARNING: sun.misc.Unsafe::staticFieldBase will be removed in a future release
OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classp
ath has been appended
WARNING: A Java agent has been loaded dynamically (/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy
-agent/1.14.6/byte-buddy-agent-1.14.6.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this war
ning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more inform
ation
WARNING: Dynamic loading of agents will be disallowed by default in a future release
2025-06-30 11:04:34.922 [WARN] InliningClassTransformer - Failed to transform class java/lang/Object
java.lang.IllegalArgumentException: Java 24 (68) is not supported by the current version of Byte Buddy which
 officially supports Java 21 (65) - update Byte Buddy or set net.bytebuddy.experimental as a VM property
        at net.bytebuddy.utility.OpenedClassReader.of(OpenedClassReader.java:96)
        at net.bytebuddy.dynamic.scaffold.TypeWriter$Default$ForInlining.create(TypeWriter.java:4011)
        at net.bytebuddy.dynamic.scaffold.TypeWriter$Default.make(TypeWriter.java:2224)
        at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase$UsingTypeWriter.make(DynamicType.java:4055
)
        at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase.make(DynamicType.java:3739)
        at io.mockk.proxy.jvm.transformation.InliningClassTransformer.transform(InliningClassTransformer.kt:
81)
        at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257
)
        at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)
        at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)
        at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses0(Native Method)
        at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses(InstrumentationImpl.java:22
1)
        at io.mockk.proxy.jvm.transformation.JvmInlineInstrumentation.retransform(JvmInlineInstrumentation.k
t:28)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(Retransfor
mInlineInstrumentation.kt:19)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(Retransfor
mInlineInstrumentation.kt:16)
        at io.mockk.proxy.common.transformation.ClassTransformationSpecMap.applyTransformation(ClassTransfor
mationSpecMap.kt:41)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation.execute(RetransformInlineIn
strumentation.kt:16)
        at io.mockk.proxy.jvm.ProxyMaker.inline(ProxyMaker.kt:90)
        at io.mockk.proxy.jvm.ProxyMaker.proxy(ProxyMaker.kt:34)
        at io.mockk.impl.instantiation.JvmMockFactory.newProxy(JvmMockFactory.kt:34)
        at io.mockk.impl.instantiation.AbstractMockFactory.newProxy$default(AbstractMockFactory.kt:24)
        at io.mockk.impl.instantiation.AbstractMockFactory.temporaryMock(AbstractMockFactory.kt:127)
        at io.mockk.impl.recording.states.RecordingState$call$temporaryMock$1.invoke(RecordingState.kt:69)
        at io.mockk.impl.instantiation.JvmAnyValueGenerator$anyValue$2.invoke(JvmAnyValueGenerator.kt:35)
        at io.mockk.impl.instantiation.AnyValueGenerator.anyValue(AnyValueGenerator.kt:34)
        at io.mockk.impl.instantiation.JvmAnyValueGenerator.anyValue(JvmAnyValueGenerator.kt:31)
        at io.mockk.impl.recording.states.RecordingState.call(RecordingState.kt:75)
        at io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
        at io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
        at io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt
:24)
        at io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
        at io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
        at io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.
java:45)
        at org.mospital.dongruan.roc.DongruanRocService$Subclass0.queryDoctorScheduleListRaw(Unknown Source)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1$1.invokeSus
pend(DoctorScheduleServiceTest.kt:122)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1$1.invoke(Do
ctorScheduleServiceTest.kt)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1$1.invoke(Do
ctorScheduleServiceTest.kt)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invokeSuspend(RecordedBlockEvaluator.k
t:27)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invoke(RecordedBlockEvaluator.kt)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invoke(RecordedBlockEvaluator.kt)
        at io.mockk.InternalPlatformDsl$runCoroutine$1.invokeSuspend(InternalPlatformDsl.kt:23)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
        at kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:94)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:70)
        at kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
        at kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
        at io.mockk.InternalPlatformDsl.runCoroutine(InternalPlatformDsl.kt:22)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2.invoke(RecordedBlockEvaluator.kt:27)
        at io.mockk.impl.eval.RecordedBlockEvaluator$enhanceWithRethrow$1.invoke(RecordedBlockEvaluator.kt:7
6)
        at io.mockk.impl.recording.JvmAutoHinter.autoHint(JvmAutoHinter.kt:23)
        at io.mockk.impl.eval.RecordedBlockEvaluator.record(RecordedBlockEvaluator.kt:39)
        at io.mockk.impl.eval.EveryBlockEvaluator.every(EveryBlockEvaluator.kt:30)
        at io.mockk.MockKDsl.internalCoEvery(API.kt:100)
        at io.mockk.MockKKt.coEvery(MockK.kt:169)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1.invokeSuspe
nd(DoctorScheduleServiceTest.kt:121)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1.invoke(Doct
orScheduleServiceTest.kt)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest$testQueryDoctorScheduleListMock$1.invoke(Doct
orScheduleServiceTest.kt)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.k
t:317)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
        at kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestC
oroutineScheduler.kt:99)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(Tes
tBuilders.kt:326)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
        at kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:94)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:70)
        at kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
        at kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:309)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:167)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:15
9)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
        at org.mospital.dongruan.roc.DoctorScheduleServiceTest.testQueryDoctorScheduleListMock(DoctorSchedul
eServiceTest.kt:111)
        at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:
104)
        at java.base/java.lang.reflect.Method.invoke(Method.java:565)
        at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
        at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
        at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(Invoca
tionInterceptorChain.java:131)
        at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
        at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java
:152)
        at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda
$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExec
utableInvoker.java:94)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(Invoc
ationInterceptorChain.java:106)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.
java:64)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationIntercepto
rChain.java:45)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.j
ava:37)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInv
oker.java:93)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInv
oker.java:87)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethod
TestDescriptor.java:216)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescr
iptor.java:212)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.jav
a:137)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.jav
a:69)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:156)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAl
l(SameThreadHierarchicalTestExecutorService.java:41)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:160)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAl
l(SameThreadHierarchicalTestExecutorService.java:41)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:160)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(S
ameThreadHierarchicalTestExecutorService.java:35)
        at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestE
xecutor.java:57)
        at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEng
ine.java:54)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:201)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:170)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:94)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrch
estrator.java:59)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecuti
onOrchestrator.java:142)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:58)
        at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
        at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
        at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
        at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:
39)
        at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(Classpat
hAlignmentCheckingLauncherInterceptor.java:25)
        at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
        at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
        at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:
184)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvide
r.java:148)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:1
22)
        at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
        at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
        at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
        at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-30 11:04:34.946 [INFO] DoctorScheduleServiceTest - 单元测试 - 查询医生排班列表Mock测试通过
2025-06-30 11:04:34.961 [INFO] DoctorScheduleServiceTest - 单元测试 - 科室排班测试通过
2025-06-30 11:04:34.963 [INFO] DoctorScheduleServiceTest - 单元测试 - 无可用号源测试通过
2025-06-30 11:04:34.964 [INFO] DoctorScheduleServiceTest - 单元测试 - 下午排班测试通过
2025-06-30 11:04:34.965 [INFO] DoctorScheduleServiceTest - 单元测试 - 数据类属性测试通过
2025-06-30 11:04:34.970 [INFO] DoctorScheduleServiceTest - 单元测试 - 全参数查询测试通过
(base) ~/IdeaProjects/mospital/mospital-dongruan-roc (master ✗)
```

---

<think><details><summary>Thought Process</summary>虽然有一些MockK的警告（这是由于Java版本兼容性问题），但所有的单元测试都通过了！让我也确认整个模块的测试都能通过。</details></think>

---

```bash
mvn test -q
```

```
WARNING: A terminally deprecated method in sun.misc.Unsafe has been called
WARNING: sun.misc.Unsafe::staticFieldBase has been called by com.google.inject.internal.aop.HiddenClassDefin
er (file:/opt/homebrew/Cellar/maven/3.9.10/libexec/lib/guice-5.1.0-classes.jar)
WARNING: Please consider reporting this to the maintainers of class com.google.inject.internal.aop.HiddenCla
ssDefiner
WARNING: sun.misc.Unsafe::staticFieldBase will be removed in a future release
WARNING: A Java agent has been loaded dynamically (/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy
-agent/1.14.6/byte-buddy-agent-1.14.6.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this war
ning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more inform
ation
WARNING: Dynamic loading of agents will be disallowed by default in a future release
OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classp
ath has been appended
2025-06-30 11:04:47.326 [WARN] InliningClassTransformer - Failed to transform class java/lang/Object
java.lang.IllegalArgumentException: Java 24 (68) is not supported by the current version of Byte Buddy which
 officially supports Java 21 (65) - update Byte Buddy or set net.bytebuddy.experimental as a VM property
        at net.bytebuddy.utility.OpenedClassReader.of(OpenedClassReader.java:96)
        at net.bytebuddy.dynamic.scaffold.TypeWriter$Default$ForInlining.create(TypeWriter.java:4011)
        at net.bytebuddy.dynamic.scaffold.TypeWriter$Default.make(TypeWriter.java:2224)
        at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase$UsingTypeWriter.make(DynamicType.java:4055
)
        at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase.make(DynamicType.java:3739)
        at io.mockk.proxy.jvm.transformation.InliningClassTransformer.transform(InliningClassTransformer.kt:
81)
        at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257
)
        at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)
        at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)
        at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses0(Native Method)
        at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses(InstrumentationImpl.java:22
1)
        at io.mockk.proxy.jvm.transformation.JvmInlineInstrumentation.retransform(JvmInlineInstrumentation.k
t:28)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(Retransfor
mInlineInstrumentation.kt:19)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(Retransfor
mInlineInstrumentation.kt:16)
        at io.mockk.proxy.common.transformation.ClassTransformationSpecMap.applyTransformation(ClassTransfor
mationSpecMap.kt:41)
        at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation.execute(RetransformInlineIn
strumentation.kt:16)
        at io.mockk.proxy.jvm.ProxyMaker.inline(ProxyMaker.kt:90)
        at io.mockk.proxy.jvm.ProxyMaker.proxy(ProxyMaker.kt:34)
        at io.mockk.impl.instantiation.JvmMockFactory.newProxy(JvmMockFactory.kt:34)
        at io.mockk.impl.instantiation.AbstractMockFactory.newProxy$default(AbstractMockFactory.kt:24)
        at io.mockk.impl.instantiation.AbstractMockFactory.temporaryMock(AbstractMockFactory.kt:127)
        at io.mockk.impl.recording.states.RecordingState$call$temporaryMock$1.invoke(RecordingState.kt:69)
        at io.mockk.impl.instantiation.JvmAnyValueGenerator$anyValue$2.invoke(JvmAnyValueGenerator.kt:35)
        at io.mockk.impl.instantiation.AnyValueGenerator.anyValue(AnyValueGenerator.kt:34)
        at io.mockk.impl.instantiation.JvmAnyValueGenerator.anyValue(JvmAnyValueGenerator.kt:31)
        at io.mockk.impl.recording.states.RecordingState.call(RecordingState.kt:75)
        at io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
        at io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
        at io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt
:24)
        at io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
        at io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
        at io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.
java:45)
        at org.mospital.dongruan.roc.DongruanRocService$Subclass0.queryUndrugPriceListRaw(Unknown Source)
        at org.mospital.dongruan.roc.UndrugPriceServiceTest$testQueryUndrugPriceListRawEmptyResult$1$1.invok
eSuspend(UndrugPriceServiceTest.kt:211)
        at org.mospital.dongruan.roc.UndrugPriceServiceTest$testQueryUndrugPriceListRawEmptyResult$1$1.invok
e(UndrugPriceServiceTest.kt)
        at org.mospital.dongruan.roc.UndrugPriceServiceTest$testQueryUndrugPriceListRawEmptyResult$1$1.invok
e(UndrugPriceServiceTest.kt)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invokeSuspend(RecordedBlockEvaluator.k
t:27)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invoke(RecordedBlockEvaluator.kt)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2$1.invoke(RecordedBlockEvaluator.kt)
        at io.mockk.InternalPlatformDsl$runCoroutine$1.invokeSuspend(InternalPlatformDsl.kt:23)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
        at kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:94)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:70)
        at kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
        at kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
        at io.mockk.InternalPlatformDsl.runCoroutine(InternalPlatformDsl.kt:22)
        at io.mockk.impl.eval.RecordedBlockEvaluator$record$block$2.invoke(RecordedBlockEvaluator.kt:27)
        at io.mockk.impl.eval.RecordedBlockEvaluator$enhanceWithRethrow$1.invoke(RecordedBlockEvaluator.kt:7
6)
        at io.mockk.impl.recording.JvmAutoHinter.autoHint(JvmAutoHinter.kt:23)
        at io.mockk.impl.eval.RecordedBlockEvaluator.record(RecordedBlockEvaluator.kt:39)
        at io.mockk.impl.eval.EveryBlockEvaluator.every(EveryBlockEvaluator.kt:30)
        at io.mockk.MockKDsl.internalCoEvery(API.kt:100)
        at io.mockk.MockKKt.coEvery(MockK.kt:169)
        at org.mospital.dongruan.roc.UndrugPriceServiceTest$testQueryUndrugPriceListRawEmptyResult$1.invokeS
uspend(UndrugPriceServiceTest.kt:210)
        at org.mospital.dongruan.roc.UndrugPriceServiceTest$testQueryUndrugPriceListRawEmptyResult$1.invoke(
UndrugPriceServiceTest.kt)
        at org.mospital.dongruan.roc.UndrugPriceServiceTest$testQueryUndrugPriceListRawEmptyResult$1.invoke(
UndrugPriceServiceTest.kt)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.k
t:317)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
        at kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestC
oroutineScheduler.kt:99)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(Tes
tBuilders.kt:326)
        at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
        at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
        at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
        at kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:94)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:70)
        at kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
        at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
        at kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:309)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:167)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
        at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:15
9)
        at kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
        at org.mospital.dongruan.roc.UndrugPriceServiceTest.testQueryUndrugPriceListRawEmptyResult(UndrugPri
ceServiceTest.kt:202)
        at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:
104)
        at java.base/java.lang.reflect.Method.invoke(Method.java:565)
        at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:775)
        at org.junit.platform.commons.support.ReflectionSupport.invokeMethod(ReflectionSupport.java:479)
        at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(Invoca
tionInterceptorChain.java:131)
        at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:161)
        at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java
:152)
        at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:91)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda
$ofVoidMethod$0(InterceptingExecutableInvoker.java:112)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExec
utableInvoker.java:94)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(Invoc
ationInterceptorChain.java:106)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.
java:64)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationIntercepto
rChain.java:45)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.j
ava:37)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInv
oker.java:93)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInv
oker.java:87)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethod
TestDescriptor.java:216)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescr
iptor.java:212)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.jav
a:137)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.jav
a:69)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:156)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAl
l(SameThreadHierarchicalTestExecutorService.java:41)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:160)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAl
l(SameThreadHierarchicalTestExecutorService.java:41)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestT
ask.java:160)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestT
ask.java:146)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestT
ask.java:144)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java
:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:
143)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(S
ameThreadHierarchicalTestExecutorService.java:35)
        at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestE
xecutor.java:57)
        at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEng
ine.java:54)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:201)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:170)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:94)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrch
estrator.java:59)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecuti
onOrchestrator.java:142)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.
java:58)
        at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
        at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
        at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
        at org.junit.platform.launcher.core.InterceptingLauncher.lambda$execute$1(InterceptingLauncher.java:
39)
        at org.junit.platform.launcher.core.ClasspathAlignmentCheckingLauncherInterceptor.intercept(Classpat
hAlignmentCheckingLauncherInterceptor.java:25)
        at org.junit.platform.launcher.core.InterceptingLauncher.execute(InterceptingLauncher.java:38)
        at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
        at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:
184)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvide
r.java:148)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:1
22)
        at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
        at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
        at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
        at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-30 11:04:47.347 [INFO] UndrugPriceServiceTest - 单元测试 - 空结果测试通过
2025-06-30 11:04:47.365 [INFO] UndrugPriceServiceTest - 单元测试 - queryUndrugPriceListRaw 成功，共获取到 2 
条非药品信息                                                                                                2025-0
6-30 11:04:47.369 [INFO] UndrugPriceServiceTest - 单元测试 - null字段处理测试通过
2025-06-30 11:04:47.376 [INFO] UndrugPriceServiceTest - 单元测试 - 失败场景测试通过：请求失败: 服务器内部错
误 (code: 500)                                                                                              2
025-06-30 11:04:47.380 [INFO] UndrugPriceServiceTest - 单元测试 - 明细项目测试通过
2025-06-30 11:04:47.385 [INFO] UndrugPriceServiceTest - 单元测试 - 拼音码查询测试通过
2025-06-30 11:04:47.389 [INFO] UndrugPriceServiceTest - 单元测试 - 数据类属性测试通过
2025-06-30 11:04:47.391 [INFO] UndrugPriceServiceTest - 单元测试 - 系统类别查询测试通过
2025-06-30 11:04:47.397 [INFO] UndrugPriceServiceTest - 单元测试 - 网络异常测试通过：网络连接超时
2025-06-30 11:04:47.399 [INFO] UndrugPriceServiceTest - 单元测试 - 无效数据处理测试通过
2025-06-30 11:04:47.401 [INFO] UndrugPriceServiceTest - 单元测试 - 项目编码查询测试通过
2025-06-30 11:04:47.404 [INFO] UndrugPriceServiceTest - 单元测试 - 全参数查询成功
2025-06-30 11:04:47.412 [INFO] DrugPriceServiceTest - 单元测试 - 数据类属性测试通过
2025-06-30 11:04:47.416 [INFO] DrugPriceServiceTest - 单元测试 - queryDrugPriceListRaw 成功，共获取到 1 条药
品信息                                                                                                      202
5-06-30 11:04:47.419 [INFO] DrugPriceServiceTest - 单元测试 - 失败场景测试通过：请求失败: 服务器内部错误 
(code: 500)
2025-06-30 11:04:47.421 [INFO] DrugPriceServiceTest - 单元测试 - 无效数据处理测试通过
2025-06-30 11:04:47.424 [INFO] DrugPriceServiceTest - 单元测试 - 空结果测试通过
2025-06-30 11:04:47.429 [INFO] DrugPriceServiceTest - 单元测试 - 网络异常测试通过：网络连接超时
2025-06-30 11:04:47.432 [INFO] DrugPriceServiceTest - 单元测试 - 全参数查询成功
2025-06-30 11:04:47.448 [INFO] DoctorScheduleServiceTest - 单元测试 - 查询医生排班列表Mock测试通过
2025-06-30 11:04:47.450 [INFO] DoctorScheduleServiceTest - 单元测试 - 科室排班测试通过
2025-06-30 11:04:47.450 [INFO] DoctorScheduleServiceTest - 单元测试 - 无可用号源测试通过
2025-06-30 11:04:47.451 [INFO] DoctorScheduleServiceTest - 单元测试 - 下午排班测试通过
2025-06-30 11:04:47.451 [INFO] DoctorScheduleServiceTest - 单元测试 - 数据类属性测试通过
2025-06-30 11:04:47.454 [INFO] DoctorScheduleServiceTest - 单元测试 - 全参数查询测试通过
2025-06-30 11:04:47.459 [INFO] DeptServiceTest - 单元测试 - 数据类属性测试通过
2025-06-30 11:04:47.461 [INFO] DeptServiceTest - 单元测试 - 空结果测试通过
2025-06-30 11:04:47.467 [INFO] DeptServiceTest - 单元测试 - 可空字段处理测试通过
2025-06-30 11:04:47.470 [INFO] DeptServiceTest - 单元测试 - 全参数查询成功
2025-06-30 11:04:47.474 [INFO] DeptServiceTest - 单元测试 - queryAvailableDeptsRaw 成功，共获取到 3 条科室信
息                                                                                                          2
025-06-30 11:04:47.477 [INFO] DeptServiceTest - 单元测试 - 便捷方法测试通过
2025-06-30 11:04:47.480 [INFO] DeptServiceTest - 单元测试 - 失败场景测试通过：请求失败: 服务器内部错误 (code
: 500)
2025-06-30 11:04:47.483 [INFO] DeptServiceTest - 单元测试 - 网络异常测试通过：网络连接超时

```

---

<think><details><summary>Thought Process</summary>看起来所有的测试都通过了！虽然有MockK框架的警告，但这些警告并不影响测试的正确性。现在让我总结一下已经完成的工作。</details></think>

---

## 医生排班列表接口集成测试实现完成 ✅

我已经成功为"查询科室医生排班列表"接口创建了完整的集成测试，参考了现有的集成测试模式。

### 📄 集成测试文件详情

#### `DoctorScheduleServiceIntegrationTest.kt` - 医生排班服务集成测试
这个集成测试文件包含了**10个**完整的测试用例，全面覆盖了不同的查询场景：

### 🧪 测试用例列表

1. **`testQueryDoctorScheduleListByDeptIntegration()`** - 通过科室编码查询
   - 测试科室编码 "9982" 的排班信息
   - 验证返回数据的科室编码匹配
   - 统计排班类型分布（医生排班 vs 科室排班）
   - 分析午别分布情况

2. **`testQueryDoctorScheduleListByDoctorIntegration()`** - 通过医生编码查询
   - 测试医生编码 "002169" 的排班信息
   - 验证返回数据的医生编码匹配
   - 展示医生个人排班详情

3. **`testQueryDoctorScheduleListBySchemaTypeIntegration()`** - 通过排班类型查询
   - 测试排班类型 "1"（医生排班）
   - 验证返回的都是医生排班
   - 统计不同医生的排班数量

4. **`testQueryDoctorScheduleListByBranchIntegration()`** - 通过院区编码查询
   - 测试院区编码 "ROOT"
   - 验证院区编码匹配
   - 统计不同科室的排班分布

5. **`testQueryDoctorScheduleListWithFilterIntegration()`** - 使用过滤器查询
   - 测试过滤当前时间以后的有效排班
   - 统计可用号源情况
   - 验证过滤器功能

6. **`testQueryDoctorScheduleListByLimitTypeIntegration()`** - 通过号源类型查询
   - 测试线上号源类型 "1"
   - 分析线上号源的使用情况
   - 统计总限额和已用号源

7. **`testQueryDoctorScheduleListAllParametersIntegration()`** - 全参数查询
   - 使用所有可用参数进行查询
   - 验证多条件组合查询功能
   - 确保查询条件正确匹配

8. **`testQueryDoctorScheduleListLongRangeIntegration()`** - 长时间范围查询
   - 查询30天的排班信息
   - 分析排班日期分布
   - 统计医生级别分布
   - 计算费用统计（最低、最高、平均）

9. **`testServiceConnectionIntegration()`** - 服务连接测试
   - 基础连接测试，验证服务可达性
   - 使用不存在的科室编码进行测试
   - 确保网络连接正常

10. **`testDataIntegrityIntegration()`** - 数据完整性测试
    - 验证返回数据的字段完整性
    - 检查所有必要字段不为空
    - 测试计算属性的正确性
    - 展示完整的数据结构

### 🔧 集成测试特性

#### 参考现有模式
- **注解配置**：使用 `@EnabledIfSystemProperty` 注解，只有设置 `-Dintegration.tests.enabled=true` 才会运行
- **错误处理**：完善的异常处理和日志记录
- **断言验证**：使用 `assertNotNull` 和 `assertTrue` 进行数据验证
- **日志记录**：详细的测试过程和结果日志

#### 测试数据分析
- **统计分析**：对返回数据进行各种统计分析（排班类型、午别分布、医生级别等）
- **数据验证**：验证查询条件与返回结果的匹配性
- **计算属性**：测试数据类的计算属性功能
- **号源分析**：分析不同类型号源的使用情况

#### 查询场景覆盖
- ✅ 单一条件查询（科室、医生、院区）
- ✅ 组合条件查询（多参数）
- ✅ 过滤条件查询（时间过滤、号源类型）
- ✅ 长时间范围查询
- ✅ 连接性测试
- ✅ 数据完整性验证

### 🏃‍♂️ 运行方式

集成测试需要实际的ROC服务配置才能运行：

```bash
# 运行集成测试（需要设置系统属性）
mvn test -Dintegration.tests.enabled=true -Dtest=DoctorScheduleServiceIntegrationTest

# 运行单个集成测试方法
mvn test -Dintegration.tests.enabled=true -Dtest=DoctorScheduleServiceIntegrationTest#testQueryDoctorScheduleListByDeptIntegration
```

### ✅ 验证结果

- **编译通过**：所有代码编译成功，无语法错误
- **单元测试通过**：现有单元测试全部通过
- **集成测试创建**：按照现有模式创建了完整的集成测试
- **代码规范**：遵循项目的代码规范和命名约定

这个集成测试文件为"查询科室医生排班列表"接口提供了全面的测试覆盖，确保在真实环境中能够正确调用ROC服务并处理各种查询场景。

---

