package org.mospital.dongruan.lis.print

import org.mospital.common.IdUtil
import org.mospital.dongruan.lis.print.client.ArrayOfString
import org.mospital.dongruan.lis.print.client.NeuLisPrintService
import org.mospital.dongruan.lis.print.client.NeuLisPrintServiceSoap
import org.mospital.jackson.DateTimeFormatters
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

object DongruanLisPrintService {

    private val log: Logger = LoggerFactory.getLogger(DongruanLisPrintService::class.java)
    private val client: NeuLisPrintServiceSoap = NeuLisPrintService(Configuration.wsdlUrl).neuLisPrintServiceSoap


    /**
     * @param patientType 门诊，住院
     */
    fun getTestForm(
        patientId: String,
        patientType: String,
        startTime: LocalDateTime,
        endTime: LocalDateTime
    ): List<LisReportForm> {
        val traceId = IdUtil.simpleUUID()

        val formattedStartTime = startTime.format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
        val formattedEndTime = endTime.format(DateTimeFormatters.NORM_DATETIME_FORMATTER)

        log.info("GetTestFormRequest#$traceId: url=${Configuration.wsdlUrl}, patientId=$patientId, patientType=$patientType, startTime=$formattedStartTime, endTime=$formattedEndTime")
        val reports: ArrayOfString = client.getTestForm(
            patientId,
            patientType,
            formattedStartTime,
            formattedEndTime,
            ""
        )
        log.info("GetTestFormResponse#$traceId: reports=${reports.string.joinToString(separator = ",")}")

        return reports.string.map { LisReportForm.fromString(it) }
    }

}
