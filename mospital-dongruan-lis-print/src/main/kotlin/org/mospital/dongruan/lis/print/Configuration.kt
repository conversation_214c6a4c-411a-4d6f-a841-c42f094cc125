package org.mospital.dongruan.lis.print

import org.mospital.jackson.ConfigLoader
import java.net.URI
import java.net.URL

object Configuration {

    private val config: DongruanLisPrintConfig by lazy {
        ConfigLoader.loadConfig("dongruan-lis-print.yaml", DongruanLisPrintConfig::class.java, Configuration::class.java)
    }

    val wsdlUrl: URL get() = URI.create(config.neuLisPrintServiceWsdlUrl).toURL()
}
