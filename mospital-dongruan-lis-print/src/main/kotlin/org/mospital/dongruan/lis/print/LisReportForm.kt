package org.mospital.dongruan.lis.print

/**
 * @param reportStatus 报告状态：5=审核，2=核收，0=接收，9=待查
 */
data class LisReportForm(
    val reportStatus: String,
    val reportTime: String,
    val barcode: String,
    val machineId: String,
    val sampleId: String,
    val reportDate: String,
    val patientName: String,
    val patientId: String,
    val patientSex: String,
    val sampleType: String,
    val itemCodes: String,
    val reportTitle: String
) {
    companion object {
        fun fromString(s: String): LisReportForm {
            val attributes = s.split("|")
            val patientAttributes = attributes[8].split("^")
            return LisReportForm(
                reportStatus = attributes[0],
                reportTime = attributes[1],
                barcode = attributes[2],
                machineId = attributes[3],
                sampleId = attributes[4],
                reportDate = attributes[5],
                patientName = patientAttributes[0],
                patientId = patientAttributes[1],
                patientSex = patientAttributes[2],
                sampleType = attributes[10],
                itemCodes = attributes[11],
                reportTitle = attributes[12]
            )
        }
    }

    fun isAudited(): Boolean {
        return reportStatus == "5"
    }
}
