<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/"
                  xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  targetNamespace="http://tempuri.org/">
    <wsdl:types>
        <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
            <s:element name="HelloWorld">
                <s:complexType/>
            </s:element>
            <s:element name="HelloWorldResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="OpenCon">
                <s:complexType/>
            </s:element>
            <s:element name="OpenConResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="1" maxOccurs="1" name="OpenConResult" type="s:int"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PrintForm">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientType" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtReg" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtEnd" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PrintFormResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="PrintFormResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="ArrayOfString">
                <s:sequence>
                    <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:element name="PrintForm_ByBarcode">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="barcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PrintForm_ByBarcodeResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="PrintForm_ByBarcodeResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PrintFormPDF">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientType" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtReg" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtEnd" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PrintFormPDFResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="PrintFormPDFResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTestForm">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientType" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtReg" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtEnd" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="includeNoAccept" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTestFormResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetTestFormResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="EditPrinted">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="EditPrintedResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="1" maxOccurs="1" name="EditPrintedResult" type="s:int"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetReportPrintstate">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="Reportid" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetReportPrintstateResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetReportPrintstateResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetReportLockDoubt">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="Barcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetReportLockDoubtResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetReportLockDoubtResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetReportCardMessage">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="Barcode" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetReportCardMessageResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetReportCardMessageResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetItemInfoFromUndoReport">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientseq" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetItemInfoFromUndoReportResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetItemInfoFromUndoReportResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ShowMessage">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="testDate" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ShowMessageResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ShowMessageResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetReportListByXml">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xmlContent" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetReportListByXmlResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetReportListByXmlResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PrintGet">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xmlContent" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="PrintGetResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="PrintGetResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="EditPrintState">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="xmlContent" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="EditPrintStateResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="EditPrintStateResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="string" nillable="true" type="s:string"/>
            <s:element name="int" type="s:int"/>
            <s:element name="ArrayOfString" nillable="true" type="tns:ArrayOfString"/>
        </s:schema>
    </wsdl:types>
    <wsdl:message name="HelloWorldSoapIn">
        <wsdl:part name="parameters" element="tns:HelloWorld"/>
    </wsdl:message>
    <wsdl:message name="HelloWorldSoapOut">
        <wsdl:part name="parameters" element="tns:HelloWorldResponse"/>
    </wsdl:message>
    <wsdl:message name="OpenConSoapIn">
        <wsdl:part name="parameters" element="tns:OpenCon"/>
    </wsdl:message>
    <wsdl:message name="OpenConSoapOut">
        <wsdl:part name="parameters" element="tns:OpenConResponse"/>
    </wsdl:message>
    <wsdl:message name="PrintFormSoapIn">
        <wsdl:part name="parameters" element="tns:PrintForm"/>
    </wsdl:message>
    <wsdl:message name="PrintFormSoapOut">
        <wsdl:part name="parameters" element="tns:PrintFormResponse"/>
    </wsdl:message>
    <wsdl:message name="PrintForm_ByBarcodeSoapIn">
        <wsdl:part name="parameters" element="tns:PrintForm_ByBarcode"/>
    </wsdl:message>
    <wsdl:message name="PrintForm_ByBarcodeSoapOut">
        <wsdl:part name="parameters" element="tns:PrintForm_ByBarcodeResponse"/>
    </wsdl:message>
    <wsdl:message name="PrintFormPDFSoapIn">
        <wsdl:part name="parameters" element="tns:PrintFormPDF"/>
    </wsdl:message>
    <wsdl:message name="PrintFormPDFSoapOut">
        <wsdl:part name="parameters" element="tns:PrintFormPDFResponse"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormSoapIn">
        <wsdl:part name="parameters" element="tns:GetTestForm"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormSoapOut">
        <wsdl:part name="parameters" element="tns:GetTestFormResponse"/>
    </wsdl:message>
    <wsdl:message name="EditPrintedSoapIn">
        <wsdl:part name="parameters" element="tns:EditPrinted"/>
    </wsdl:message>
    <wsdl:message name="EditPrintedSoapOut">
        <wsdl:part name="parameters" element="tns:EditPrintedResponse"/>
    </wsdl:message>
    <wsdl:message name="GetReportPrintstateSoapIn">
        <wsdl:part name="parameters" element="tns:GetReportPrintstate"/>
    </wsdl:message>
    <wsdl:message name="GetReportPrintstateSoapOut">
        <wsdl:part name="parameters" element="tns:GetReportPrintstateResponse"/>
    </wsdl:message>
    <wsdl:message name="GetReportLockDoubtSoapIn">
        <wsdl:part name="parameters" element="tns:GetReportLockDoubt"/>
    </wsdl:message>
    <wsdl:message name="GetReportLockDoubtSoapOut">
        <wsdl:part name="parameters" element="tns:GetReportLockDoubtResponse"/>
    </wsdl:message>
    <wsdl:message name="GetReportCardMessageSoapIn">
        <wsdl:part name="parameters" element="tns:GetReportCardMessage"/>
    </wsdl:message>
    <wsdl:message name="GetReportCardMessageSoapOut">
        <wsdl:part name="parameters" element="tns:GetReportCardMessageResponse"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportSoapIn">
        <wsdl:part name="parameters" element="tns:GetItemInfoFromUndoReport"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportSoapOut">
        <wsdl:part name="parameters" element="tns:GetItemInfoFromUndoReportResponse"/>
    </wsdl:message>
    <wsdl:message name="ShowMessageSoapIn">
        <wsdl:part name="parameters" element="tns:ShowMessage"/>
    </wsdl:message>
    <wsdl:message name="ShowMessageSoapOut">
        <wsdl:part name="parameters" element="tns:ShowMessageResponse"/>
    </wsdl:message>
    <wsdl:message name="GetReportListByXmlSoapIn">
        <wsdl:part name="parameters" element="tns:GetReportListByXml"/>
    </wsdl:message>
    <wsdl:message name="GetReportListByXmlSoapOut">
        <wsdl:part name="parameters" element="tns:GetReportListByXmlResponse"/>
    </wsdl:message>
    <wsdl:message name="PrintGetSoapIn">
        <wsdl:part name="parameters" element="tns:PrintGet"/>
    </wsdl:message>
    <wsdl:message name="PrintGetSoapOut">
        <wsdl:part name="parameters" element="tns:PrintGetResponse"/>
    </wsdl:message>
    <wsdl:message name="EditPrintStateSoapIn">
        <wsdl:part name="parameters" element="tns:EditPrintState"/>
    </wsdl:message>
    <wsdl:message name="EditPrintStateSoapOut">
        <wsdl:part name="parameters" element="tns:EditPrintStateResponse"/>
    </wsdl:message>
    <wsdl:message name="HelloWorldHttpGetIn"/>
    <wsdl:message name="HelloWorldHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="OpenConHttpGetIn"/>
    <wsdl:message name="OpenConHttpGetOut">
        <wsdl:part name="Body" element="tns:int"/>
    </wsdl:message>
    <wsdl:message name="PrintFormHttpGetIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="PrintFormHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="PrintForm_ByBarcodeHttpGetIn">
        <wsdl:part name="barcode" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="PrintForm_ByBarcodeHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="PrintFormPDFHttpGetIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="PrintFormPDFHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormHttpGetIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
        <wsdl:part name="includeNoAccept" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="EditPrintedHttpGetIn">
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="EditPrintedHttpGetOut">
        <wsdl:part name="Body" element="tns:int"/>
    </wsdl:message>
    <wsdl:message name="GetReportPrintstateHttpGetIn">
        <wsdl:part name="Reportid" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportPrintstateHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportLockDoubtHttpGetIn">
        <wsdl:part name="Barcode" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportLockDoubtHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportCardMessageHttpGetIn">
        <wsdl:part name="Barcode" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportCardMessageHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportHttpGetIn">
        <wsdl:part name="patientseq" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="ShowMessageHttpGetIn">
        <wsdl:part name="testDate" type="s:string"/>
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ShowMessageHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportListByXmlHttpGetIn">
        <wsdl:part name="xmlContent" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportListByXmlHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="PrintGetHttpGetIn">
        <wsdl:part name="xmlContent" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="PrintGetHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="EditPrintStateHttpGetIn">
        <wsdl:part name="xmlContent" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="EditPrintStateHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="HelloWorldHttpPostIn"/>
    <wsdl:message name="HelloWorldHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="OpenConHttpPostIn"/>
    <wsdl:message name="OpenConHttpPostOut">
        <wsdl:part name="Body" element="tns:int"/>
    </wsdl:message>
    <wsdl:message name="PrintFormHttpPostIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="PrintFormHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="PrintForm_ByBarcodeHttpPostIn">
        <wsdl:part name="barcode" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="PrintForm_ByBarcodeHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="PrintFormPDFHttpPostIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="PrintFormPDFHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormHttpPostIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
        <wsdl:part name="includeNoAccept" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="EditPrintedHttpPostIn">
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="EditPrintedHttpPostOut">
        <wsdl:part name="Body" element="tns:int"/>
    </wsdl:message>
    <wsdl:message name="GetReportPrintstateHttpPostIn">
        <wsdl:part name="Reportid" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportPrintstateHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportLockDoubtHttpPostIn">
        <wsdl:part name="Barcode" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportLockDoubtHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportCardMessageHttpPostIn">
        <wsdl:part name="Barcode" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportCardMessageHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportHttpPostIn">
        <wsdl:part name="patientseq" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="ShowMessageHttpPostIn">
        <wsdl:part name="testDate" type="s:string"/>
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ShowMessageHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportListByXmlHttpPostIn">
        <wsdl:part name="xmlContent" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetReportListByXmlHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="PrintGetHttpPostIn">
        <wsdl:part name="xmlContent" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="PrintGetHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="EditPrintStateHttpPostIn">
        <wsdl:part name="xmlContent" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="EditPrintStateHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:portType name="NeuLisPrintServiceSoap">
        <wsdl:operation name="HelloWorld">
            <wsdl:input message="tns:HelloWorldSoapIn"/>
            <wsdl:output message="tns:HelloWorldSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <wsdl:input message="tns:OpenConSoapIn"/>
            <wsdl:output message="tns:OpenConSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintForm">
            <wsdl:input message="tns:PrintFormSoapIn"/>
            <wsdl:output message="tns:PrintFormSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintForm_ByBarcode">
            <wsdl:input message="tns:PrintForm_ByBarcodeSoapIn"/>
            <wsdl:output message="tns:PrintForm_ByBarcodeSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintFormPDF">
            <wsdl:input message="tns:PrintFormPDFSoapIn"/>
            <wsdl:output message="tns:PrintFormPDFSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获得检验单对象信息，返回接口要求格式的数组内容</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormSoapIn"/>
            <wsdl:output message="tns:GetTestFormSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="EditPrinted">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">更新检验单打印标识</wsdl:documentation>
            <wsdl:input message="tns:EditPrintedSoapIn"/>
            <wsdl:output message="tns:EditPrintedSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportPrintstate">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">检验单打印状态查询</wsdl:documentation>
            <wsdl:input message="tns:GetReportPrintstateSoapIn"/>
            <wsdl:output message="tns:GetReportPrintstateSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportLockDoubt">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询待查原因</wsdl:documentation>
            <wsdl:input message="tns:GetReportLockDoubtSoapIn"/>
            <wsdl:output message="tns:GetReportLockDoubtSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportCardMessage">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询增加报卡项目提示</wsdl:documentation>
            <wsdl:input message="tns:GetReportCardMessageSoapIn"/>
            <wsdl:output message="tns:GetReportCardMessageSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">未出报告返回项目编码等信息</wsdl:documentation>
            <wsdl:input message="tns:GetItemInfoFromUndoReportSoapIn"/>
            <wsdl:output message="tns:GetItemInfoFromUndoReportSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ShowMessage">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">不可打印报告提示</wsdl:documentation>
            <wsdl:input message="tns:ShowMessageSoapIn"/>
            <wsdl:output message="tns:ShowMessageSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportListByXml">
            <wsdl:input message="tns:GetReportListByXmlSoapIn"/>
            <wsdl:output message="tns:GetReportListByXmlSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintGet">
            <wsdl:input message="tns:PrintGetSoapIn"/>
            <wsdl:output message="tns:PrintGetSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="EditPrintState">
            <wsdl:input message="tns:EditPrintStateSoapIn"/>
            <wsdl:output message="tns:EditPrintStateSoapOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:portType name="NeuLisPrintServiceHttpGet">
        <wsdl:operation name="HelloWorld">
            <wsdl:input message="tns:HelloWorldHttpGetIn"/>
            <wsdl:output message="tns:HelloWorldHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <wsdl:input message="tns:OpenConHttpGetIn"/>
            <wsdl:output message="tns:OpenConHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintForm">
            <wsdl:input message="tns:PrintFormHttpGetIn"/>
            <wsdl:output message="tns:PrintFormHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintForm_ByBarcode">
            <wsdl:input message="tns:PrintForm_ByBarcodeHttpGetIn"/>
            <wsdl:output message="tns:PrintForm_ByBarcodeHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintFormPDF">
            <wsdl:input message="tns:PrintFormPDFHttpGetIn"/>
            <wsdl:output message="tns:PrintFormPDFHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获得检验单对象信息，返回接口要求格式的数组内容</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormHttpGetIn"/>
            <wsdl:output message="tns:GetTestFormHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="EditPrinted">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">更新检验单打印标识</wsdl:documentation>
            <wsdl:input message="tns:EditPrintedHttpGetIn"/>
            <wsdl:output message="tns:EditPrintedHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportPrintstate">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">检验单打印状态查询</wsdl:documentation>
            <wsdl:input message="tns:GetReportPrintstateHttpGetIn"/>
            <wsdl:output message="tns:GetReportPrintstateHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportLockDoubt">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询待查原因</wsdl:documentation>
            <wsdl:input message="tns:GetReportLockDoubtHttpGetIn"/>
            <wsdl:output message="tns:GetReportLockDoubtHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportCardMessage">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询增加报卡项目提示</wsdl:documentation>
            <wsdl:input message="tns:GetReportCardMessageHttpGetIn"/>
            <wsdl:output message="tns:GetReportCardMessageHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">未出报告返回项目编码等信息</wsdl:documentation>
            <wsdl:input message="tns:GetItemInfoFromUndoReportHttpGetIn"/>
            <wsdl:output message="tns:GetItemInfoFromUndoReportHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ShowMessage">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">不可打印报告提示</wsdl:documentation>
            <wsdl:input message="tns:ShowMessageHttpGetIn"/>
            <wsdl:output message="tns:ShowMessageHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportListByXml">
            <wsdl:input message="tns:GetReportListByXmlHttpGetIn"/>
            <wsdl:output message="tns:GetReportListByXmlHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintGet">
            <wsdl:input message="tns:PrintGetHttpGetIn"/>
            <wsdl:output message="tns:PrintGetHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="EditPrintState">
            <wsdl:input message="tns:EditPrintStateHttpGetIn"/>
            <wsdl:output message="tns:EditPrintStateHttpGetOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:portType name="NeuLisPrintServiceHttpPost">
        <wsdl:operation name="HelloWorld">
            <wsdl:input message="tns:HelloWorldHttpPostIn"/>
            <wsdl:output message="tns:HelloWorldHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <wsdl:input message="tns:OpenConHttpPostIn"/>
            <wsdl:output message="tns:OpenConHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintForm">
            <wsdl:input message="tns:PrintFormHttpPostIn"/>
            <wsdl:output message="tns:PrintFormHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintForm_ByBarcode">
            <wsdl:input message="tns:PrintForm_ByBarcodeHttpPostIn"/>
            <wsdl:output message="tns:PrintForm_ByBarcodeHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintFormPDF">
            <wsdl:input message="tns:PrintFormPDFHttpPostIn"/>
            <wsdl:output message="tns:PrintFormPDFHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获得检验单对象信息，返回接口要求格式的数组内容</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormHttpPostIn"/>
            <wsdl:output message="tns:GetTestFormHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="EditPrinted">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">更新检验单打印标识</wsdl:documentation>
            <wsdl:input message="tns:EditPrintedHttpPostIn"/>
            <wsdl:output message="tns:EditPrintedHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportPrintstate">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">检验单打印状态查询</wsdl:documentation>
            <wsdl:input message="tns:GetReportPrintstateHttpPostIn"/>
            <wsdl:output message="tns:GetReportPrintstateHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportLockDoubt">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询待查原因</wsdl:documentation>
            <wsdl:input message="tns:GetReportLockDoubtHttpPostIn"/>
            <wsdl:output message="tns:GetReportLockDoubtHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportCardMessage">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询增加报卡项目提示</wsdl:documentation>
            <wsdl:input message="tns:GetReportCardMessageHttpPostIn"/>
            <wsdl:output message="tns:GetReportCardMessageHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">未出报告返回项目编码等信息</wsdl:documentation>
            <wsdl:input message="tns:GetItemInfoFromUndoReportHttpPostIn"/>
            <wsdl:output message="tns:GetItemInfoFromUndoReportHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ShowMessage">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">不可打印报告提示</wsdl:documentation>
            <wsdl:input message="tns:ShowMessageHttpPostIn"/>
            <wsdl:output message="tns:ShowMessageHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetReportListByXml">
            <wsdl:input message="tns:GetReportListByXmlHttpPostIn"/>
            <wsdl:output message="tns:GetReportListByXmlHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="PrintGet">
            <wsdl:input message="tns:PrintGetHttpPostIn"/>
            <wsdl:output message="tns:PrintGetHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="EditPrintState">
            <wsdl:input message="tns:EditPrintStateHttpPostIn"/>
            <wsdl:output message="tns:EditPrintStateHttpPostOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="NeuLisPrintServiceSoap" type="tns:NeuLisPrintServiceSoap">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="HelloWorld">
            <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <soap:operation soapAction="http://tempuri.org/OpenCon" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintForm">
            <soap:operation soapAction="http://tempuri.org/PrintForm" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintForm_ByBarcode">
            <soap:operation soapAction="http://tempuri.org/PrintForm_ByBarcode" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintFormPDF">
            <soap:operation soapAction="http://tempuri.org/PrintFormPDF" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <soap:operation soapAction="http://tempuri.org/GetTestForm" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EditPrinted">
            <soap:operation soapAction="http://tempuri.org/EditPrinted" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportPrintstate">
            <soap:operation soapAction="http://tempuri.org/GetReportPrintstate" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportLockDoubt">
            <soap:operation soapAction="http://tempuri.org/GetReportLockDoubt" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportCardMessage">
            <soap:operation soapAction="http://tempuri.org/GetReportCardMessage" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <soap:operation soapAction="http://tempuri.org/GetItemInfoFromUndoReport" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ShowMessage">
            <soap:operation soapAction="http://tempuri.org/ShowMessage" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportListByXml">
            <soap:operation soapAction="http://tempuri.org/GetReportListByXml" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintGet">
            <soap:operation soapAction="http://tempuri.org/PrintGet" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EditPrintState">
            <soap:operation soapAction="http://tempuri.org/EditPrintState" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="NeuLisPrintServiceSoap12" type="tns:NeuLisPrintServiceSoap">
        <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="HelloWorld">
            <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <soap12:operation soapAction="http://tempuri.org/OpenCon" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintForm">
            <soap12:operation soapAction="http://tempuri.org/PrintForm" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintForm_ByBarcode">
            <soap12:operation soapAction="http://tempuri.org/PrintForm_ByBarcode" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintFormPDF">
            <soap12:operation soapAction="http://tempuri.org/PrintFormPDF" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <soap12:operation soapAction="http://tempuri.org/GetTestForm" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EditPrinted">
            <soap12:operation soapAction="http://tempuri.org/EditPrinted" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportPrintstate">
            <soap12:operation soapAction="http://tempuri.org/GetReportPrintstate" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportLockDoubt">
            <soap12:operation soapAction="http://tempuri.org/GetReportLockDoubt" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportCardMessage">
            <soap12:operation soapAction="http://tempuri.org/GetReportCardMessage" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <soap12:operation soapAction="http://tempuri.org/GetItemInfoFromUndoReport" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ShowMessage">
            <soap12:operation soapAction="http://tempuri.org/ShowMessage" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportListByXml">
            <soap12:operation soapAction="http://tempuri.org/GetReportListByXml" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintGet">
            <soap12:operation soapAction="http://tempuri.org/PrintGet" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EditPrintState">
            <soap12:operation soapAction="http://tempuri.org/EditPrintState" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="NeuLisPrintServiceHttpGet" type="tns:NeuLisPrintServiceHttpGet">
        <http:binding verb="GET"/>
        <wsdl:operation name="HelloWorld">
            <http:operation location="/HelloWorld"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <http:operation location="/OpenCon"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintForm">
            <http:operation location="/PrintForm"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintForm_ByBarcode">
            <http:operation location="/PrintForm_ByBarcode"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintFormPDF">
            <http:operation location="/PrintFormPDF"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <http:operation location="/GetTestForm"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EditPrinted">
            <http:operation location="/EditPrinted"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportPrintstate">
            <http:operation location="/GetReportPrintstate"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportLockDoubt">
            <http:operation location="/GetReportLockDoubt"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportCardMessage">
            <http:operation location="/GetReportCardMessage"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <http:operation location="/GetItemInfoFromUndoReport"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ShowMessage">
            <http:operation location="/ShowMessage"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportListByXml">
            <http:operation location="/GetReportListByXml"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintGet">
            <http:operation location="/PrintGet"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EditPrintState">
            <http:operation location="/EditPrintState"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="NeuLisPrintServiceHttpPost" type="tns:NeuLisPrintServiceHttpPost">
        <http:binding verb="POST"/>
        <wsdl:operation name="HelloWorld">
            <http:operation location="/HelloWorld"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <http:operation location="/OpenCon"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintForm">
            <http:operation location="/PrintForm"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintForm_ByBarcode">
            <http:operation location="/PrintForm_ByBarcode"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintFormPDF">
            <http:operation location="/PrintFormPDF"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <http:operation location="/GetTestForm"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EditPrinted">
            <http:operation location="/EditPrinted"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportPrintstate">
            <http:operation location="/GetReportPrintstate"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportLockDoubt">
            <http:operation location="/GetReportLockDoubt"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportCardMessage">
            <http:operation location="/GetReportCardMessage"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <http:operation location="/GetItemInfoFromUndoReport"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ShowMessage">
            <http:operation location="/ShowMessage"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetReportListByXml">
            <http:operation location="/GetReportListByXml"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="PrintGet">
            <http:operation location="/PrintGet"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="EditPrintState">
            <http:operation location="/EditPrintState"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="NeuLisPrintService">
        <wsdl:port name="NeuLisPrintServiceSoap" binding="tns:NeuLisPrintServiceSoap">
            <soap:address location="http://127.0.0.1:7039/NeuLisPrintService.asmx"/>
        </wsdl:port>
        <wsdl:port name="NeuLisPrintServiceSoap12" binding="tns:NeuLisPrintServiceSoap12">
            <soap12:address location="http://127.0.0.1:7039/NeuLisPrintService.asmx"/>
        </wsdl:port>
        <wsdl:port name="NeuLisPrintServiceHttpGet" binding="tns:NeuLisPrintServiceHttpGet">
            <http:address location="http://127.0.0.1:7039/NeuLisPrintService.asmx"/>
        </wsdl:port>
        <wsdl:port name="NeuLisPrintServiceHttpPost" binding="tns:NeuLisPrintServiceHttpPost">
            <http:address location="http://127.0.0.1:7039/NeuLisPrintService.asmx"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
