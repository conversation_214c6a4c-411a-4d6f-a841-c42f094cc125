package org.mospital.dongruan.lis.print

import org.junit.jupiter.api.Test

import java.time.LocalDateTime

class DongruanLisPrintServiceTest {

    @Test
    fun testGetTestForm() {
        DongruanLisPrintService.getTestForm(
            patientId = "**********",
            patientType = "门诊",
            startTime = LocalDateTime.now().minusDays(300),
            endTime = LocalDateTime.now()
        ).forEach { println(it) }
    }

}
