# 日志配置指南

## 概述

`mospital-common` 模块使用 SLF4J 作为日志门面，Log4j2 相关依赖已标记为可选（`optional`）。这意味着：

- ✅ 您可以自由选择任何支持 SLF4J 的日志实现
- ✅ 避免了强制的日志框架绑定
- ✅ 减少了依赖冲突的可能性

## 支持的日志实现

### 1. 使用 Log4j2（推荐）

如果您希望使用 Log4j2，请在您的项目中添加以下依赖：

```xml
<dependencies>
    <!-- Log4j2 实现 -->
    <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
    </dependency>
    
    <!-- SLF4J 到 Log4j2 的桥接器 -->
    <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j2-impl</artifactId>
    </dependency>
</dependencies>
```

配置文件 `log4j2.xml`：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="[%-5p] %d{yyyy-MM-dd HH:mm:ss,SSS} --> [%t] %l: %m %n"/>
        </Console>
    </Appenders>
    <Loggers>
        <Root level="INFO">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>
```

### 2. 使用 Logback

如果您偏好 Logback，请添加以下依赖：

```xml
<dependencies>
    <!-- Logback 实现 -->
    <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
    </dependency>
</dependencies>
```

配置文件 `logback.xml`：
```xml
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%-5level] %d{yyyy-MM-dd HH:mm:ss,SSS} --> [%thread] %logger{36}: %msg %n</pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
```

### 3. 使用 Java Util Logging (JUL)

如果您使用 JUL，请添加 SLF4J 到 JUL 的桥接器：

```xml
<dependencies>
    <!-- SLF4J 到 JUL 的桥接器 -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk14</artifactId>
    </dependency>
</dependencies>
```

### 4. 不使用任何日志实现

如果您不需要日志输出，可以使用 SLF4J 的空实现：

```xml
<dependencies>
    <!-- SLF4J 空实现 -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-nop</artifactId>
    </dependency>
</dependencies>
```

## 代码中的使用

在您的代码中，统一使用 SLF4J API：

```kotlin
import org.slf4j.LoggerFactory

class YourClass {
    companion object {
        private val logger = LoggerFactory.getLogger(YourClass::class.java)
    }
    
    fun someMethod() {
        logger.info("这是一条信息日志")
        logger.debug("这是一条调试日志")
        logger.error("这是一条错误日志", exception)
    }
}
```

## 注意事项

1. **依赖冲突**：确保项目中只有一个 SLF4J 绑定实现
2. **版本兼容**：使用项目中定义的 BOM 管理版本，避免版本冲突
3. **性能考虑**：Log4j2 通常提供更好的性能，Logback 提供更好的可配置性
4. **测试环境**：测试时可以使用 `slf4j-simple` 或 `slf4j-nop` 简化配置

## 迁移指南

### 从强制 Log4j2 迁移

如果您之前依赖 `mospital-common` 的 Log4j2 配置，现在需要在您的项目中显式添加 Log4j2 依赖：

```xml
<!-- 在您的项目 pom.xml 中添加 -->
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-core</artifactId>
</dependency>
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-slf4j2-impl</artifactId>
</dependency>
```

### 切换到其他日志框架

1. 移除项目中的 Log4j2 相关依赖
2. 添加目标日志框架的依赖
3. 更新配置文件
4. 测试确保日志正常输出 