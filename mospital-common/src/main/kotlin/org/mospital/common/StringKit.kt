package org.mospital.common

import org.dromara.hutool.core.lang.Validator

/**
 * 实名信息脱敏，参见 https://opendocs.alipay.com/open/200/security#%E2%80%8B%0A%E6%95%8F%E6%84%9F%E4%BF%A1%E6%81%AF%E7%94%A8%E4%BA%8E%E5%B1%95%E7%A4%BA%E7%9A%84%E5%9C%BA%E6%99%AF
 */
object StringKit {

    /**
     * 脱敏身份证号
     */
    fun hideIdCardNo(idCardNo: String): String {
        return if (idCardNo.length > 2) {
            idCardNo.replaceRange(1, idCardNo.length - 1, "*".repeat(idCardNo.length - 2))
        } else {
            idCardNo
        }
    }

    /**
     * 脱敏姓名
     */
    fun hideName(name: String): String {
        return when (name.length) {
            1 -> "*"
            else -> name.replaceRange(0, name.length - 1, "*".repeat(name.length - 1))
        }
    }

    /**
     * 脱敏手机号
     */
    fun hideMobile(mobile: String): String {
        return if (Validator.isMobile(mobile)) mobile.replaceRange(3, 9, "*".repeat(6)) else mobile
    }

    /**
     * 移除非中文字符
     * 支持以下Unicode区域的中文字符：
     * - 基本汉字：\u4e00-\u9fa5
     * - 扩展A区：\u3400-\u4dbf
     * - 扩展B区：\u20000-\u2a6df
     * - 扩展C区：\u2a700-\u2b73f
     * - 扩展D区：\u2b740-\u2b81f
     * - 扩展E区：\u2b820-\u2ceaf
     * - 扩展F区：\u2ceb0-\u2ebef
     * - 扩展G区：\u30000-\u3134f
     */
    fun removeNonChinese(text: String?): String {
        if (text.isNullOrEmpty()) {
            return ""
        }

        return text.filter { char ->
            Character.UnicodeScript.of(char.code) == Character.UnicodeScript.HAN
        }
    }

    /**
     * 移除非基本汉字
     * 只保留基本汉字区（\u4e00-\u9fa5）的字符
     * 
     * 示例:
     * ```
     * removeNonBasicChinese("你好123") // 返回 "你好"
     * removeNonBasicChinese("测试㐀") // 返回 "测试"，㐀为扩展区汉字将被移除
     * ```
     */
    fun removeNonBasicChinese(text: String?): String {
        if (text.isNullOrEmpty()) {
            return ""
        }
        
        return text.filter { char -> 
            char.code in 0x4E00..0x9FA5
        }
    }

    /**
     * 忽略非中文字符的比较
     * 
     * @param text1 第一个待比较文本
     * @param text2 第二个待比较文本
     * @return 去除非中文字符后的比较结果

     * 示例:
     * ```
     * equalsIgnoreNonChinese("测试123", "测试abc") // 返回 true
     * equalsIgnoreNonChinese("你好", null) // 返回 false
     * equalsIgnoreNonChinese("", "") // 返回 true
     * ```
     */
    fun equalsIgnoreNonChinese(text1: String?, text2: String?): Boolean {
        // 快速路径：引用相等
        if (text1 === text2) {
            return true
        }
        
        // 如果其中一个为null，只有两个都为null时才相等
        if (text1 == null || text2 == null) {
            return text1 == text2
        }

        // 如果两个字符串都为空，直接返回true
        if (text1.isEmpty() && text2.isEmpty()) {
            return true
        }

        return removeNonChinese(text1) == removeNonChinese(text2)
    }

    /**
     * 仅比较基本汉字（\u4e00-\u9fa5）
     * 
     * @param text1 第一个待比较文本
     * @param text2 第二个待比较文本
     * @return 仅考虑基本汉字的比较结果
     * 
     * 示例:
     * ```
     * equalsBasicChineseOnly("测试123", "测试abc") // 返回 true
     * equalsBasicChineseOnly("测试㐀", "测试") // 返回 true，㐀为扩展区汉字将被忽略
     * equalsBasicChineseOnly("你好", null) // 返回 false
     * equalsBasicChineseOnly("", "") // 返回 true
     * ```
     */
    fun equalsBasicChineseOnly(text1: String?, text2: String?): Boolean {
        // 快速路径：引用相等
        if (text1 === text2) {
            return true
        }
        
        // null 值处理
        if (text1 == null || text2 == null) {
            return text1 == text2
        }
        
        // 空字符串快速处理
        if (text1.isEmpty() && text2.isEmpty()) {
            return true
        }
        
        return removeNonBasicChinese(text1) == removeNonBasicChinese(text2)
    }
    
    /**
     * 验证微信小程序订阅消息中的thing.DATA参数
     * 
     * 校验规则，参见：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
     * 1. 20个以内字符
     * 2. 可汉字、数字、字母或符号组合
     * 
     * @param text 待验证的字符串
     * @return 是否符合规则
     * 
     * 示例:
     * ```
     * validateThing("测试内容") // 返回 true
     * validateThing("这个字符串超过了二十个字符所以会验证失败") // 返回 false
     * validateThing("") // 返回 false
     * validateThing(null) // 返回 false
     * validateThing("包含\n换行") // 返回 false
     * ```
     */
    fun validateThing(text: String?): Boolean {
        if (text.isNullOrBlank()) {
            return false
        }
        
        // 验证长度不超过20个字符
        if (text.length > 20) {
            return false
        }
        
        // 验证不包含控制字符（如换行符等）
        val hasControlChar = text.any { char -> 
            char.isISOControl()
        }
        
        return !hasControlChar
    }
    
    /**
     * 清理微信小程序订阅消息中的thing.DATA参数，使其符合规范
     * 
     * 处理规则，参见：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
     * 1. 去除控制字符（包括换行符等）
     * 2. 截断至20个字符以内
     * 
     * @param text 待清理的字符串
     * @return 清理后的字符串，如果输入为null或空则返回空字符串
     * 
     * 示例:
     * ```
     * sanitizeThing("测试内容") // 返回 "测试内容"
     * sanitizeThing("这个字符串超过了二十个字符所以会被截断") // 返回 "这个字符串超过了二十个字符"
     * sanitizeThing("包含\n换行符") // 返回 "包含换行符"
     * sanitizeThing(null) // 返回 ""
     * ```
     */
    fun sanitizeThing(text: String?): String {
        if (text.isNullOrBlank()) {
            return ""
        }
        
        // 去除控制字符
        val noControlChars = text.filter { char -> 
            !char.isISOControl()
        }
        
        // 截断至20个字符
        return noControlChars.take(20)
    }

    /**
     * 验证微信小程序订阅消息中的name.DATA参数
     * 
     * 校验规则，参见：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
     * 1. 中文名10个汉字内
     * 2. 纯英文名20个字母内
     * 3. 中文和字母混合按中文名算，10个字内
     * 
     * @param text 待验证的字符串
     * @return 是否符合规则
     * 
     * 示例:
     * ```
     * validateName("张三") // 返回 true
     * validateName("张三李四王五赵六孙七") // 返回 false，超过10个汉字
     * validateName("John Smith") // 返回 true
     * validateName("abcdefghijklmnopqrst") // 返回 true，恰好20个字母
     * validateName("abcdefghijklmnopqrstu") // 返回 false，超过20个字母
     * validateName("张三Smith") // 返回 true，按中文名计算在10个字内
     * validateName("张三李四王五John Smith") // 返回 false，按中文名计算超过10个字
     * validateName("") // 返回 false
     * validateName(null) // 返回 false
     * validateName("张三\n李四") // 返回 false，包含控制字符
     * ```
     */
    fun validateName(text: String?): Boolean {
        if (text.isNullOrBlank()) {
            return false
        }
        
        // 验证不包含控制字符
        if (text.any { char -> char.isISOControl() }) {
            return false
        }
        
        // 判断是否包含中文字符
        val containsChinese = text.any { char -> 
            Character.UnicodeScript.of(char.code) == Character.UnicodeScript.HAN
        }
        
        // 判断是否为纯英文字母（包括空格）
        val isEnglishOnly = text.all { char -> 
            char.isLetter() && char.code < 128 || char.isWhitespace()
        }
        
        return when {
            // 中文名或中英混合名，限制10个字符
            containsChinese -> text.length <= 10
            // 纯英文名，限制20个字符
            isEnglishOnly -> text.length <= 20
            // 其他情况（如包含数字或特殊符号），按中文名处理
            else -> text.length <= 10
        }
    }
    
    /**
     * 清理微信小程序订阅消息中的name.DATA参数，使其符合规范
     * 
     * 处理规则，参见：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
     * 1. 去除控制字符（包括换行符等）
     * 2. 根据内容类型截断：
     *    - 中文名或含中文：截断至10个字符以内
     *    - 纯英文名：截断至20个字符以内
     *    - 其他情况：按中文名处理，截断至10个字符
     * 
     * @param text 待清理的字符串
     * @return 清理后的字符串，如果输入为null或空则返回空字符串
     * 
     * 示例:
     * ```
     * sanitizeName("张三") // 返回 "张三"
     * sanitizeName("张三李四王五赵六孙七周八") // 返回 "张三李四王五赵六孙七周"，超过10个汉字被截断
     * sanitizeName("John Smith") // 返回 "John Smith"
     * sanitizeName("abcdefghijklmnopqrstuvwxyz") // 返回 "abcdefghijklmnopqrst"，超过20个字母被截断
     * sanitizeName("张三Smith") // 返回 "张三Smith"
     * sanitizeName("张三李四王五李四王五Smith") // 返回 "张三李四王五李四王"，按中文名被截断
     * sanitizeName("包含\n换行符") // 返回 "包含换行符"
     * sanitizeName(null) // 返回 ""
     * ```
     */
    fun sanitizeName(text: String?): String {
        if (text.isNullOrBlank()) {
            return ""
        }
        
        // 去除控制字符
        val noControlChars = text.filter { char -> 
            !char.isISOControl()
        }
        
        // 判断是否包含中文字符
        val containsChinese = noControlChars.any { char -> 
            Character.UnicodeScript.of(char.code) == Character.UnicodeScript.HAN
        }
        
        // 判断是否为纯英文字母（包括空格）
        val isEnglishOnly = noControlChars.all { char -> 
            char.isLetter() && char.code < 128 || char.isWhitespace()
        }
        
        // 根据内容类型截断字符串
        return when {
            // 中文名或中英混合名，限制10个字符
            containsChinese -> noControlChars.take(10)
            // 纯英文名，限制20个字符
            isEnglishOnly -> noControlChars.take(20)
            // 其他情况（如包含数字或特殊符号），按中文名处理
            else -> noControlChars.take(10)
        }
    }

    /**
     * 验证微信小程序订阅消息中的phrase.DATA参数
     * 
     * 校验规则，参见：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
     * 1. 5个以内纯汉字
     * 
     * @param text 待验证的字符串
     * @return 是否符合规则
     * 
     * 示例:
     * ```
     * validatePhrase("已完成") // 返回 true
     * validatePhrase("审核通过") // 返回 true
     * validatePhrase("你好123") // 返回 false，包含非汉字字符
     * validatePhrase("这个短语太长了") // 返回 false，超过5个汉字
     * validatePhrase("") // 返回 false
     * validatePhrase(null) // 返回 false
     * validatePhrase("你\n好") // 返回 false，包含控制字符
     * ```
     */
    fun validatePhrase(text: String?): Boolean {
        if (text.isNullOrBlank()) {
            return false
        }
        
        // 验证不包含控制字符
        if (text.any { char -> char.isISOControl() }) {
            return false
        }
        
        // 验证只包含汉字
        val isChineseOnly = text.all { char -> 
            Character.UnicodeScript.of(char.code) == Character.UnicodeScript.HAN
        }
        
        // 验证长度不超过5个字符
        return isChineseOnly && text.length <= 5
    }
    
    /**
     * 清理微信小程序订阅消息中的phrase.DATA参数，使其符合规范
     * 
     * 处理规则，参见：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
     * 1. 去除控制字符
     * 2. 只保留汉字
     * 3. 截断至5个汉字以内
     * 
     * @param text 待清理的字符串
     * @return 清理后的字符串，如果输入为null或空或不包含汉字则返回空字符串
     * 
     * 示例:
     * ```
     * sanitizePhrase("已完成") // 返回 "已完成"
     * sanitizePhrase("审核通过了") // 返回 "审核通过了"
     * sanitizePhrase("这个短语太长了") // 返回 "这个短语太"，超过5个汉字被截断
     * sanitizePhrase("你好123") // 返回 "你好"，非汉字被移除
     * sanitizePhrase("你\n好") // 返回 "你好"，控制字符被移除
     * sanitizePhrase("hello") // 返回 ""，不包含汉字
     * sanitizePhrase(null) // 返回 ""
     * ```
     */
    fun sanitizePhrase(text: String?): String {
        if (text.isNullOrBlank()) {
            return ""
        }
        
        // 去除控制字符并只保留汉字
        val chineseOnly = text.filter { char -> 
            !char.isISOControl() && Character.UnicodeScript.of(char.code) == Character.UnicodeScript.HAN
        }
        
        if (chineseOnly.isEmpty()) {
            return ""
        }
        
        // 截断至5个汉字
        return chineseOnly.take(5)
    }

    /**
     * 验证微信小程序订阅消息中的character_string.DATA参数
     * 
     * 校验规则，参见：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
     * 1. 32位以内数字、字母或符号
     * 
     * @param text 待验证的字符串
     * @return 是否符合规则
     * 
     * 示例:
     * ```
     * validateCharacterString("ABC123") // 返回 true
     * validateCharacterString("12345-abcde-FGHIJ-67890") // 返回 true
     * validateCharacterString("") // 返回 false
     * validateCharacterString(null) // 返回 false
     * validateCharacterString("这个字符串超过了三十二位数字、字母或符号的限制，所以会验证失败") // 返回 false，包含汉字
     * validateCharacterString("ABC\n123") // 返回 false，包含控制字符
     * ```
     */
    fun validateCharacterString(text: String?): Boolean {
        if (text.isNullOrBlank()) {
            return false
        }
        
        // 验证不包含控制字符
        if (text.any { char -> char.isISOControl() }) {
            return false
        }
        
        // 验证只包含数字、字母或符号（ASCII范围内的可打印字符）
        val containsNonAscii = text.any { char -> char.code > 127 }
        if (containsNonAscii) {
            return false
        }
        
        // 验证长度不超过32位
        return text.length <= 32
    }
    
    /**
     * 清理微信小程序订阅消息中的character_string.DATA参数，使其符合规范
     * 
     * 处理规则，参见：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/subscribe-message/sendMessage.html
     * 1. 去除控制字符
     * 2. 只保留数字、字母或符号（ASCII范围内的可打印字符）
     * 3. 截断至32位以内
     * 
     * @param text 待清理的字符串
     * @return 清理后的字符串，如果输入为null或空或不包含有效字符则返回空字符串
     * 
     * 示例:
     * ```
     * sanitizeCharacterString("ABC123") // 返回 "ABC123"
     * sanitizeCharacterString("12345-abcde-FGHIJ-67890") // 返回 "12345-abcde-FGHIJ-67890"
     * sanitizeCharacterString("abcdefghijklmnopqrstuvwxyz0123456789") // 返回 "abcdefghijklmnopqrstuvwxyz012345"，超过32位被截断
     * sanitizeCharacterString("ABC\n123") // 返回 "ABC123"，控制字符被移除
     * sanitizeCharacterString("你好ABC123") // 返回 "ABC123"，汉字被移除
     * sanitizeCharacterString("你好") // 返回 ""，不包含有效字符
     * sanitizeCharacterString(null) // 返回 ""
     * ```
     */
    fun sanitizeCharacterString(text: String?): String {
        if (text.isNullOrBlank()) {
            return ""
        }
        
        // 去除控制字符并只保留ASCII范围内的可打印字符（数字、字母或符号）
        val asciiOnly = text.filter { char -> 
            !char.isISOControl() && char.code <= 127
        }
        
        if (asciiOnly.isEmpty()) {
            return ""
        }
        
        // 截断至32位
        return asciiOnly.take(32)
    }

}

fun CharSequence.toOneLineString() = replace(Regex("[\\r\\n]"), "")
