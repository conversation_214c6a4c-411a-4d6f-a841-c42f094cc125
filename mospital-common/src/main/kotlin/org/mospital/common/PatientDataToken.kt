package org.mospital.common

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef

data class PatientDataToken(
    val dataId: String,
    val patientId: String
) {
    companion object {
        @JvmStatic
        fun parse(token: String): PatientDataToken? = TokenManager.parseToken(token, jacksonTypeRef<PatientDataToken>())
    }

    fun generateToken(): String = TokenManager.generateToken(this)

}