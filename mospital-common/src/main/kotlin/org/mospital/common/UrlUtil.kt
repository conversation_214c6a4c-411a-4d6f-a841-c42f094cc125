package org.mospital.common

import java.io.File
import java.net.URI
import java.net.URL

object UrlUtil {
    /**
     * 将字符串路径解析为URL
     * 支持协议: https://, http://, file://, classpath://
     *
     * @param path 需要解析的路径字符串
     * @return 解析后的URL对象
     * @throws IllegalArgumentException 当路径格式不正确或不支持的协议时抛出
     */
    fun parseUrl(path: String): URL {
        return when {
            path.startsWith("http://") || path.startsWith("https://") -> {
                URI.create(path).toURL()
            }

            path.startsWith("file://") -> {
                File(path.substring(7)).toURI().toURL()
            }

            path.startsWith("classpath:") -> {
                val resourcePath = path.removePrefix("classpath://").removePrefix("classpath:").removePrefix("/")
                UrlUtil::class.java.classLoader.getResource(resourcePath)
                    ?: throw IllegalArgumentException("Classpath resource not found: $resourcePath")
            }

            else -> {
                throw IllegalArgumentException("Unsupported protocol or invalid URL format: $path")
            }
        }
    }
}
