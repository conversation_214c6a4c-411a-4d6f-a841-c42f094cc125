package org.mospital.common.http

import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.slf4j.Logger
import java.util.concurrent.TimeUnit

/**
 * HTTP客户端工厂类
 * 提供便捷方法创建预配置的OkHttpClient实例
 */
object HttpClientFactory {

    /**
     * 创建一个带请求ID日志的OkHttpClient
     *
     * @param logger 日志记录器
     * @param connectTimeout 连接超时时间（毫秒）
     * @param readTimeout 读取超时时间（毫秒）
     * @param writeTimeout 写入超时时间（毫秒）
     * @param maxRequests 最大并发请求数
     * @param maxRequestsPerHost 每个主机的最大请求数
     * @param useUuidRequestId 是否使用UUID作为请求ID，false则使用自增ID
     * @param configurator 额外的配置函数，用于在构建前对Builder进行额外配置
     * @return 预配置的OkHttpClient实例
     */
    @JvmStatic
    @JvmOverloads
    fun createWithRequestIdLogging(
        logger: Logger,
        connectTimeout: Long = 500,
        readTimeout: Long = 30_000,
        writeTimeout: Long = 30_000,
        maxRequests: Int = Runtime.getRuntime().availableProcessors() * 32,
        maxRequestsPerHost: Int = maxRequests,
        useUuidRequestId: Boolean = false,
        configurator: ((OkHttpClient.Builder) -> Unit)? = null
    ): OkHttpClient {
        // 创建带请求ID的日志拦截器
        val loggingInterceptor = if (useUuidRequestId) {
            RequestIdLoggingInterceptor.createWithUuid(logger)
        } else {
            RequestIdLoggingInterceptor.create(logger)
        }

        // 构建OkHttpClient
        val builder = OkHttpClient.Builder()
            .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
            .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
            .writeTimeout(writeTimeout, TimeUnit.MILLISECONDS)
            .dispatcher(okhttp3.Dispatcher().apply {
                this.maxRequests = maxRequests
                this.maxRequestsPerHost = maxRequestsPerHost
            })

        // 先应用额外的配置（如添加其他拦截器）
        configurator?.invoke(builder)

        // 最后添加日志拦截器，确保它能记录到完整的请求信息
        builder.addInterceptor(loggingInterceptor)

        return builder.build()
    }

    /**
     * 创建一个标准的OkHttpClient，使用原始HttpLoggingInterceptor
     *
     * @param logger 日志记录器
     * @param logLevel 日志级别
     * @param connectTimeout 连接超时时间（毫秒）
     * @param readTimeout 读取超时时间（毫秒）
     * @param writeTimeout 写入超时时间（毫秒）
     * @param maxRequests 最大并发请求数
     * @param maxRequestsPerHost 每个主机的最大请求数
     * @param configurator 额外的配置函数，用于在构建前对Builder进行额外配置
     * @return 预配置的OkHttpClient实例
     */
    @JvmStatic
    @JvmOverloads
    fun createStandard(
        logger: Logger,
        logLevel: HttpLoggingInterceptor.Level = HttpLoggingInterceptor.Level.BODY,
        connectTimeout: Long = 500,
        readTimeout: Long = 30_000,
        writeTimeout: Long = 30_000,
        maxRequests: Int = Runtime.getRuntime().availableProcessors() * 32,
        maxRequestsPerHost: Int = maxRequests,
        configurator: ((OkHttpClient.Builder) -> Unit)? = null
    ): OkHttpClient {
        // 创建标准日志拦截器
        val loggingInterceptor = HttpLoggingInterceptor {
            logger.debug(it)
        }.apply {
            this.level = logLevel
        }

        // 构建OkHttpClient
        val builder = OkHttpClient.Builder()
            .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
            .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
            .writeTimeout(writeTimeout, TimeUnit.MILLISECONDS)
            .dispatcher(okhttp3.Dispatcher().apply {
                this.maxRequests = maxRequests
                this.maxRequestsPerHost = maxRequestsPerHost
            })

        // 先应用额外的配置（如添加其他拦截器）
        configurator?.invoke(builder)

        // 最后添加日志拦截器，确保它能记录到完整的请求信息
        builder.addInterceptor(loggingInterceptor)

        return builder.build()
    }
} 