# HTTP工具包

本包提供HTTP请求相关的工具类，包括带请求ID的日志拦截器和HTTP客户端工厂。

## 主要功能

1. **RequestIdLoggingInterceptor**：为每个HTTP请求生成唯一ID，便于在日志中跟踪整个请求的生命周期
2. **HttpClientFactory**：提供便捷方法创建预配置的OkHttpClient实例，支持自定义配置

## 使用示例

### 基础使用 - 带请求ID的HTTP客户端

```kotlin
import org.mospital.common.http.HttpClientFactory
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory

// 创建日志记录器
val logger = LoggerFactory.getLogger(YourClass::class.java)

// 创建带请求ID的HTTP客户端
val httpClient = HttpClientFactory.createWithRequestIdLogging(
    logger = logger,
    connectTimeout = 500,  // 毫秒
    readTimeout = 30_000,  // 毫秒
    useUuidRequestId = true  // 使用UUID作为请求ID，false则使用自增ID
)

// 用于Retrofit
val retrofit = Retrofit.Builder()
    .baseUrl("https://api.example.com/")
    .client(httpClient)
    .addConverterFactory(JacksonConverterFactory.create())
    .build()
```

### 高级使用 - 使用 configurator 添加自定义拦截器

```kotlin
import okhttp3.Interceptor
import okhttp3.Response
import org.mospital.common.http.HttpClientFactory
import org.slf4j.LoggerFactory

// 创建日志记录器
val logger = LoggerFactory.getLogger(YourClass::class.java)

// 自定义头部拦截器
class HeaderInterceptor(
    private val domain: String,
    private val key: String
) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val requestWithHeaders = originalRequest.newBuilder()
            .header("domain", domain)
            .header("key", key)
            .build()
        return chain.proceed(requestWithHeaders)
    }
}

// 使用 configurator 添加自定义拦截器
val httpClient = HttpClientFactory.createWithRequestIdLogging(
    logger = logger,
    connectTimeout = 500,
    readTimeout = 30_000,
    useUuidRequestId = true
) { builder ->
    // 添加自定义拦截器
    builder.addInterceptor(HeaderInterceptor("example.com", "api-key-123"))
    
    // 可以添加多个拦截器
    builder.addInterceptor { chain ->
        val request = chain.request().newBuilder()
            .header("User-Agent", "MyApp/1.0")
            .build()
        chain.proceed(request)
    }
    
    // 其他配置
    builder.retryOnConnectionFailure(true)
}
```

### 直接使用RequestIdLoggingInterceptor

```kotlin
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.mospital.common.http.RequestIdLoggingInterceptor
import org.slf4j.LoggerFactory
import java.util.concurrent.TimeUnit

// 创建日志记录器
val logger = LoggerFactory.getLogger(YourClass::class.java)

// 创建带请求ID的日志拦截器
val loggingInterceptor = RequestIdLoggingInterceptor.create(
    logger = logger,
    level = HttpLoggingInterceptor.Level.BODY
)

// 创建OkHttpClient
val httpClient = OkHttpClient.Builder()
    .connectTimeout(500, TimeUnit.MILLISECONDS)
    .readTimeout(30, TimeUnit.SECONDS)
    .addInterceptor(loggingInterceptor)
    .build()
```

## 重要说明

### 拦截器顺序

**重要**：`HttpClientFactory` 会按照正确的顺序添加拦截器：

1. **先应用 `configurator` 中的配置**（包括业务拦截器）
2. **最后添加日志拦截器**

这样确保日志拦截器能够记录到经过所有其他拦截器处理后的**完整请求信息**，包括：
- 所有添加的头部信息
- 修改后的请求体
- 完整的URL参数

```kotlin
// ✅ 正确：日志会包含 domain 和 key 头部
val httpClient = HttpClientFactory.createWithRequestIdLogging(logger) { builder ->
    builder.addInterceptor(HeaderInterceptor("domain", "key"))
}

// ❌ 错误：如果手动构建，要注意顺序
val wrongClient = OkHttpClient.Builder()
    .addInterceptor(loggingInterceptor)  // 先添加日志拦截器
    .addInterceptor(headerInterceptor)   // 后添加业务拦截器
    .build()  // 日志中将看不到头部信息
```

### 参数说明

#### createWithRequestIdLogging 参数

- `logger`: 日志记录器
- `connectTimeout`: 连接超时时间（毫秒，默认500）
- `readTimeout`: 读取超时时间（毫秒，默认30000）
- `writeTimeout`: 写入超时时间（毫秒，默认30000）
- `maxRequests`: 最大并发请求数（默认：CPU核心数 × 32）
- `maxRequestsPerHost`: 每个主机最大请求数（默认同 maxRequests）
- `useUuidRequestId`: 是否使用UUID作为请求ID（默认false，使用自增ID）
- `configurator`: 可选的配置函数，用于添加自定义拦截器或其他配置

#### createStandard 参数

- `logger`: 日志记录器
- `logLevel`: 日志级别（默认 BODY）
- 其他参数同 `createWithRequestIdLogging`

## 日志输出示例

使用请求ID拦截器后，日志输出将包含唯一请求ID，便于追踪同一请求的不同日志：

```
[REQ_1646123456789abcd] 开始 POST http://api.example.com/users
[REQ_1646123456789abcd] --> POST http://api.example.com/users
[REQ_1646123456789abcd] Content-Type: application/json; charset=UTF-8
[REQ_1646123456789abcd] domain: example.com
[REQ_1646123456789abcd] key: api-key-123
[REQ_1646123456789abcd] User-Agent: MyApp/1.0
[REQ_1646123456789abcd] Content-Length: 53
[REQ_1646123456789abcd] {"name":"张三","age":30,"email":"<EMAIL>"}
[REQ_1646123456789abcd] --> END POST (53-byte body)
[REQ_1646123456789abcd] <-- 200 OK (150ms)
[REQ_1646123456789abcd] Content-Type: application/json
[REQ_1646123456789abcd] Content-Length: 86
[REQ_1646123456789abcd] {"id":1001,"name":"张三","age":30,"email":"<EMAIL>","createdAt":"2025-03-07T12:30:45Z"}
[REQ_1646123456789abcd] <-- END HTTP (86-byte body)
[REQ_1646123456789abcd] 完成 200 (180ms)
```

## 优势

1. **完整的日志追踪**：轻松识别同一请求的所有相关日志
2. **灵活的配置**：通过 `configurator` 参数轻松添加自定义拦截器
3. **正确的拦截器顺序**：确保日志记录完整的请求信息
4. **并发友好**：在高并发环境中便于追踪和分析请求流程
5. **问题排查**：有助于快速定位和分析问题
6. **性能监控**：提供请求耗时统计
7. **向下兼容**：新的 `configurator` 参数是可选的，不影响现有代码 