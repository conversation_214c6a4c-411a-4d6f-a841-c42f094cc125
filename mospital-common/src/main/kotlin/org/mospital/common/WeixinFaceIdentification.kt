package org.mospital.common

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.core.JsonProcessingException
import okhttp3.HttpUrl.Companion.toHttpUrl
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.apache.commons.codec.digest.DigestUtils
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.slf4j.LoggerFactory
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.net.ssl.SSLException

/**
 * 微信人脸核身的认证结果，参见：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
 * @param errCode 错误码，0表示成功
 * @param errMsg 错误信息
 * @param identifyRet 认证结果
 * @param identifyTime 认证时间
 * @param validateData 用户读的数字（如是读数字）
 * @param openid 用户openid
 * @param userIdKey 用于后台交户表示用户姓名、身份证的凭证
 * @param finishTime 认证结束时间
 * @param idCardNoMD5 身份证号的md5（最后一位X为大写）
 * @param nameMD5 姓名MD5
 */
data class WeixinFaceIdentifyResult(
    @JsonProperty("errcode")
    val errCode: Int = -1,
    @JsonProperty("errmsg")
    val errMsg: String = "",
    @JsonProperty("identify_ret")
    val identifyRet: Int = -1,
    @JsonProperty("identify_time")
    val identifyTime: Long = 0,
    @JsonProperty("validate_data")
    val validateData: String = "",
    @JsonProperty("openid")
    val openid: String = "",
    @JsonProperty("user_id_key")
    val userIdKey: String = "",
    @JsonProperty("finish_time")
    val finishTime: Long = 0,
    @JsonProperty("id_card_number_md5")
    val idCardNoMD5: String = "",
    @JsonProperty("name_utf8_md5")
    val nameMD5: String = "",
) {

    companion object {
        /**
         * 创建错误结果
         * @param errCode 错误码
         * @param errMsg 错误信息
         * @return 错误结果
         */
        @JvmStatic
        fun error(errCode: Int = -1, errMsg: String) = WeixinFaceIdentifyResult(errCode = errCode, errMsg = errMsg)
    }

    /**
     * 是否成功
     * @return 是否成功
     */
    fun isOk() = errCode == 0 && identifyRet == 0

    /**
     * 是否匹配
     * @param idCardNo 身份证号
     * @param name 姓名
     * @return 是否匹配
     */
    fun matches(idCardNo: String, name: String): Boolean {
        // 确保身份证号最后一位X为大写，其他保持原样
        val normalizedIdCardNo = if (idCardNo.length == 18 && idCardNo.last() == 'x') {
            idCardNo.dropLast(1) + "X"
        } else {
            idCardNo
        }
        val calculatedIdCardNoMD5 = DigestUtils.md5Hex(normalizedIdCardNo).uppercase()
        val calculatedNameMD5 = DigestUtils.md5Hex(name).uppercase()

        return idCardNoMD5.equals(calculatedIdCardNoMD5, ignoreCase = true) &&
                nameMD5.equals(calculatedNameMD5, ignoreCase = true)
    }

}

object WeixinFaceIdentification {

    private val log = LoggerFactory.getLogger(WeixinFaceIdentification::class.java)

    private const val WEIXIN_FACE_API_URL = "https://api.weixin.qq.com/cityservice/face/identify/getinfo"

    // 错误码常量
    private const val ERROR_CODE_NETWORK = -1001       // 网络连接异常
    private const val ERROR_CODE_TIMEOUT = -1002       // 请求超时
    private const val ERROR_CODE_SSL = -1003           // SSL证书异常
    private const val ERROR_CODE_DNS = -1004           // DNS解析失败
    private const val ERROR_CODE_JSON_PARSE = -1005    // JSON解析异常
    private const val ERROR_CODE_HTTP = -1006          // HTTP响应异常
    private const val ERROR_CODE_PARAM = -1007         // 参数错误
    private const val ERROR_CODE_UNKNOWN = -1999       // 未知异常

    // 默认HTTP客户端，使用HttpClientFactory创建
    private val defaultHttpClient: OkHttpClient by lazy {
        HttpClientFactory.createWithRequestIdLogging(
            logger = log,
            connectTimeout = 5000,  // 5秒连接超时
            readTimeout = 30_000,   // 30秒读取超时
            writeTimeout = 30_000   // 30秒写入超时
        )
    }

    /**
     * 使用默认HTTP客户端进行人脸核身认证
     * @param accessToken 微信访问令牌
     * @param verifyResult 核身结果
     * @return 核身结果
     */
    @JvmStatic
    fun identify(accessToken: String, verifyResult: String?): WeixinFaceIdentifyResult {
        return identify(defaultHttpClient, accessToken, verifyResult)
    }

    /**
     * 使用指定的HTTP客户端进行人脸核身认证
     * @param httpClient HTTP客户端
     * @param accessToken 微信访问令牌
     * @param verifyResult 核身结果
     * @return 核身结果
     */
    @JvmStatic
    fun identify(httpClient: OkHttpClient, accessToken: String, verifyResult: String?): WeixinFaceIdentifyResult {
        // 参数校验
        if (verifyResult.isNullOrBlank()) {
            log.warn("微信人脸核身参数校验失败: verifyResult为空")
            return WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_PARAM,
                errMsg = "verify_result参数不能为空"
            )
        }

        if (accessToken.isBlank()) {
            log.warn("微信人脸核身参数校验失败: accessToken为空")
            return WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_PARAM,
                errMsg = "access_token参数不能为空"
            )
        }

        // 使用HttpUrl安全构建URL，自动处理URL编码
        val httpUrl = WEIXIN_FACE_API_URL.toHttpUrl().newBuilder()
            .addQueryParameter("access_token", accessToken)
            .build()

        // 使用Jackson安全序列化JSON，防止JSON注入
        val requestData = mapOf("verify_result" to verifyResult)
        val requestBody = try {
            JacksonKit.writeValueAsString(requestData)
                .toRequestBody("application/json; charset=utf-8".toMediaType())
        } catch (e: JsonProcessingException) {
            log.error("微信人脸核身请求JSON序列化失败", e)
            return WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_JSON_PARSE,
                errMsg = "请求参数序列化失败: ${e.message}"
            )
        }

        val request = Request.Builder()
            .url(httpUrl)
            .post(requestBody)
            .build()

        return handleApiCall(httpClient, request)
    }

    /**
     * 使用默认HTTP客户端进行人脸核身认证（带重试机制）
     * @param accessToken 微信访问令牌
     * @param verifyResult 核身结果
     * @param maxRetries 最大重试次数，默认3次
     * @param retryDelayMs 重试间隔毫秒数，默认1000ms
     * @return 核身结果
     */
    @JvmStatic
    fun identifyWithRetry(
        accessToken: String,
        verifyResult: String?,
        maxRetries: Int = 3,
        retryDelayMs: Long = 1000
    ): WeixinFaceIdentifyResult {
        return identifyWithRetry(defaultHttpClient, accessToken, verifyResult, maxRetries, retryDelayMs)
    }

    /**
     * 使用指定HTTP客户端进行人脸核身认证（带重试机制）
     * @param httpClient HTTP客户端
     * @param accessToken 微信访问令牌
     * @param verifyResult 核身结果
     * @param maxRetries 最大重试次数，默认3次
     * @param retryDelayMs 重试间隔毫秒数，默认1000ms
     * @return 核身结果
     */
    private fun identifyWithRetry(
        httpClient: OkHttpClient,
        accessToken: String,
        verifyResult: String?,
        maxRetries: Int = 3,
        retryDelayMs: Long = 1000
    ): WeixinFaceIdentifyResult {
        var lastResult: WeixinFaceIdentifyResult? = null

        repeat(maxRetries + 1) { attempt ->
            if (attempt > 0) {
                log.info("微信人脸核身第${attempt}次重试，间隔${retryDelayMs}ms")
                try {
                    Thread.sleep(retryDelayMs)
                } catch (_: InterruptedException) {
                    Thread.currentThread().interrupt()
                    return WeixinFaceIdentifyResult.error(
                        errCode = ERROR_CODE_UNKNOWN,
                        errMsg = "重试过程被中断"
                    )
                }
            }

            val result = identify(httpClient, accessToken, verifyResult)
            lastResult = result

            // 如果成功或者是业务错误（非网络异常），则直接返回
            if (result.isOk() || !isRetryableError(result.errCode)) {
                if (attempt > 0) {
                    log.info("微信人脸核身第${attempt}次重试成功")
                }
                return result
            }

            if (attempt < maxRetries) {
                log.warn("微信人脸核身第${attempt + 1}次尝试失败，错误码: ${result.errCode}, 错误信息: ${result.errMsg}")
            }
        }

        log.error("微信人脸核身重试${maxRetries}次后仍然失败")
        return lastResult ?: WeixinFaceIdentifyResult.error(
            errCode = ERROR_CODE_UNKNOWN,
            errMsg = "重试失败"
        )
    }

    /**
     * 判断是否为可重试的错误
     */
    private fun isRetryableError(errCode: Int): Boolean {
        return when (errCode) {
            ERROR_CODE_NETWORK,     // 网络连接异常
            ERROR_CODE_TIMEOUT,     // 请求超时
            ERROR_CODE_DNS          // DNS解析失败
                -> true

            else -> false
        }
    }

    /**
     * 处理API调用，包含完善的异常处理机制
     * @param httpClient HTTP客户端
     * @param request 请求
     * @return 核身结果
     */
    private fun handleApiCall(httpClient: OkHttpClient, request: Request): WeixinFaceIdentifyResult {
        return try {
            httpClient.newCall(request).execute().use { response ->
                val responseText = response.body?.string() ?: ""

                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    log.warn("微信人脸核身API响应失败: $errorMsg, body: $responseText")
                    return WeixinFaceIdentifyResult.error(
                        errCode = ERROR_CODE_HTTP,
                        errMsg = "API调用失败: $errorMsg"
                    )
                }

                // 检查响应体是否为空
                if (responseText.isBlank()) {
                    log.warn("微信人脸核身API返回空响应体")
                    return WeixinFaceIdentifyResult.error(
                        errCode = ERROR_CODE_HTTP,
                        errMsg = "API返回空响应"
                    )
                }

                try {
                    JacksonKit.readValue(responseText, WeixinFaceIdentifyResult::class.java)
                } catch (e: JsonProcessingException) {
                    log.error("微信人脸核身响应JSON解析失败: responseText=$responseText", e)
                    WeixinFaceIdentifyResult.error(
                        errCode = ERROR_CODE_JSON_PARSE,
                        errMsg = "响应数据解析失败: ${e.message}"
                    )
                }
            }
        } catch (e: UnknownHostException) {
            log.error("微信人脸核身DNS解析失败: ${e.message}", e)
            WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_DNS,
                errMsg = "域名解析失败，请检查网络连接"
            )
        } catch (e: ConnectException) {
            log.error("微信人脸核身网络连接失败: ${e.message}", e)
            WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_NETWORK,
                errMsg = "网络连接失败，请检查网络状态"
            )
        } catch (e: SocketTimeoutException) {
            log.error("微信人脸核身请求超时: ${e.message}", e)
            WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_TIMEOUT,
                errMsg = "请求超时，请稍后重试"
            )
        } catch (e: SSLException) {
            log.error("微信人脸核身SSL证书异常: ${e.message}", e)
            WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_SSL,
                errMsg = "SSL证书验证失败"
            )
        } catch (e: IOException) {
            log.error("微信人脸核身IO异常: ${e.message}", e)
            WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_NETWORK,
                errMsg = "网络IO异常: ${e.message}"
            )
        } catch (e: IllegalArgumentException) {
            log.error("微信人脸核身参数异常: ${e.message}", e)
            WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_PARAM,
                errMsg = "请求参数异常: ${e.message}"
            )
        } catch (e: Exception) {
            log.error("微信人脸核身发生未知异常: ${e.javaClass.simpleName} - ${e.message}", e)
            WeixinFaceIdentifyResult.error(
                errCode = ERROR_CODE_UNKNOWN,
                errMsg = "系统异常: ${e.message ?: e.javaClass.simpleName}"
            )
        }
    }

}