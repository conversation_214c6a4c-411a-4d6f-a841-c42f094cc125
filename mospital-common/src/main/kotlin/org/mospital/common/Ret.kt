package org.mospital.common

class Ret() : HashMap<String, Any>() {
    companion object {
        private const val serialVersionUID: Long = 9153441360332043544L

        private const val STATE = "state"
        private const val STATE_OK = "ok"
        private const val STATE_FAIL = "fail"

        private const val DATA = "data"
        private const val MSG = "msg"

        fun ok(data: Any? = null): Ret = Ret().ok().apply {
            data?.let {
                this.data(it)
            }
        }
        fun fail(msg: String): Ret = Ret().fail().msg(msg)

    }

    fun ok(): Ret {
        super.put(STATE, STATE_OK)
        return this
    }

    fun fail(): Ret {
        super.put(STATE, STATE_FAIL)
        return this
    }

    fun data(data: Any): Ret {
        super.put(DATA, data)
        return this
    }

    fun msg(msg: String): Ret {
        super.put(MSG, msg)
        return this
    }

    fun isOk(): Boolean {
        return when (super.get(STATE)) {
            STATE_OK -> true
            STATE_FAIL -> false
            else -> throw IllegalStateException("调用 isOk() 之前，必须先调用 ok()、fail() 方法")
        }
    }

    fun isFail(): Boolean {
        return when (super.get(STATE)) {
            STATE_OK -> false
            STATE_FAIL -> true
            else -> throw IllegalStateException("调用 isFail() 之前，必须先调用 ok()、fail() 方法")
        }
    }

    fun set(key: String, value: Any): Ret {
        requireNotNull(value)
        super.put(key, value)
        return this
    }

    fun set(map: Map<String, Any>): Ret {
        super.putAll(map)
        return this
    }

    fun set(ret: Ret): Ret {
        super.putAll(ret)
        return this
    }

    override fun remove(key: String): Ret {
        super.remove(key)
        return this
    }

    @Suppress("UNCHECKED_CAST")
    fun <T> getAs(key: String = DATA): T {
        return super.get(key) as T
    }

    fun getStr(key: String): String = super.get(key)?.toString() ?: ""

    fun getMsg(): String {
        return getStr(MSG)
    }

    fun getBoolean(key: String): Boolean {
        val value = super.get(key)
        return if (value is Boolean) {
            value
        } else {
            throw NumberFormatException("$value is not a Boolean")
        }
    }

    fun getNumber(key: String): Number {
        val value = super.get(key)
        return if (value is Number) {
            value
        } else {
            throw NumberFormatException("$value is not a Number")
        }
    }

    fun getInt(key: String): Int = getNumber(key).toInt()
    fun getLong(key: String): Long = getNumber(key).toLong()
    fun getDouble(key: String): Double = getNumber(key).toDouble()
    fun getFloat(key: String): Float = getNumber(key).toFloat()

    override fun equals(other: Any?): Boolean {
        return other != null && other is Ret && super.equals(other)
    }

}
