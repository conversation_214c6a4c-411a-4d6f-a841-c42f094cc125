package org.mospital.common

import org.slf4j.LoggerFactory
import java.time.LocalDate
import java.time.Period
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

/**
 * 中国居民身份证工具类
 * 基于 GB 11643-1999 公民身份号码标准实现
 */
object IdCardUtil {

    private val log = LoggerFactory.getLogger(IdCardUtil::class.java)

    private val REGEX_ID_CARD = "^[0-9]{17}[0-9X]$".toRegex()

    private val BIRTH_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd")

    private val MIN_BIRTH_DATE = LocalDate.of(1800, 1, 1)

    // 权重因子数组，用于校验码计算
    private val WEIGHT_FACTORS = intArrayOf(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2)

    // 校验码对应表
    private val CHECK_CODE_MAP = charArrayOf('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2')

    /**
     * 检查身份证号码的基本格式
     * @param idCard 已清理的身份证号码（18位，大写）
     * @return 格式是否正确
     */
    private fun isValidIdCardFormat(idCard: String): Boolean {
        // The regex ensures the string is exactly 18 characters long, with the first 17 being digits
        // and the last being a digit or 'X'. This assumes the string has been uppercased.
        return idCard.matches(REGEX_ID_CARD)
    }

    /**
     * 清理并验证身份证号码的基本格式
     * @param idCard 原始身份证号码
     * @return 清理后的身份证号码，格式不正确时返回null
     */
    private fun cleanAndValidateIdCard(idCard: String?): String? {
        if (idCard.isNullOrBlank()) {
            return null
        }
        
        val cleanIdCard = idCard.trim().uppercase()
        return if (isValidIdCardFormat(cleanIdCard)) cleanIdCard else null
    }

    /**
     * 从身份证号码中解析出生日期
     * @param idCard 已清理的身份证号码（18位）
     * @return 解析成功的LocalDate，失败时返回null
     */
    private fun parseBirthDateFromIdCard(idCard: String): LocalDate? {
        val birthDateStr = idCard.substring(6, 14)
        return try {
            LocalDate.parse(birthDateStr, BIRTH_DATE_FORMATTER)
        } catch (e: DateTimeParseException) {
            null
        }
    }

    /**
     * 验证身份证号码是否有效
     * @param idCard 身份证号码
     * @return 是否有效
     */
    @JvmStatic
    fun isValid(idCard: String?): Boolean {
        val cleanIdCard = cleanAndValidateIdCard(idCard) ?: return false

        // 出生日期有效性检查
        if (!isValidBirthDate(cleanIdCard)) {
            return false
        }

        // 校验码验证
        return isValidCheckCode(cleanIdCard)
    }

    /**
     * 提取身份证号码中的性别
     * @param idCard 身份证号码
     * @return 性别枚举，格式不正确时返回 null
     */
    @JvmStatic
    fun extractGender(idCard: String?): Gender? {
        val cleanIdCard = cleanAndValidateIdCard(idCard) ?: return null

        // 第17位数字（顺序码的最后一位）决定性别
        val genderDigit = cleanIdCard[16].digitToInt()

        return if (genderDigit % 2 == 1) Gender.MALE else Gender.FEMALE  // 奇数为男性，偶数为女性
    }

    /**
     * 提取身份证号码中的出生日期
     * @param idCard 身份证号码
     * @return 出生日期，格式为 LocalDate，格式不正确时返回 null
     */
    @JvmStatic
    fun extractBirthDate(idCard: String?): LocalDate? {
        val cleanIdCard = cleanAndValidateIdCard(idCard) ?: return null
        
        val birthDate = parseBirthDateFromIdCard(cleanIdCard)
        if (birthDate == null) {
            log.debug("Could not parse birth date from invalid ID card: {}", cleanIdCard)
        }
        return birthDate
    }

    /**
     * 验证出生日期是否有效
     * @param idCard 身份证号码
     * @return 出生日期是否有效
     */
    private fun isValidBirthDate(idCard: String): Boolean {
        val birthDate = parseBirthDateFromIdCard(idCard)
        if (birthDate == null) {
            log.warn("Could not parse birth date from ID card: {}", idCard)
            return false
        }
        
        val now = LocalDate.now()
        // 出生日期不能晚于当前日期，且不能早于1800年（放宽限制以支持历史测试数据）
        return !birthDate.isAfter(now) && !birthDate.isBefore(MIN_BIRTH_DATE)
    }

    /**
     * 验证校验码是否正确
     * @param idCard 身份证号码
     * @return 校验码是否正确
     */
    private fun isValidCheckCode(idCard: String): Boolean {
        // 计算前17位的加权和
        val sum = (0..16).sumOf { idCard[it].digitToInt() * WEIGHT_FACTORS[it] }

        // 计算模11的余数
        val remainder = sum % 11

        // 获取对应的校验码
        val expectedCheckCode = CHECK_CODE_MAP[remainder]

        // 比较实际校验码与期望校验码
        return idCard[17] == expectedCheckCode
    }

    /**
     * 获取性别描述
     * @param idCard 身份证号码
     * @return 性别描述：男、女
     */
    @JvmStatic
    fun getGenderDescription(idCard: String?): String? {
        return extractGender(idCard)?.description
    }

    /**
     * 获取年龄
     * @param idCard 身份证号码
     * @return 年龄，格式不正确时返回 null
     */
    @JvmStatic
    fun extractAge(idCard: String?): Int? {
        val birthDate = extractBirthDate(idCard) ?: return null
        val now = LocalDate.now()

        return Period.between(birthDate, now).years
    }
}
