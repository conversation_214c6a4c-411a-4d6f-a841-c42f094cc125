package org.mospital.common

import com.fasterxml.jackson.annotation.JsonIgnore
import org.dromara.hutool.core.codec.binary.Base64
import org.dromara.hutool.core.io.file.FileNameUtil
import org.dromara.hutool.crypto.KeyUtil
import org.dromara.hutool.crypto.SecureUtil
import org.dromara.hutool.crypto.symmetric.SymmetricAlgorithm
import org.mospital.jackson.JacksonKit

data class DownloaderTicket(
    val url: String,
    val expiredTime: Long = 0,
    val fileName: String = FileNameUtil.getName(url),
    @JsonIgnore
    var aesKey: String = defaultAesKey,
) {

    companion object {

        private val defaultAesKey: String = "Oi6+d0TPbwsiKf1dHqu1Pw=="
        fun parse(aesKey: String = defaultAesKey, token: String): DownloaderTicket? {
            return try {
                val jsonText: String = SecureUtil.aes(Base64.decode(aesKey)).decryptStr(token)
                JacksonKit.readValue(jsonText, DownloaderTicket::class.java).apply {
                    this.aesKey = aesKey
                }
            } catch (e: Exception) {
                null
            }
        }

        fun randomAesKey(): String {
            return Base64.encode(KeyUtil.generateKey(SymmetricAlgorithm.AES.value).encoded)
        }
    }

    @JsonIgnore
    fun isValid(): Boolean {
        return expiredTime <= 0L || expiredTime > System.currentTimeMillis()
    }

    fun generateToken(): String {
        val jsonText: String = JacksonKit.writeValueAsString(this)
        return SecureUtil.aes(Base64.decode(aesKey)).encryptBase64(jsonText)
    }
}
