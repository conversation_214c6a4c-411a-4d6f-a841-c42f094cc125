package org.mospital.common

import org.mospital.jackson.DateTimeFormatters
import java.time.LocalDate
import java.util.UUID

object IdUtil {

    fun idToLocalDate(id: Long): LocalDate {
        return LocalDate.parse(id.toString(), DateTimeFormatters.PURE_DATE_FORMATTER)
    }

    fun localDateToId(localDate: LocalDate): Long {
        return localDate.format(DateTimeFormatters.PURE_DATE_FORMATTER).toLong()
    }

    fun simpleUUID(): String {
        return UUID.randomUUID().toString().replace("-", "")
    }

}
