package org.mospital.common

import java.io.File
import java.util.*

/**
 * 配置数据类生成器
 * 用于从 .setting 文件生成对应的 Kotlin 数据类和 YAML 配置文件
 */
object ConfigGenerator {

    /**
     * 为指定模块生成配置类和 YAML 文件
     *
     * @param moduleName 模块名称（如 "allinpay", "donggang" 等）
     * @param settingFilePath .setting 文件路径
     * @param outputPackage 输出包名
     */
    fun generateConfigForModule(
        moduleName: String,
        settingFilePath: String,
        outputPackage: String
    ) {
        val settingFile = File(settingFilePath)
        if (!settingFile.exists()) {
            println("❌ 配置文件不存在: $settingFilePath")
            return
        }

        println("🔄 开始为模块 $moduleName 生成配置...")

        // 解析配置文件
        val configData = parseSettingFile(settingFile)
        
        // 生成数据类
        val configClassName = "${moduleName.replaceFirstChar { it.uppercase() }}Config"
        val dataClass = generateDataClass(configClassName, configData, outputPackage)
        
        // 生成 YAML 文件
        val yamlContent = generateYamlContent(configData)
        
        println("✅ 配置类生成完成:")
        println("📄 数据类:")
        println(dataClass)
        println("\n📄 YAML 配置:")
        println(yamlContent)
        
        println("\n💡 使用 ConfigLoader 的示例代码:")
        generateUsageExample(moduleName, configClassName, outputPackage)
    }

    /**
     * 解析 .setting 文件
     */
    private fun parseSettingFile(file: File): Map<String, Map<String, String>> {
        val result = mutableMapOf<String, MutableMap<String, String>>()
        val globalSection = mutableMapOf<String, String>()
        var currentSection = "global"
        result[currentSection] = globalSection

        file.readLines().forEach { line ->
            val trimmedLine = line.trim()
            
            when {
                // 跳过空行和注释
                trimmedLine.isEmpty() || trimmedLine.startsWith("#") -> return@forEach
                
                // 处理分组 [section]
                trimmedLine.matches(Regex("\\[(.+)]")) -> {
                    currentSection = trimmedLine.removeSurrounding("[", "]")
                    result[currentSection] = mutableMapOf()
                }
                
                // 处理键值对
                "=" in trimmedLine -> {
                    val (key, value) = trimmedLine.split("=", limit = 2).map { it.trim() }
                    result[currentSection]!![key] = value
                }
            }
        }

        return result.filterValues { it.isNotEmpty() }
    }

    /**
     * 生成 Kotlin 数据类代码
     */
    private fun generateDataClass(
        className: String,
        configData: Map<String, Map<String, String>>,
        packageName: String
    ): String {
        val builder = StringBuilder()
        
        builder.appendLine("package $packageName")
        builder.appendLine()
        builder.appendLine("/**")
        builder.appendLine(" * $className 配置类")
        builder.appendLine(" * 对应 ${className.lowercase().removeSuffix("config")}.yaml 配置文件")
        builder.appendLine(" */")
        
        // 如果只有一个分组或全局配置，生成简单的数据类
        if (configData.size == 1 && (configData.containsKey("global") || configData.size == 1)) {
            val properties = configData.values.first()
            builder.appendLine("data class $className(")
            properties.entries.forEachIndexed { index, (key, _) ->
                val propertyName = toCamelCase(key)
                val isLast = index == properties.size - 1
                builder.append("    val $propertyName: String")
                if (!isLast) builder.append(",")
                builder.appendLine()
            }
            builder.appendLine(")")
        } else {
            // 生成主配置类和子配置类
            builder.appendLine("data class $className(")
            configData.entries.forEachIndexed { index, (sectionName, _) ->
                val propertyName = toCamelCase(sectionName)
                val propertyType = "${propertyName.replaceFirstChar { it.uppercase() }}Config"
                val isLast = index == configData.size - 1
                builder.append("    val $propertyName: $propertyType")
                if (!isLast) builder.append(",")
                builder.appendLine()
            }
            builder.appendLine(")")
            builder.appendLine()
            
            // 生成子配置类
            configData.forEach { (sectionName, properties) ->
                val sectionClassName = "${toCamelCase(sectionName).replaceFirstChar { it.uppercase() }}Config"
                builder.appendLine("/**")
                builder.appendLine(" * ${sectionName.uppercase()} 相关配置")
                builder.appendLine(" */")
                builder.appendLine("data class $sectionClassName(")
                properties.entries.forEachIndexed { index, (key, _) ->
                    val propertyName = toCamelCase(key)
                    val isLast = index == properties.size - 1
                    builder.append("    val $propertyName: String")
                    if (!isLast) builder.append(",")
                    builder.appendLine()
                }
                builder.appendLine(")")
                if (sectionName != configData.keys.last()) {
                    builder.appendLine()
                }
            }
        }
        
        return builder.toString()
    }

    /**
     * 生成 YAML 配置内容
     */
    private fun generateYamlContent(configData: Map<String, Map<String, String>>): String {
        val builder = StringBuilder()
        
        if (configData.size == 1 && configData.containsKey("global")) {
            // 全局配置，不需要分组
            configData["global"]!!.forEach { (key, value) ->
                val yamlKey = toCamelCase(key)
                val yamlValue = if (value.isEmpty()) "\"\"" else "\"$value\""
                builder.appendLine("$yamlKey: $yamlValue")
            }
        } else {
            // 分组配置
            configData.entries.forEachIndexed { sectionIndex, (sectionName, properties) ->
                if (sectionName != "global") {
                    builder.appendLine("$sectionName:")
                    properties.forEach { (key, value) ->
                        val yamlKey = toCamelCase(key)
                        val yamlValue = if (value.isEmpty()) "\"\"" else "\"$value\""
                        builder.appendLine("  $yamlKey: $yamlValue")
                    }
                    if (sectionIndex < configData.size - 1) {
                        builder.appendLine()
                    }
                }
            }
        }
        
        return builder.toString()
    }

    /**
     * 生成使用示例代码
     */
    private fun generateUsageExample(moduleName: String, configClassName: String, packageName: String) {
        val settingClassName = "${moduleName.replaceFirstChar { it.uppercase() }}Setting"
        
        println("""
// 在 ${settingClassName}.kt 中替换为:
package $packageName

import org.mospital.jackson.ConfigLoader

object $settingClassName {

    private val config: $configClassName by lazy {
        ConfigLoader.loadConfig("${moduleName.lowercase()}.yaml", ${configClassName}::class.java)
    }

    // 根据原有属性调整 getter 方法
    // 例如: val URL: String get() = config.url
}
        """.trimIndent())
    }

    /**
     * 转换为驼峰命名
     */
    private fun toCamelCase(input: String): String {
        return input.split("_", " ", "-")
            .mapIndexed { index, word ->
                if (index == 0) word.lowercase()
                else word.replaceFirstChar { it.uppercase() }
            }
            .joinToString("")
    }

    @JvmStatic
    fun main(args: Array<String>) {
        // 示例用法
        println("🔧 配置生成器")
        println("使用示例:")
        println("ConfigGenerator.generateConfigForModule(")
        println("    \"allinpay\",")
        println("    \"mospital-allinpay/src/test/resources/allinpay.setting\",")
        println("    \"org.mospital.allinpay\"")
        println(")")
    }
} 