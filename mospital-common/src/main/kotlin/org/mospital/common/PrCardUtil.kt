package org.mospital.common

import org.dromara.hutool.core.lang.Validator
import java.time.LocalDate
import java.time.format.DateTimeFormatter


object PrCardUtil {

    private val PR_CARD15_REGEX = Regex("^[A-Z]{3}[0-9]{12}\$")

    fun isValidCard15(prCard: String): Boolean {
        if (prCard.length != 15) {
            return false
        }

        if (!PR_CARD15_REGEX.matches(prCard)) {
            return false
        }

        if (!Validator.isBirthday("19" + prCard.substring(7, 13))) {
            return false
        }

        return prCard.substring(14) == calculateCheckPoint(prCard).toString()
    }

    private fun letterToDecimal(letter: Char): Int {
        if (letter in 'A'..'Z') {
            return letter.code - 'A'.code + 10
        } else {
            throw IllegalArgumentException("Invalid character: $letter")
        }
    }

    private fun calculateCheckPoint(prCard: String): Int {
        val weights = intArrayOf(7, 3, 1, 7, 3, 1, 7, 3, 1, 7, 3, 1, 7, 3)
        var sum = 0
        for (i in 0..13) {
            val char = prCard[i]
            var num = if (char.isDigit()) {
                char.toString().toInt()
            } else {
                letterToDecimal(char)
            }
            sum += num * weights[i]
        }

        return sum % 10
    }

    fun getBirthByPrCard15(prCard: String): LocalDate {
        require(isValidCard15(prCard)) { "Invalid PR Card: $prCard" }

        val birthday = prCard.substring(7, 13)
        var birthDate = LocalDate.parse(birthday, DateTimeFormatter.ofPattern("yyMMdd"))
        return if (birthDate.year > 2023) {
            // 2023年后不存在15位永久居留证号
            birthDate.minusYears(100L)
        } else {
            birthDate
        }
    }

}
