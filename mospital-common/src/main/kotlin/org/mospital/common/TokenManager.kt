package org.mospital.common

import com.fasterxml.jackson.core.type.TypeReference
import org.apache.commons.codec.binary.Base64
import org.mospital.jackson.JacksonKit

object TokenManager {

    private val SECRET_KEY = Base64.decodeBase64("0GPtRuUY2Jv/rqT48saltA==")

    fun generateToken(data: Any): String {
        val jsonText: String = JacksonKit.writeValueAsString(data)
        return AESUtil.encryptAES(SECRET_KEY, jsonText)
            .replace('+', '-')
            .replace('/', '_')
            .replace("=", "")
    }

    fun <T> parseToken(token: String, valueType: TypeReference<T>): T? {
        return try {
            val urlSafeToken = token.replace('-', '+').replace('_', '/')
            val jsonText: String = AESUtil.decryptAES(SECRET_KEY, urlSafeToken)
            JacksonKit.readValue(jsonText, valueType)
        } catch (_: Exception) {
            null
        }
    }

}