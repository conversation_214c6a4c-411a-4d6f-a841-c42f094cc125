package org.mospital.common.jaxb

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import jakarta.xml.bind.annotation.adapters.XmlAdapter

class LocalDateTimeJaxbAdapter : XmlAdapter<String, LocalDateTime>() {

    companion object {
        private val outputFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        private val formatters: Array<DateTimeFormatter> = arrayOf(
            DateTimeFormatter.ofPattern("yyyy-M-d H:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/M/d H:mm:ss"),
            DateTimeFormatter.ofPattern("yyyyMMddHH:mm:ss")
        )
    }

    override fun unmarshal(v: String?): LocalDateTime? {
        if (v.isNullOrBlank()) {
            return null
        }
        formatters.forEach { formatter ->
            try {
                return LocalDateTime.parse(v, formatter)
            } catch (_: Exception) {
                // do nothing
            }
        }
        return null
    }

    override fun marshal(v: LocalDateTime?): String {
        return v?.format(outputFormatter) ?: ""
    }

}
