package org.mospital.common.jaxb

import jakarta.xml.bind.annotation.adapters.XmlAdapter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 时间转换器：格式 格式yyyy-MM-dd HH:mm:ss
 */
class DateTimeAdapter : XmlAdapter<String, Date>() {

    override fun marshal(v: Date?): String? {
        return v?.let { SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(v) }
    }

    override fun unmarshal(v: String?): Date? {
        return v?.let {
            try {
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(v)
            } catch (_: Exception) {
                null
            }
        }
    }
}
