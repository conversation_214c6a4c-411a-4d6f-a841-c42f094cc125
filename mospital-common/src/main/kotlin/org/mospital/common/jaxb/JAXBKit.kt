package org.mospital.common.jaxb

import org.dromara.hutool.core.io.BomReader
import java.io.StringWriter
import java.util.concurrent.ConcurrentHashMap
import jakarta.xml.bind.JAXBContext
import jakarta.xml.bind.JAXBIntrospector

object JAXBKit {

    private val contexts = ConcurrentHashMap<String, JAXBContext>()

    fun <T> getJAXBContext(clazz: Class<T>): JAXBContext {
        return contexts.getOrPut(clazz.name) {
            JAXBContext.newInstance(clazz)
        }
    }

    @Suppress("UNCHECKED_CAST")
    fun <T> unmarshal(clazz: Class<T>, xml: String): T {
        val context = getJAXBContext(clazz)
        val unmarshaller = context.createUnmarshaller()
        val reader = BomReader(xml.toByteArray().inputStream())
        return JAXBIntrospector.getValue(unmarshaller.unmarshal(reader)) as T
    }

    fun <T : Any> marshal(obj: T): String {
        val context = getJAXBContext(obj.javaClass)
        val marshaller = context.createMarshaller()
        val writer = StringWriter().apply {
            marshaller.marshal(obj, this)
        }
        return writer.toString()
    }

    /**
     * Escape special characters in the content of the specified tag in the XML string.
     */
    fun escapeSpecialChars(xml: String, tag: String): String {
        val startTag = "<$tag>"
        val endTag = "</$tag>"
        val startIndex = xml.indexOf(startTag)
        val endIndex = xml.indexOf(endTag)
        if (startIndex == -1 || endIndex == -1) {
            return xml
        }
        val content = xml.substring(startIndex + startTag.length, endIndex)
        val escapedContent = content
            .replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("'", "&apos;")
            .replace("\"", "&quot;")
        val newXml = xml.replaceRange(startIndex + startTag.length, endIndex, escapedContent)
        val newEndIndex = newXml.indexOf(endTag)
        return escapeSpecialChars(newXml.substring(newEndIndex + endTag.length), tag).let {
            newXml.substring(0, newEndIndex + endTag.length) + it
        }
    }

}
