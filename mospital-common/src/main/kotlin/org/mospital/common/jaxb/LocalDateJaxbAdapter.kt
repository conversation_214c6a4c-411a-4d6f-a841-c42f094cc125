package org.mospital.common.jaxb

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import jakarta.xml.bind.annotation.adapters.XmlAdapter

class LocalDateJaxbAdapter : XmlAdapter<String, LocalDate>() {

    companion object {
        private val outputFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        private val formatters: Array<DateTimeFormatter> = arrayOf(
            DateTimeFormatter.ofPattern("yyyy-M-d"),
            DateTimeFormatter.ofPattern("yyyy/M/d")
        )
    }

    override fun unmarshal(v: String?): LocalDate? {
        if (v.isNullOrBlank()) {
            return null
        }
        formatters.forEach { formatter ->
            try {
                return LocalDate.parse(v, formatter)
            } catch (_: Exception) {
                // do nothing
            }
        }
        return null
    }

    override fun marshal(v: LocalDate?): String {
        return v?.format(outputFormatter) ?: ""
    }

}
