package org.mospital.common

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class StringKitTest {

    @Test
    fun `removeNonChinese should handle null and empty input`() {
        Assertions.assertEquals("", StringKit.removeNonChinese(null))
        Assertions.assertEquals("", StringKit.removeNonChinese(""))
        Assertions.assertEquals("", StringKit.removeNonChinese("   "))
    }

    @Test
    fun `removeNonChinese should remove non-Chinese characters`() {
        // 基本测试
        Assertions.assertEquals("中文", StringKit.removeNonChinese("中文123"))
        Assertions.assertEquals("测试", StringKit.removeNonChinese("Test测试Test"))
        Assertions.assertEquals("你好世界", StringKit.removeNonChinese("你好,世界!"))

        // 特殊字符测试
        Assertions.assertEquals("中文", StringKit.removeNonChinese("中@#￥%文"))
        Assertions.assertEquals("测试", StringKit.removeNonChinese("测试\n\t\r"))

        // 混合字符测试
        Assertions.assertEquals(
            "春节快乐新年好",
            StringKit.removeNonChinese("春节(Spring Festival)快乐！新年好(Happy New Year)!")
        )
    }

    @Test
    fun `removeNonChinese should keep Chinese characters from different Unicode blocks`() {
        // 基本汉字区测试 (4E00-9FA5)
        Assertions.assertEquals("你好", StringKit.removeNonChinese("你好"))

        // 扩展A区测试 (3400-4DBF)
        val extendedA = "㐀" // U+3400
        Assertions.assertEquals(extendedA, StringKit.removeNonChinese(extendedA))

        // 生僻字测试
        val rareChinese = "䶮" // 比较罕见的汉字
        Assertions.assertEquals(rareChinese, StringKit.removeNonChinese(rareChinese))

        // 混合Unicode区域测试
        val mixedChinese = "你好㐀䶮"
        Assertions.assertEquals(mixedChinese, StringKit.removeNonChinese(mixedChinese))
    }

    @Test
    fun `removeNonBasicChinese should handle null and empty input`() {
        Assertions.assertEquals("", StringKit.removeNonBasicChinese(null))
        Assertions.assertEquals("", StringKit.removeNonBasicChinese(""))
        Assertions.assertEquals("", StringKit.removeNonBasicChinese(" "))
    }

    @Test
    fun `removeNonBasicChinese should remove non basic Chinese characters`() {
        // 基本测试
        Assertions.assertEquals("你好", StringKit.removeNonBasicChinese("你好123"))
        Assertions.assertEquals("测试", StringKit.removeNonBasicChinese("Test测试Test"))

        // 特殊字符测试
        Assertions.assertEquals("中文", StringKit.removeNonBasicChinese("中@#￥%文"))
        Assertions.assertEquals("测试", StringKit.removeNonBasicChinese("测试\n\t\r"))

        // 扩展区汉字测试
        Assertions.assertEquals("测试", StringKit.removeNonBasicChinese("测试㐀")) // 㐀是扩展A区汉字(U+3400)
        Assertions.assertEquals("汉字", StringKit.removeNonBasicChinese("汉字䶮")) // 䶮是扩展B区汉字

        // 混合字符测试
        Assertions.assertEquals(
            "春节快乐新年好",
            StringKit.removeNonBasicChinese("春节(Spring Festival)快乐！新年好(Happy New Year)!")
        )
    }

    @Test
    fun `equalsIgnoreNonChinese should handle null values`() {
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese(null, null))
        Assertions.assertFalse(StringKit.equalsIgnoreNonChinese(null, "测试"))
        Assertions.assertFalse(StringKit.equalsIgnoreNonChinese("测试", null))
    }

    @Test
    fun `equalsIgnoreNonChinese should handle empty strings`() {
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("", ""))
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese(" ", ""))
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("123", ""))
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("abc", ""))
    }

    @Test
    fun `equalsIgnoreNonChinese should handle same reference`() {
        val text = "测试123"
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese(text, text))
    }

    @Test
    fun `equalsIgnoreNonChinese should compare Chinese characters only`() {
        // 基本比较
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("测试123", "测试abc"))
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("测试!", "测试@"))
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("你好世界123", "你好世界abc"))

        // 带空格和特殊字符
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("测 试", "测试"))
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("测\n试", "测试"))
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("测！试", "测。试"))

        // 不同顺序应该不相等
        Assertions.assertFalse(StringKit.equalsIgnoreNonChinese("测试", "试测"))

        // 部分匹配应该不相等
        Assertions.assertFalse(StringKit.equalsIgnoreNonChinese("测试", "测"))
        Assertions.assertFalse(StringKit.equalsIgnoreNonChinese("测试", "测试啊"))
    }

    @Test
    fun `equalsIgnoreNonChinese should handle mixed Unicode Chinese characters`() {
        // 基本汉字
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("你好123", "你好abc"))

        // 生僻字
        val rareChinese = "䶮" // 罕见汉字
        Assertions.assertTrue(StringKit.equalsIgnoreNonChinese("测试${rareChinese}123", "测试${rareChinese}abc"))

        // 扩展区汉字
        val extendedChinese = "㐀" // 扩展A区汉字
        Assertions.assertTrue(
            StringKit.equalsIgnoreNonChinese(
                "测试${extendedChinese}123",
                "测试${extendedChinese}abc"
            )
        )
    }

    @Test
    fun `equalsBasicChineseOnly should handle null values`() {
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly(null, null))
        Assertions.assertFalse(StringKit.equalsBasicChineseOnly(null, "测试"))
        Assertions.assertFalse(StringKit.equalsBasicChineseOnly("测试", null))
    }

    @Test
    fun `equalsBasicChineseOnly should handle empty strings`() {
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly("", ""))
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly(" ", ""))
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly("123", ""))
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly("abc", ""))
    }

    @Test
    fun `equalsBasicChineseOnly should handle same reference`() {
        val text = "测试123"
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly(text, text))
    }

    @Test
    fun `equalsBasicChineseOnly should compare basic Chinese characters only`() {
        // 基本汉字比较
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly("测试123", "测试abc"))
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly("你好世界!", "你好世界@"))
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly("中文ABC", "中文123"))

        // 扩展区汉字测试
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly("测试㐀", "测试"))  // 㐀为扩展A区汉字(U+3400)
        Assertions.assertTrue(StringKit.equalsBasicChineseOnly("汉字䶮", "汉字"))  // 䶮为扩展B区汉字

        // 不同顺序或内容应不相等
        Assertions.assertFalse(StringKit.equalsBasicChineseOnly("测试", "试测"))
        Assertions.assertFalse(StringKit.equalsBasicChineseOnly("测试", "测"))
        Assertions.assertFalse(StringKit.equalsBasicChineseOnly("测试", "测试啊"))
    }

    @Test
    fun `validateThing should handle null and empty input`() {
        // null和空字符串应该返回false
        Assertions.assertFalse(StringKit.validateThing(null))
        Assertions.assertFalse(StringKit.validateThing(""))
        Assertions.assertFalse(StringKit.validateThing("   "))
    }

    @Test
    fun `validateThing should validate string length`() {
        // 20个字符以内的应返回true
        Assertions.assertTrue(StringKit.validateThing("测试内容"))
        Assertions.assertTrue(StringKit.validateThing("1234567890"))
        Assertions.assertTrue(StringKit.validateThing("测试Test123!@#$"))
        Assertions.assertTrue(StringKit.validateThing("一二三四五六七八九十"))
        Assertions.assertTrue(StringKit.validateThing("一二三四五六七八九十一二三四五六七八九十")) // 恰好20个字符

        // 超过20个字符的应返回false
        Assertions.assertFalse(StringKit.validateThing("一二三四五六七八九十一二三四五六七八九十一")) // 21个字符
        Assertions.assertFalse(StringKit.validateThing("这个字符串超过了二十个字符所以会验证失败啊"))
    }

    @Test
    fun `validateThing should detect control characters`() {
        // 包含控制字符应返回false
        Assertions.assertFalse(StringKit.validateThing("测试\n换行"))
        Assertions.assertFalse(StringKit.validateThing("测试\r回车"))
        Assertions.assertFalse(StringKit.validateThing("测试\t制表符"))
        Assertions.assertFalse(StringKit.validateThing("\u0000测试空字符"))

        // 不包含控制字符应返回true
        Assertions.assertTrue(StringKit.validateThing("测试换行符"))
        Assertions.assertTrue(StringKit.validateThing("测试@#¥%……&*"))
    }

    @Test
    fun `sanitizeThing should handle null and empty input`() {
        // null和空字符串应返回空字符串
        Assertions.assertEquals("", StringKit.sanitizeThing(null))
        Assertions.assertEquals("", StringKit.sanitizeThing(""))
        Assertions.assertEquals("", StringKit.sanitizeThing("   "))
    }

    @Test
    fun `sanitizeThing should remove control characters`() {
        // 应移除控制字符
        Assertions.assertEquals("测试换行", StringKit.sanitizeThing("测试\n换行"))
        Assertions.assertEquals("测试回车", StringKit.sanitizeThing("测试\r回车"))
        Assertions.assertEquals("测试制表符", StringKit.sanitizeThing("测试\t制表符"))
        Assertions.assertEquals("测试空字符", StringKit.sanitizeThing("\u0000测试空字符"))

        // 复杂混合测试
        Assertions.assertEquals("测试各种控制字符", StringKit.sanitizeThing("测试\n\r\t\u0000各种控制字符"))
    }

    @Test
    fun `sanitizeThing should truncate long strings`() {
        // 应截断超过20个字符的字符串
        val longString = "一二三四五六七这个字符串超过了二十个字符所以会被截断的"
        Assertions.assertEquals("一二三四五六七这个字符串超过了二十个字符", StringKit.sanitizeThing(longString))

        // 刚好20字符不应被截断
        val exactString = "一二三四五六七八九十一二三四五六七八九十"
        Assertions.assertEquals(exactString, StringKit.sanitizeThing(exactString))

        // 同时有控制字符和超长的情况
        Assertions.assertEquals(
            "测试控制字符和超长字符串超过了二十个字",
            StringKit.sanitizeThing("测试\n\r控制字符和超长字符串超过了二十个字")
        )
    }
    
    @Test
    fun `validateName should handle null and empty input`() {
        // null和空字符串应该返回false
        Assertions.assertFalse(StringKit.validateName(null))
        Assertions.assertFalse(StringKit.validateName(""))
        Assertions.assertFalse(StringKit.validateName("   "))
    }
    
    @Test
    fun `validateName should detect control characters`() {
        // 包含控制字符应返回false
        Assertions.assertFalse(StringKit.validateName("张三\n李四"))
        Assertions.assertFalse(StringKit.validateName("John\rSmith"))
        Assertions.assertFalse(StringKit.validateName("王五\t赵六"))
        Assertions.assertFalse(StringKit.validateName("\u0000测试"))
    }
    
    @Test
    fun `validateName should validate Chinese name length`() {
        // 10个汉字以内的中文名应返回true
        Assertions.assertTrue(StringKit.validateName("张三"))
        Assertions.assertTrue(StringKit.validateName("李四王五"))
        Assertions.assertTrue(StringKit.validateName("一二三四五六七八九十")) // 恰好10个汉字
        
        // 超过10个汉字的中文名应返回false
        Assertions.assertFalse(StringKit.validateName("一二三四五六七八九十一")) // 11个汉字
        Assertions.assertFalse(StringKit.validateName("这个名字超过了十个汉字所以会验证失败"))
    }
    
    @Test
    fun `validateName should validate English name length`() {
        // 20个字母以内的英文名应返回true
        Assertions.assertTrue(StringKit.validateName("John"))
        Assertions.assertTrue(StringKit.validateName("John Smith"))
        Assertions.assertTrue(StringKit.validateName("abcdefghijklmnopqrst")) // 恰好20个字母
        
        // 超过20个字母的英文名应返回false
        Assertions.assertFalse(StringKit.validateName("abcdefghijklmnopqrstu")) // 21个字母
        Assertions.assertFalse(StringKit.validateName("This name is too long for validation"))
    }
    
    @Test
    fun `validateName should validate mixed Chinese and English name`() {
        // 10个字符以内的中英混合名应返回true
        Assertions.assertTrue(StringKit.validateName("张John"))
        Assertions.assertTrue(StringKit.validateName("王Smith李"))
        Assertions.assertTrue(StringKit.validateName("一二三四五六七八九J")) // 恰好10个字符
        
        // 超过10个字符的中英混合名应返回false
        Assertions.assertFalse(StringKit.validateName("一二三四五六七八九十Smith"))
        Assertions.assertFalse(StringKit.validateName("王五John Smith李四赵六"))
    }
    
    @Test
    fun `validateName should handle special cases`() {
        // 包含数字和符号按中文名规则（10个字符）处理
        Assertions.assertTrue(StringKit.validateName("张三123"))
        Assertions.assertTrue(StringKit.validateName("123456789")) // 9个字符
        Assertions.assertTrue(StringKit.validateName("1234567890")) // 10个字符
        Assertions.assertFalse(StringKit.validateName("12345678901")) // 11个字符
        
        // 特殊符号处理
        Assertions.assertTrue(StringKit.validateName("张-三"))
        Assertions.assertTrue(StringKit.validateName("@#¥%……&*"))
        Assertions.assertFalse(StringKit.validateName("@#¥%……&*()_+-="))
    }
    
    @Test
    fun `sanitizeName should handle null and empty input`() {
        // null和空字符串应返回空字符串
        Assertions.assertEquals("", StringKit.sanitizeName(null))
        Assertions.assertEquals("", StringKit.sanitizeName(""))
        Assertions.assertEquals("", StringKit.sanitizeName("   "))
    }
    
    @Test
    fun `sanitizeName should remove control characters`() {
        // 应移除控制字符
        Assertions.assertEquals("张三李四", StringKit.sanitizeName("张三\n李四"))
        Assertions.assertEquals("JohnSmith", StringKit.sanitizeName("John\rSmith"))
        Assertions.assertEquals("王五赵六", StringKit.sanitizeName("王五\t赵六"))
        Assertions.assertEquals("测试", StringKit.sanitizeName("\u0000测试"))
    }
    
    @Test
    fun `sanitizeName should truncate Chinese names`() {
        // 应截断超过10个字符的中文名
        val longChineseName = "一二三四五六七八九十一二三四"
        Assertions.assertEquals("一二三四五六七八九十", StringKit.sanitizeName(longChineseName))
        
        // 刚好10个字符不应被截断
        val exactChineseName = "一二三四五六七八九十"
        Assertions.assertEquals(exactChineseName, StringKit.sanitizeName(exactChineseName))
    }
    
    @Test
    fun `sanitizeName should truncate English names`() {
        // 应截断超过20个字符的英文名
        val longEnglishName = "abcdefghijklmnopqrstuvwxyz"
        Assertions.assertEquals("abcdefghijklmnopqrst", StringKit.sanitizeName(longEnglishName))
        
        // 刚好20个字符不应被截断
        val exactEnglishName = "abcdefghijklmnopqrst"
        Assertions.assertEquals(exactEnglishName, StringKit.sanitizeName(exactEnglishName))
        
        // 带空格的英文名处理
        val longEnglishNameWithSpaces = "John Smith Johnson Williams Thompson"
        Assertions.assertEquals("John Smith Johnson W", StringKit.sanitizeName(longEnglishNameWithSpaces))
    }
    
    @Test
    fun `sanitizeName should truncate mixed names`() {
        // 应截断超过10个字符的中英混合名
        val longMixedName = "张三李四Wang Smith"
        Assertions.assertEquals("张三李四Wang S", StringKit.sanitizeName(longMixedName))
        
        // 同时有控制字符和超长的混合情况
        Assertions.assertEquals(
            "张三李四王五赵六孙七",
            StringKit.sanitizeName("张三\n李四王五赵六孙七")
        )
    }
    
    @Test
    fun `sanitizeName should handle special cases`() {
        // 包含数字和符号按中文名规则（10个字符）处理
        val numericName = "12345678901234567890"
        Assertions.assertEquals("1234567890", StringKit.sanitizeName(numericName))
        
        // 特殊符号处理
        val specialCharsName = "@#¥%……&*()_+-=[]{}|;':\",./<>?"
        Assertions.assertEquals("@#¥%……&*()", StringKit.sanitizeName(specialCharsName))
    }
    
    @Test
    fun `validatePhrase should handle null and empty input`() {
        // null和空字符串应该返回false
        Assertions.assertFalse(StringKit.validatePhrase(null))
        Assertions.assertFalse(StringKit.validatePhrase(""))
        Assertions.assertFalse(StringKit.validatePhrase("   "))
    }
    
    @Test
    fun `validatePhrase should validate Chinese only`() {
        // 纯汉字应返回true
        Assertions.assertTrue(StringKit.validatePhrase("已完成"))
        Assertions.assertTrue(StringKit.validatePhrase("审核通过"))
        Assertions.assertTrue(StringKit.validatePhrase("处理中"))
        
        // 非纯汉字应返回false
        Assertions.assertFalse(StringKit.validatePhrase("已完成123"))
        Assertions.assertFalse(StringKit.validatePhrase("Pass"))
        Assertions.assertFalse(StringKit.validatePhrase("处理中!"))
        Assertions.assertFalse(StringKit.validatePhrase("你好 世界")) // 包含空格
    }
    
    @Test
    fun `validatePhrase should validate length`() {
        // 5个以内汉字应返回true
        Assertions.assertTrue(StringKit.validatePhrase("一"))
        Assertions.assertTrue(StringKit.validatePhrase("一二三"))
        Assertions.assertTrue(StringKit.validatePhrase("一二三四五")) // 恰好5个汉字
        
        // 超过5个汉字应返回false
        Assertions.assertFalse(StringKit.validatePhrase("一二三四五六")) // 6个汉字
        Assertions.assertFalse(StringKit.validatePhrase("这是一个很长的短语"))
    }
    
    @Test
    fun `validatePhrase should detect control characters`() {
        // 包含控制字符应返回false
        Assertions.assertFalse(StringKit.validatePhrase("你\n好"))
        Assertions.assertFalse(StringKit.validatePhrase("审核\t中"))
        Assertions.assertFalse(StringKit.validatePhrase("\r已完成"))
        Assertions.assertFalse(StringKit.validatePhrase("已\u0000通过"))
    }
    
    @Test
    fun `validatePhrase should handle extended Chinese characters`() {
        // 扩展区汉字也应当视为汉字
        Assertions.assertTrue(StringKit.validatePhrase("㐀㐁㐂㐃㐄")) // 扩展A区汉字
        
        // 扩展区汉字超过5个仍返回false
        Assertions.assertFalse(StringKit.validatePhrase("㐀㐁㐂㐃㐄㐅"))
        
        // 混合基本区和扩展区汉字
        Assertions.assertTrue(StringKit.validatePhrase("你好㐀㐁㐂"))
        Assertions.assertFalse(StringKit.validatePhrase("你好㐀㐁㐂㐃"))
    }
    
    @Test
    fun `sanitizePhrase should handle null and empty input`() {
        // null和空字符串应返回空字符串
        Assertions.assertEquals("", StringKit.sanitizePhrase(null))
        Assertions.assertEquals("", StringKit.sanitizePhrase(""))
        Assertions.assertEquals("", StringKit.sanitizePhrase("   "))
    }
    
    @Test
    fun `sanitizePhrase should remove non-Chinese characters`() {
        // 应移除非汉字字符
        Assertions.assertEquals("你好", StringKit.sanitizePhrase("你好123"))
        Assertions.assertEquals("审核通过", StringKit.sanitizePhrase("审核通过!"))
        Assertions.assertEquals("处理中", StringKit.sanitizePhrase("处理中 "))
        Assertions.assertEquals("完成", StringKit.sanitizePhrase("完成ABC"))
    }
    
    @Test
    fun `sanitizePhrase should remove control characters`() {
        // 应移除控制字符
        Assertions.assertEquals("你好", StringKit.sanitizePhrase("你\n好"))
        Assertions.assertEquals("审核中", StringKit.sanitizePhrase("审核\t中"))
        Assertions.assertEquals("已完成", StringKit.sanitizePhrase("\r已完成"))
        Assertions.assertEquals("已通过", StringKit.sanitizePhrase("已\u0000通过"))
    }
    
    @Test
    fun `sanitizePhrase should truncate long phrases`() {
        // 应截断超过5个汉字的短语
        val longPhrase = "这是一个很长的短语"
        Assertions.assertEquals("这是一个很", StringKit.sanitizePhrase(longPhrase))
        
        // 刚好5个汉字不应被截断
        val exactPhrase = "一二三四五"
        Assertions.assertEquals(exactPhrase, StringKit.sanitizePhrase(exactPhrase))
        
        // 同时有非汉字和超长的情况
        Assertions.assertEquals(
            "这是一个很",
            StringKit.sanitizePhrase("这是一个很长ABC的短语123")
        )
    }
    
    @Test
    fun `sanitizePhrase should handle extended Chinese characters`() {
        // 扩展区汉字处理
        Assertions.assertEquals("㐀㐁㐂㐃㐄", StringKit.sanitizePhrase("㐀㐁㐂㐃㐄"))
        Assertions.assertEquals("㐀㐁㐂㐃㐄", StringKit.sanitizePhrase("㐀㐁㐂㐃㐄㐅"))
        
        // 混合基本区和扩展区汉字
        Assertions.assertEquals("你好㐀㐁㐂", StringKit.sanitizePhrase("你好㐀㐁㐂"))
        Assertions.assertEquals("你好㐀㐁㐂", StringKit.sanitizePhrase("你好㐀㐁㐂㐃"))
    }
    
    @Test
    fun `sanitizePhrase should return empty for non-Chinese input`() {
        // 不包含汉字的输入应返回空字符串
        Assertions.assertEquals("", StringKit.sanitizePhrase("12345"))
        Assertions.assertEquals("", StringKit.sanitizePhrase("Hello"))
        Assertions.assertEquals("", StringKit.sanitizePhrase("!@#$%"))
    }
    
    @Test
    fun `validateCharacterString should handle null and empty input`() {
        // null和空字符串应该返回false
        Assertions.assertFalse(StringKit.validateCharacterString(null))
        Assertions.assertFalse(StringKit.validateCharacterString(""))
        Assertions.assertFalse(StringKit.validateCharacterString("   "))
    }
    
    @Test
    fun `validateCharacterString should validate string length`() {
        // 32位以内应返回true
        Assertions.assertTrue(StringKit.validateCharacterString("ABC123"))
        Assertions.assertTrue(StringKit.validateCharacterString("12345-abcde-FGHIJ-67890"))
        Assertions.assertTrue(StringKit.validateCharacterString("abcdefghijklmnopqrstuvwxyz123456")) // 恰好32位
        
        // 超过32位应返回false
        Assertions.assertFalse(StringKit.validateCharacterString("abcdefghijklmnopqrstuvwxyz1234567")) // 33位
    }
    
    @Test
    fun `validateCharacterString should detect control characters`() {
        // 包含控制字符应返回false
        Assertions.assertFalse(StringKit.validateCharacterString("ABC\n123"))
        Assertions.assertFalse(StringKit.validateCharacterString("DEF\rGHI"))
        Assertions.assertFalse(StringKit.validateCharacterString("XYZ\tABC"))
        Assertions.assertFalse(StringKit.validateCharacterString("\u0000ABC"))
    }
    
    @Test
    fun `validateCharacterString should handle special characters`() {
        // 可以包含ASCII范围内的特殊字符
        Assertions.assertTrue(StringKit.validateCharacterString("ABC-123"))
        Assertions.assertTrue(StringKit.validateCharacterString("<EMAIL>"))
        Assertions.assertTrue(StringKit.validateCharacterString("Hello, World!"))
        Assertions.assertTrue(StringKit.validateCharacterString("!@#$%^&*()_+-=[]{}|;':\",./<>?"))
    }
    
    @Test
    fun `validateCharacterString should reject non-ASCII characters`() {
        // 不能包含汉字
        Assertions.assertFalse(StringKit.validateCharacterString("你好123"))
        Assertions.assertFalse(StringKit.validateCharacterString("ABC中文"))
        
        // 不能包含扩展ASCII字符
        Assertions.assertFalse(StringKit.validateCharacterString("é®©"))
        
        // 不能包含表情符号
        Assertions.assertFalse(StringKit.validateCharacterString("😀😃😄"))
    }
    
    @Test
    fun `sanitizeCharacterString should handle null and empty input`() {
        // null和空字符串应返回空字符串
        Assertions.assertEquals("", StringKit.sanitizeCharacterString(null))
        Assertions.assertEquals("", StringKit.sanitizeCharacterString(""))
        Assertions.assertEquals("", StringKit.sanitizeCharacterString("   "))
    }
    
    @Test
    fun `sanitizeCharacterString should remove control characters`() {
        // 应移除控制字符
        Assertions.assertEquals("ABC123", StringKit.sanitizeCharacterString("ABC\n123"))
        Assertions.assertEquals("DEFGHI", StringKit.sanitizeCharacterString("DEF\rGHI"))
        Assertions.assertEquals("XYZABC", StringKit.sanitizeCharacterString("XYZ\tABC"))
        Assertions.assertEquals("ABC", StringKit.sanitizeCharacterString("\u0000ABC"))
        
        // 混合多种控制字符
        Assertions.assertEquals("ABC123", StringKit.sanitizeCharacterString("ABC\n\r\t\u0000123"))
    }
    
    @Test
    fun `sanitizeCharacterString should remove non-ASCII characters`() {
        // 应移除汉字，只保留ASCII字符
        Assertions.assertEquals("ABC123", StringKit.sanitizeCharacterString("你好ABC123"))
        Assertions.assertEquals("", StringKit.sanitizeCharacterString("你好"))
        
        // 应移除扩展ASCII字符
        Assertions.assertEquals("ABC", StringKit.sanitizeCharacterString("é®©ABC"))
        
        // 应移除表情符号
        Assertions.assertEquals("Hello", StringKit.sanitizeCharacterString("Hello😀😃"))
        
        // 混合情况
        Assertions.assertEquals("ABC123", StringKit.sanitizeCharacterString("你好ABC中文123测试"))
    }
    
    @Test
    fun `sanitizeCharacterString should truncate long strings`() {
        // 应截断超过32位的字符串
        val longString = "abcdefghijklmnopqrstuvwxyz0123456789"
        Assertions.assertEquals("abcdefghijklmnopqrstuvwxyz012345", StringKit.sanitizeCharacterString(longString))
        
        // 恰好32位不应被截断
        val exactString = "abcdefghijklmnopqrstuvwxyz123456"
        Assertions.assertEquals(exactString, StringKit.sanitizeCharacterString(exactString))
        
        // 同时有非ASCII字符和超长的情况
        Assertions.assertEquals(
            "abcdefghijklmnopqrstuvwxyz012345",
            StringKit.sanitizeCharacterString("你好abcdefghijklmnopqrstuvwxyz0123456789")
        )
    }
    
    @Test
    fun `sanitizeCharacterString should keep all valid characters`() {
        // 应保留所有ASCII范围内的字符
        Assertions.assertEquals("ABC123!@#$%", StringKit.sanitizeCharacterString("ABC123!@#$%"))
        Assertions.assertEquals("<EMAIL>", StringKit.sanitizeCharacterString("<EMAIL>"))
        Assertions.assertEquals("Hello, World!", StringKit.sanitizeCharacterString("Hello, World!"))
        Assertions.assertEquals("0123456789", StringKit.sanitizeCharacterString("0123456789"))
    }
}