package org.mospital.common

import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlin.test.assertFalse

class PatientDataTokenTest {

    @Test
    fun `should create PatientDataToken with valid data`() {
        val dataId = "data123"
        val patientId = "patient456"
        
        val token = PatientDataToken(dataId, patientId)
        
        assertEquals(dataId, token.dataId)
        assertEquals(patientId, token.patientId)
    }

    @Test
    fun `should generate and parse token successfully`() {
        val originalToken = PatientDataToken("testData", "testPatient")
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals(originalToken.dataId, parsedToken.dataId)
        assertEquals(originalToken.patientId, parsedToken.patientId)
    }

    @Test
    fun `should handle empty strings correctly`() {
        val originalToken = PatientDataToken("", "")
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals("", parsedToken.dataId)
        assertEquals("", parsedToken.patientId)
    }

    @Test
    fun `should handle Chinese characters correctly`() {
        val originalToken = PatientDataToken("数据标识123", "患者编号456")
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals("数据标识123", parsedToken.dataId)
        assertEquals("患者编号456", parsedToken.patientId)
    }

    @Test
    fun `should handle special characters correctly`() {
        val originalToken = PatientDataToken("data@#$%^&*()", "patient!@#$%^&*()")
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals("data@#$%^&*()", parsedToken.dataId)
        assertEquals("patient!@#$%^&*()", parsedToken.patientId)
    }

    @Test
    fun `should handle long data correctly`() {
        val longDataId = "data" + "x".repeat(1000)
        val longPatientId = "patient" + "y".repeat(1000)
        val originalToken = PatientDataToken(longDataId, longPatientId)
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals(longDataId, parsedToken.dataId)
        assertEquals(longPatientId, parsedToken.patientId)
    }

    @Test
    fun `should handle UUID-like strings correctly`() {
        val originalToken = PatientDataToken(
            "550e8400-e29b-41d4-a716-************",
            "6ba7b810-9dad-11d1-80b4-00c04fd430c8"
        )
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals("550e8400-e29b-41d4-a716-************", parsedToken.dataId)
        assertEquals("6ba7b810-9dad-11d1-80b4-00c04fd430c8", parsedToken.patientId)
    }

    @Test
    fun `should handle numeric strings correctly`() {
        val originalToken = PatientDataToken("123456789", "987654321")
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals("123456789", parsedToken.dataId)
        assertEquals("987654321", parsedToken.patientId)
    }

    @Test
    fun `should handle whitespace and special formatting correctly`() {
        val originalToken = PatientDataToken("  data with spaces  ", "\tpatient\nwith\ttabs\r")
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals("  data with spaces  ", parsedToken.dataId)
        assertEquals("\tpatient\nwith\ttabs\r", parsedToken.patientId)
    }

    @Test
    fun `should generate URL safe tokens`() {
        val originalToken = PatientDataToken("testData123", "testPatient456")
        
        val tokenString = originalToken.generateToken()
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        // URL safe tokens should not contain +, /, or =
        assertFalse(tokenString.contains('+'))
        assertFalse(tokenString.contains('/'))
        assertFalse(tokenString.contains('='))
    }

    @Test
    fun `should return null when parsing invalid token`() {
        val invalidToken = "invalid-token-string"
        
        val parsedToken = PatientDataToken.parse(invalidToken)
        
        assertNull(parsedToken)
    }

    @Test
    fun `should return null when parsing empty token`() {
        val emptyToken = ""
        
        val parsedToken = PatientDataToken.parse(emptyToken)
        
        assertNull(parsedToken)
    }

    @Test
    fun `should return null when parsing corrupted token`() {
        // Generate a valid token first
        val originalToken = PatientDataToken("validData", "validPatient")
        val validToken = originalToken.generateToken()
        
        // Corrupt the token
        val corruptedToken = validToken.substring(0, validToken.length - 10) + "CORRUPTED!"
        
        val parsedToken = PatientDataToken.parse(corruptedToken)
        
        assertNull(parsedToken)
    }

    @Test
    fun `should return null when parsing random string`() {
        val randomToken = "randomStringThatIsNotAToken12345!@#$%"
        
        val parsedToken = PatientDataToken.parse(randomToken)
        
        assertNull(parsedToken)
    }

    @Test
    fun `should generate different tokens for different data`() {
        val token1 = PatientDataToken("data1", "patient1")
        val token2 = PatientDataToken("data2", "patient2")
        
        val tokenString1 = token1.generateToken()
        val tokenString2 = token2.generateToken()
        
        assertNotNull(tokenString1)
        assertNotNull(tokenString2)
        assertTrue(tokenString1 != tokenString2)
    }

    @Test
    fun `should generate same token for same data`() {
        val token1 = PatientDataToken("sameData", "samePatient")
        val token2 = PatientDataToken("sameData", "samePatient")
        
        val tokenString1 = token1.generateToken()
        val tokenString2 = token2.generateToken()
        
        assertNotNull(tokenString1)
        assertNotNull(tokenString2)
        assertEquals(tokenString1, tokenString2)
    }

    @Test
    fun `should maintain data integrity through multiple round trips`() {
        val originalToken = PatientDataToken("roundTripData", "roundTripPatient")
        
        // First round trip
        val tokenString1 = originalToken.generateToken()
        val parsedToken1 = PatientDataToken.parse(tokenString1)
        
        assertNotNull(parsedToken1)
        assertEquals(originalToken.dataId, parsedToken1.dataId)
        assertEquals(originalToken.patientId, parsedToken1.patientId)
        
        // Second round trip
        val tokenString2 = parsedToken1.generateToken()
        val parsedToken2 = PatientDataToken.parse(tokenString2)
        
        assertNotNull(parsedToken2)
        assertEquals(originalToken.dataId, parsedToken2.dataId)
        assertEquals(originalToken.patientId, parsedToken2.patientId)
        
        // Tokens should be identical
        assertEquals(tokenString1, tokenString2)
    }

    @Test
    fun `should properly implement data class equality`() {
        val token1 = PatientDataToken("equalData", "equalPatient")
        val token2 = PatientDataToken("equalData", "equalPatient")
        val token3 = PatientDataToken("differentData", "equalPatient")
        
        assertEquals(token1, token2)
        assertTrue(token1 != token3)
        assertEquals(token1.hashCode(), token2.hashCode())
    }

    @Test
    fun `should properly implement toString`() {
        val token = PatientDataToken("testData", "testPatient")
        
        val stringRepresentation = token.toString()
        
        assertNotNull(stringRepresentation)
        assertTrue(stringRepresentation.contains("testData"))
        assertTrue(stringRepresentation.contains("testPatient"))
        assertTrue(stringRepresentation.contains("PatientDataToken"))
    }

    @Test
    fun `should support destructuring`() {
        val token = PatientDataToken("destructureData", "destructurePatient")
        
        val (dataId, patientId) = token
        
        assertEquals("destructureData", dataId)
        assertEquals("destructurePatient", patientId)
    }

    @Test
    fun `should handle edge case with mixed character sets`() {
        val originalToken = PatientDataToken("Data数据123", "Patient患者456")
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals("Data数据123", parsedToken.dataId)
        assertEquals("Patient患者456", parsedToken.patientId)
    }

    @Test
    fun `should handle emoji characters correctly`() {
        val originalToken = PatientDataToken("data👤123", "patient🏥456")
        
        val tokenString = originalToken.generateToken()
        val parsedToken = PatientDataToken.parse(tokenString)
        
        assertNotNull(tokenString)
        assertTrue(tokenString.isNotEmpty())
        assertNotNull(parsedToken)
        assertEquals("data👤123", parsedToken.dataId)
        assertEquals("patient🏥456", parsedToken.patientId)
    }

} 