package org.mospital.common

import org.apache.commons.codec.binary.Base64
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class AESUtilTest {

    private val testKey = Base64.decodeBase64("0GPtRuUY2Jv/rqT48saltA==")
    
    @Test
    fun `should encrypt and decrypt simple text correctly`() {
        val originalText = "Hello, World!"
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        val decrypted = AESUtil.decryptAES(testKey, encrypted)
        
        assertNotNull(encrypted)
        assertTrue(encrypted.isNotEmpty())
        assertEquals(originalText, decrypted)
    }

    @Test
    fun `should encrypt and decrypt empty string correctly`() {
        val originalText = ""
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        val decrypted = AESUtil.decryptAES(testKey, encrypted)
        
        assertNotNull(encrypted)
        assertTrue(encrypted.isNotEmpty())
        assertEquals(originalText, decrypted)
    }

    @Test
    fun `should encrypt and decrypt Chinese characters correctly`() {
        val originalText = "你好，世界！这是一个测试。"
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        val decrypted = AESUtil.decryptAES(testKey, encrypted)
        
        assertNotNull(encrypted)
        assertTrue(encrypted.isNotEmpty())
        assertEquals(originalText, decrypted)
    }

    @Test
    fun `should encrypt and decrypt special characters correctly`() {
        val originalText = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~"
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        val decrypted = AESUtil.decryptAES(testKey, encrypted)
        
        assertNotNull(encrypted)
        assertTrue(encrypted.isNotEmpty())
        assertEquals(originalText, decrypted)
    }

    @Test
    fun `should encrypt and decrypt long text correctly`() {
        val originalText = "This is a very long text that contains multiple sentences and paragraphs. " +
                "It includes various types of characters, numbers 123456789, and symbols !@#$%^&*(). " +
                "The purpose is to test whether the AES encryption and decryption can handle large amounts of data correctly. " +
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        val decrypted = AESUtil.decryptAES(testKey, encrypted)
        
        assertNotNull(encrypted)
        assertTrue(encrypted.isNotEmpty())
        assertEquals(originalText, decrypted)
    }

    @Test
    fun `should encrypt and decrypt JSON data correctly`() {
        val originalText = """{"name":"张三","age":30,"city":"北京","email":"<EMAIL>","active":true}"""
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        val decrypted = AESUtil.decryptAES(testKey, encrypted)
        
        assertNotNull(encrypted)
        assertTrue(encrypted.isNotEmpty())
        assertEquals(originalText, decrypted)
    }

    @Test
    fun `should encrypt and decrypt newlines and tabs correctly`() {
        val originalText = "Line 1\nLine 2\tTabbed\rCarriage Return\n\nDouble newline"
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        val decrypted = AESUtil.decryptAES(testKey, encrypted)
        
        assertNotNull(encrypted)
        assertTrue(encrypted.isNotEmpty())
        assertEquals(originalText, decrypted)
    }

    @Test
    fun `should produce different encrypted output for same input with different keys`() {
        val originalText = "Test message"
        val key1 = Base64.decodeBase64("0GPtRuUY2Jv/rqT48saltA==")
        val key2 = Base64.decodeBase64("1GPtRuUY2Jv/rqT48saltB==")
        
        val encrypted1 = AESUtil.encryptAES(key1, originalText)
        val encrypted2 = AESUtil.encryptAES(key2, originalText)
        
        assertNotEquals(encrypted1, encrypted2)
    }

    @Test
    fun `should produce same encrypted output for same input and key`() {
        val originalText = "Test message"
        
        val encrypted1 = AESUtil.encryptAES(testKey, originalText)
        val encrypted2 = AESUtil.encryptAES(testKey, originalText)
        
        assertEquals(encrypted1, encrypted2)
    }

    @Test
    fun `should throw exception when decrypting with wrong key`() {
        val originalText = "Test message"
        val wrongKey = Base64.decodeBase64("1GPtRuUY2Jv/rqT48saltB==")
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        
        assertThrows<Exception> {
            AESUtil.decryptAES(wrongKey, encrypted)
        }
    }

    @Test
    fun `should throw exception when decrypting invalid base64 data`() {
        assertThrows<Exception> {
            AESUtil.decryptAES(testKey, "invalid-base64-data!")
        }
    }

    @Test
    fun `should encrypt and decrypt Unicode emoji correctly`() {
        val originalText = "Hello 👋 World 🌍! This is a test with emojis 😀🎉🚀"
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        val decrypted = AESUtil.decryptAES(testKey, encrypted)
        
        assertNotNull(encrypted)
        assertTrue(encrypted.isNotEmpty())
        assertEquals(originalText, decrypted)
    }

    @Test
    fun `encrypted result should be base64 encoded`() {
        val originalText = "Test message"
        
        val encrypted = AESUtil.encryptAES(testKey, originalText)
        
        // Should not throw exception when decoding as Base64
        val decodedBytes = Base64.decodeBase64(encrypted)
        assertTrue(decodedBytes.isNotEmpty())
    }

} 