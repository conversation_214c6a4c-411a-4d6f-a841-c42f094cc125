package org.mospital.common

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class PhoneUtilTest {

    @ParameterizedTest(name = "should validate {0} as a valid mobile number")
    @ValueSource(strings = [
        // 标准11位手机号
        "13812345678", "15987654321", "18123456789", "19876543210",
        // 各运营商号段
        "13012345678", "13112345678", "13212345678", "13412345678", "13512345678",
        "13612345678", "13712345678", "13812345678", "13912345678", "14512345678",
        "14712345678", "15012345678", "15112345678", "15212345678", "15312345678",
        "15512345678", "15612345678", "15712345678", "15812345678", "15912345678",
        "16212345678", "16612345678", "17012345678", "17112345678", "17212345678",
        "17312345678", "17512345678", "17612345678", "17712345678", "17812345678",
        "18012345678", "18112345678", "18212345678", "18312345678", "18412345678",
        "18512345678", "18612345678", "18712345678", "18812345678", "18912345678",
        "19012345678", "19112345678", "19812345678", "19912345678"
    ])
    fun `test valid mobile numbers`(number: String) {
        assertTrue(PhoneUtil.isMobile(number))
    }

    @Test
    fun `test valid mobile numbers with country code`() {
        // 带0前缀
        assertTrue(PhoneUtil.isMobile("013812345678"))
        assertTrue(PhoneUtil.isMobile("015987654321"))
        
        // 带86前缀
        assertTrue(PhoneUtil.isMobile("8613812345678"))
        assertTrue(PhoneUtil.isMobile("8615987654321"))
        
        // 带+86前缀
        assertTrue(PhoneUtil.isMobile("+8613812345678"))
        assertTrue(PhoneUtil.isMobile("+8615987654321"))
    }

    @Test
    fun `test invalid mobile numbers - wrong length`() {
        // 长度不足
        assertFalse(PhoneUtil.isMobile("1381234567"))
        assertFalse(PhoneUtil.isMobile("138123456"))
        assertFalse(PhoneUtil.isMobile("13812"))
        
        // 长度过长
        assertFalse(PhoneUtil.isMobile("138123456789"))
        assertFalse(PhoneUtil.isMobile("13812345678901"))
    }

    @Test
    fun `test invalid mobile numbers - wrong format`() {
        // 不以1开头
        assertFalse(PhoneUtil.isMobile("23812345678"))
        assertFalse(PhoneUtil.isMobile("03812345678"))
        assertFalse(PhoneUtil.isMobile("93812345678"))
        
        // 第二位不在3-9范围内
        assertFalse(PhoneUtil.isMobile("10812345678"))
        assertFalse(PhoneUtil.isMobile("11812345678"))
        assertFalse(PhoneUtil.isMobile("12812345678"))
        
        // 包含非数字字符
        assertFalse(PhoneUtil.isMobile("138123456a8"))
        assertFalse(PhoneUtil.isMobile("13812345-78"))
        assertFalse(PhoneUtil.isMobile("138 1234 5678"))
        assertFalse(PhoneUtil.isMobile("138.1234.5678"))
        
        // 包含特殊字符
        assertFalse(PhoneUtil.isMobile("13812345678#"))
        assertFalse(PhoneUtil.isMobile("*13812345678"))
        assertFalse(PhoneUtil.isMobile("13812345678*"))
    }

    @Test
    fun `test invalid mobile numbers - wrong country code format`() {
        // 错误的国家代码前缀
        assertFalse(PhoneUtil.isMobile("8513812345678"))
        assertFalse(PhoneUtil.isMobile("8713812345678"))
        assertFalse(PhoneUtil.isMobile("+8513812345678"))
        assertFalse(PhoneUtil.isMobile("+8713812345678"))
        
        // 多个前缀
        assertFalse(PhoneUtil.isMobile("0+8613812345678"))
        assertFalse(PhoneUtil.isMobile("86+8613812345678"))
        
        // 前缀格式错误
        assertFalse(PhoneUtil.isMobile("+ 8613812345678"))
        assertFalse(PhoneUtil.isMobile("86 13812345678"))
    }

    @Test
    fun `test edge cases`() {
        // 空字符串
        assertFalse(PhoneUtil.isMobile(""))
        
        // 空白字符
        assertFalse(PhoneUtil.isMobile(" "))
        assertFalse(PhoneUtil.isMobile("  "))
        assertFalse(PhoneUtil.isMobile("\t"))
        assertFalse(PhoneUtil.isMobile("\n"))
        
        // 前后有空格
        assertFalse(PhoneUtil.isMobile(" 13812345678"))
        assertFalse(PhoneUtil.isMobile("13812345678 "))
        assertFalse(PhoneUtil.isMobile(" 13812345678 "))
        
        // 只有数字但格式错误
        assertFalse(PhoneUtil.isMobile("00000000000"))
        assertFalse(PhoneUtil.isMobile("11111111111"))
        assertFalse(PhoneUtil.isMobile("12345678901"))
    }

    @Test
    fun `test boundary values`() {
        // 最小有效号段 (13开头)
        assertTrue(PhoneUtil.isMobile("13000000000"))
        assertTrue(PhoneUtil.isMobile("13999999999"))
        
        // 最大有效号段 (19开头)
        assertTrue(PhoneUtil.isMobile("19000000000"))
        assertTrue(PhoneUtil.isMobile("19999999999"))
        
        // 边界外的号段
        assertFalse(PhoneUtil.isMobile("12999999999"))
        assertFalse(PhoneUtil.isMobile("10000000000"))
    }

    @Test
    fun `test real world examples`() {
        // 一些真实世界中可能遇到的格式
        assertTrue(PhoneUtil.isMobile("13800138000"))
        assertTrue(PhoneUtil.isMobile("18612345678"))
        assertTrue(PhoneUtil.isMobile("+8613912345678"))
        
        // 常见的错误格式
        assertFalse(PhoneUtil.isMobile("************")) // 400电话
        assertFalse(PhoneUtil.isMobile("021-12345678")) // 固定电话
        assertFalse(PhoneUtil.isMobile("95588")) // 银行客服
        assertFalse(PhoneUtil.isMobile("10086")) // 运营商客服
    }
}
