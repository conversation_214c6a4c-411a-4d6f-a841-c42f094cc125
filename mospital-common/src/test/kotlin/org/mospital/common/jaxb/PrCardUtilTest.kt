package org.mospital.common.jaxb

import org.junit.jupiter.api.Test
import org.mospital.common.PrCardUtil
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class PrCardUtilTest {

    @Test
    fun test_isValidCard15() {
        assertTrue(PrCardUtil.isValidCard15("KAZ110090123105"))
        assertTrue(PrCardUtil.isValidCard15("PAK320181080312"))
    }

    @Test
    fun test_getBirthByPrCard15() {
        assertEquals(LocalDate.of(1990, 12, 31), PrCardUtil.getBirthByPrCard15("KAZ110090123105"))
        assertEquals(LocalDate.of(1981, 8, 3), PrCardUtil.getBirthByPrCard15("PAK320181080312"))
    }

}