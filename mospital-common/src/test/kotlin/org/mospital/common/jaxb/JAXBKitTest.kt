package org.mospital.common.jaxb

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

import jakarta.xml.bind.annotation.XmlRootElement

class JAXBKitTest {

    @Test
    fun `escapeSpecialChars should escape special characters in multiple XML tag contents`() {
        val xml = "<tag>content with & < > ' \"</tag><tag>another content with & < > ' \"</tag>"
        val expected = "<tag>content with &amp; &lt; &gt; &apos; &quot;</tag><tag>another content with &amp; &lt; &gt; &apos; &quot;</tag>"
        assertEquals(expected, JAXBKit.escapeSpecialChars(xml, "tag"))
    }

    @Test
    fun `escapeSpecialChars should handle multiple tags with empty content`() {
        val xml = "<tag></tag><tag></tag>"
        assertEquals(xml, JAXBKit.escapeSpecialChars(xml, "tag"))
    }

    @Test
    fun `escapeSpecialChars should handle xml with multiple tags without end tag`() {
        val xml = "<tag>content<tag>another content"
        assertEquals(xml, JAXBKit.escapeSpecialChars(xml, "tag"))
    }

    @Test
    fun `escapeSpecialChars should handle xml with multiple tags without start tag`() {
        val xml = "content</tag>another content</tag>"
        assertEquals(xml, JAXBKit.escapeSpecialChars(xml, "tag"))
    }

    @XmlRootElement
    data class Person(
        var name: String = "",
        var age: Int = 0
    )

    @Test
    fun testUnmarshal() {
        val xml = "<person><name>John</name><age>30</age></person>"
        val person = JAXBKit.unmarshal(Person::class.java, xml)
        assertEquals("John", person.name)
        assertEquals(30, person.age)
    }

    @Test
    fun testMarshal() {
        val person = Person("John", 30)
        val xml = JAXBKit.marshal(person)
        assertEquals("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><person><age>30</age><name>John</name></person>", xml)
    }
}
