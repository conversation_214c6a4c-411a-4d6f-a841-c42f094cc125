package org.mospital.common

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlin.test.assertFalse

// Test data classes defined outside test methods to avoid reflection issues
data class Address(val street: String, val city: String, val zipCode: String)
data class Person(val name: String, val age: Int, val address: Address, val hobbies: List<String>)
data class NullableData(val name: String?, val value: Int?)

class TokenManagerTest {

    @Test
    fun `should generate and parse PatientDataToken correctly`() {
        val originalData = PatientDataToken("data123", "patient456")
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<PatientDataToken>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertNotNull(parsedData)
        assertEquals(originalData.dataId, parsedData.dataId)
        assertEquals(originalData.patientId, parsedData.patientId)
    }

    @Test
    fun `should generate and parse simple string correctly`() {
        val originalData = "Hello, World!"
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<String>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertEquals(originalData, parsedData)
    }

    @Test
    fun `should generate and parse integer correctly`() {
        val originalData = 12345
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<Int>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertEquals(originalData, parsedData)
    }

    @Test
    fun `should generate and parse boolean correctly`() {
        val originalData = true
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<Boolean>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertEquals(originalData, parsedData)
    }

    @Test
    fun `should generate and parse list of strings correctly`() {
        val originalData = listOf("item1", "item2", "item3")
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<List<String>>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertNotNull(parsedData)
        assertEquals(originalData.size, parsedData.size)
        assertEquals(originalData, parsedData)
    }

    @Test
    fun `should generate and parse map correctly`() {
        val originalData = mapOf(
            "name" to "张三",
            "age" to "30",
            "city" to "北京"
        )
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<Map<String, String>>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertNotNull(parsedData)
        assertEquals(originalData.size, parsedData.size)
        assertEquals(originalData, parsedData)
    }

    @Test
    fun `should generate and parse complex nested data correctly`() {
        val originalData = Person(
            name = "李四",
            age = 25,
            address = Address("长安街1号", "北京", "100000"),
            hobbies = listOf("读书", "旅游", "编程")
        )
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<Person>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertNotNull(parsedData)
        assertEquals(originalData.name, parsedData.name)
        assertEquals(originalData.age, parsedData.age)
        assertEquals(originalData.address.street, parsedData.address.street)
        assertEquals(originalData.address.city, parsedData.address.city)
        assertEquals(originalData.address.zipCode, parsedData.address.zipCode)
        assertEquals(originalData.hobbies, parsedData.hobbies)
    }

    @Test
    fun `should generate and parse Chinese characters correctly`() {
        val originalData = "你好，世界！这是一个测试字符串，包含中文字符。"
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<String>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertEquals(originalData, parsedData)
    }

    @Test
    fun `should generate and parse special characters correctly`() {
        val originalData = """{"name":"测试","symbols":"!@#$%^&*()_+-=[]{}|;':\",./<>?`~"}"""
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<String>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertEquals(originalData, parsedData)
    }

    @Test
    fun `generated token should be URL safe`() {
        val originalData = PatientDataToken("data123", "patient456")
        
        val token = TokenManager.generateToken(originalData)
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        // URL safe tokens should not contain +, /, or =
        assertFalse(token.contains('+'))
        assertFalse(token.contains('/'))
        assertFalse(token.contains('='))
        // Should contain URL safe alternatives
        assertTrue(token.contains('-') || token.contains('_') || (!token.contains('-') && !token.contains('_')))
    }

    @Test
    fun `should handle empty string correctly`() {
        val originalData = ""
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<String>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertEquals(originalData, parsedData)
    }

    @Test
    fun `should handle null values in data correctly`() {
        val originalData = NullableData(null, null)
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<NullableData>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertNotNull(parsedData)
        // Jackson Kotlin module converts null String to empty string during serialization
        assertEquals("", parsedData.name)
        assertEquals(originalData.value, parsedData.value)
    }

    @Test
    fun `should return null when parsing invalid token`() {
        val invalidToken = "invalid-token-data"
        
        val parsedData = TokenManager.parseToken(invalidToken, jacksonTypeRef<PatientDataToken>())
        
        assertNull(parsedData)
    }

    @Test
    fun `should return null when parsing empty token`() {
        val emptyToken = ""
        
        val parsedData = TokenManager.parseToken(emptyToken, jacksonTypeRef<PatientDataToken>())
        
        assertNull(parsedData)
    }

    @Test
    fun `should return null when parsing corrupted token`() {
        // Generate a valid token first
        val originalData = PatientDataToken("data123", "patient456")
        val validToken = TokenManager.generateToken(originalData)
        
        // Corrupt the token by modifying it
        val corruptedToken = validToken.substring(0, validToken.length - 5) + "XXXXX"
        
        val parsedData = TokenManager.parseToken(corruptedToken, jacksonTypeRef<PatientDataToken>())
        
        assertNull(parsedData)
    }

    @Test
    fun `should handle round trip with PatientDataToken using convenience methods`() {
        val originalData = PatientDataToken("testData789", "testPatient123")
        
        // Using convenience methods from PatientDataToken
        val token = originalData.generateToken()
        val parsedData = PatientDataToken.parse(token)
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertNotNull(parsedData)
        assertEquals(originalData.dataId, parsedData.dataId)
        assertEquals(originalData.patientId, parsedData.patientId)
    }

    @Test
    fun `should return null when PatientDataToken parse invalid token`() {
        val invalidToken = "definitely-not-a-valid-token"
        
        val parsedData = PatientDataToken.parse(invalidToken)
        
        assertNull(parsedData)
    }

    @Test
    fun `should generate different tokens for different data`() {
        val data1 = PatientDataToken("data1", "patient1")
        val data2 = PatientDataToken("data2", "patient2")
        
        val token1 = TokenManager.generateToken(data1)
        val token2 = TokenManager.generateToken(data2)
        
        assertNotNull(token1)
        assertNotNull(token2)
        assertTrue(token1 != token2)
    }

    @Test
    fun `should generate same token for same data`() {
        val data = PatientDataToken("sameData", "samePatient")
        
        val token1 = TokenManager.generateToken(data)
        val token2 = TokenManager.generateToken(data)
        
        assertNotNull(token1)
        assertNotNull(token2)
        assertEquals(token1, token2)
    }

    @Test
    fun `should handle large data structures correctly`() {
        val largeList = (1..1000).map { "item$it" }
        
        val token = TokenManager.generateToken(largeList)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<List<String>>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertNotNull(parsedData)
        assertEquals(largeList.size, parsedData.size)
        assertEquals(largeList, parsedData)
    }

    @Test
    fun `should handle unicode emoji correctly`() {
        val originalData = "Hello 👋 World 🌍! Test with emojis 😀🎉🚀"
        
        val token = TokenManager.generateToken(originalData)
        val parsedData = TokenManager.parseToken(token, jacksonTypeRef<String>())
        
        assertNotNull(token)
        assertTrue(token.isNotEmpty())
        assertEquals(originalData, parsedData)
    }

} 