package org.mospital.common

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import java.time.LocalDate

/**
 * IdCardUtil 测试类
 * 测试中国居民身份证号码的校验、性别提取、出生日期提取功能
 */
class IdCardUtilTest {

    @Test
    fun testValidIdCards() {
        // 测试有效的身份证号码
        val validIdCards = listOf(
            "11010519491231002X", // 女性，1949年12月31日出生
            "******************"  // 男性，1880年1月1日出生（按标准文档示例）
        )
        
        validIdCards.forEach { idCard ->
            assertTrue(IdCardUtil.isValid(idCard), "身份证号码 $idCard 应该是有效的")
        }
    }

    @Test
    fun testInvalidIdCards() {
        // 测试无效的身份证号码
        val invalidIdCards = listOf(
            null,                    // null值
            "",                      // 空字符串
            "   ",                   // 空白字符串
            "12345678901234567",     // 17位数字
            "1234567890123456789",   // 19位数字
            "12345678901234567a",    // 包含非法字符
            "123456789012345678",    // 18位但校验码错误
            "110105194912310021",    // 校验码错误
            "110105999912310021"     // 无效日期
        )
        
        invalidIdCards.forEach { idCard ->
            assertFalse(IdCardUtil.isValid(idCard), "身份证号码 $idCard 应该是无效的")
        }
    }

    @Test
    fun testGenderExtraction() {
        // 测试性别提取
        assertEquals(Gender.FEMALE, IdCardUtil.extractGender("11010519491231002X"), "应该识别为女性")
        assertEquals(Gender.MALE, IdCardUtil.extractGender("******************"), "应该识别为男性")
        
        // 测试无效身份证
        assertNull(IdCardUtil.extractGender("invalid"), "无效身份证应返回null")
        assertNull(IdCardUtil.extractGender(null), "null值应返回null")
    }

    @Test
    fun testGenderDescription() {
        // 测试性别描述
        assertEquals("女", IdCardUtil.getGenderDescription("11010519491231002X"))
        assertEquals("男", IdCardUtil.getGenderDescription("******************"))
        assertNull(IdCardUtil.getGenderDescription("invalid"))
        assertNull(IdCardUtil.getGenderDescription(null))
    }

    @Test
    fun testBirthDateExtraction() {
        // 测试出生日期提取
        val expectedDate1 = LocalDate.of(1949, 12, 31)
        assertEquals(expectedDate1, IdCardUtil.extractBirthDate("11010519491231002X"))
        
        val expectedDate2 = LocalDate.of(1880, 1, 1)
        assertEquals(expectedDate2, IdCardUtil.extractBirthDate("******************"))
        
        // 测试无效身份证
        assertNull(IdCardUtil.extractBirthDate("invalid"), "无效身份证应返回null")
        assertNull(IdCardUtil.extractBirthDate(null), "null值应返回null")
    }

    @Test
    fun testAgeCalculation() {
        val currentDate = LocalDate.now()

        // 由于我们构造的身份证可能校验码不正确，我们使用已知有效的身份证进行测试
        // 使用1949年12月31日出生的身份证
        val age1949 = IdCardUtil.extractAge("11010519491231002X")
        assertNotNull(age1949, "应该能计算出年龄")
        
        // 计算预期年龄（1949年12月31日）
        val birthDate1949 = LocalDate.of(1949, 12, 31)
        var expectedAge = currentDate.year - birthDate1949.year
        if (currentDate.isBefore(birthDate1949.withYear(currentDate.year))) {
            expectedAge--
        }
        assertEquals(expectedAge, age1949, "年龄计算应该正确")
        
        // 测试无效身份证
        assertNull(IdCardUtil.extractAge("invalid"), "无效身份证应返回null")
        assertNull(IdCardUtil.extractAge(null), "null值应返回null")
    }

    @Test
    fun testIdCardFormatHandling() {
        // 测试身份证格式处理（大小写、空格等）
        val variations = listOf(
            "11010519491231002x",    // 小写x
            " 11010519491231002X ",  // 前后空格
            "11010519491231002 X"    // 中间空格（这应该是无效的）
        )
        
        assertTrue(IdCardUtil.isValid(variations[0]), "小写x应该被正确处理")
        assertTrue(IdCardUtil.isValid(variations[1]), "前后空格应该被正确处理")
        assertFalse(IdCardUtil.isValid(variations[2]), "中间空格应该被识别为无效")
    }

    @Test
    fun testAsciiDigitValidation() {
        // 测试非ASCII数字应该被拒绝
        val nonAsciiDigits = listOf(
            "１１０１０５１９４９１２３１００２Ｘ", // 全角数字
            "11010519491231００２X",         // 混合全角数字
            "1101051949123100２X",          // 单个全角数字
            "۱۱۰۱۰۵۱۹۴۹۱۲۳۱۰۰۲X"          // 阿拉伯-印度数字
        )
        
        nonAsciiDigits.forEach { idCard ->
            assertFalse(IdCardUtil.isValid(idCard), "包含非ASCII数字的身份证号码应该无效: $idCard")
            assertNull(IdCardUtil.extractGender(idCard), "包含非ASCII数字时性别提取应返回null: $idCard")
            assertNull(IdCardUtil.extractBirthDate(idCard), "包含非ASCII数字时出生日期提取应返回null: $idCard")
            assertNull(IdCardUtil.extractAge(idCard), "包含非ASCII数字时年龄提取应返回null: $idCard")
        }
        
        // 确保正常的ASCII数字仍然有效
        assertTrue(IdCardUtil.isValid("11010519491231002X"), "正常ASCII数字身份证应该有效")
    }

    @Test
    fun testBoundaryDates() {
        // 测试逻辑：出生日期应该在1800年1月1日之后，当前日期之前

        val futureDate = "11010520501231002X" // 2050年的日期
        assertFalse(IdCardUtil.isValid(futureDate), "未来日期应该无效")

        val pastDate = "340524179912310019"  // 1799年的日期
        assertFalse(IdCardUtil.isValid(pastDate), "1800年之前的日期应该无效")
    }
}
