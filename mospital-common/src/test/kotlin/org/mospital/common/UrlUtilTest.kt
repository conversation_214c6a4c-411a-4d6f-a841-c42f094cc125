package org.mospital.common

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.io.File
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.test.assertNotNull

class UrlUtilTest {

    @Test
    fun `should parse HTTP URL correctly`() {
        val url = UrlUtil.parseUrl("http://example.com/test")
        assertEquals("http", url.protocol)
        assertEquals("example.com", url.host)
        assertEquals("/test", url.path)
    }

    @Test
    fun `should parse HTTPS URL with query params`() {
        val url = UrlUtil.parseUrl("https://example.com/test?param=value")
        assertEquals("https", url.protocol)
        assertEquals("example.com", url.host)
        assertEquals("/test", url.path)
        assertEquals("param=value", url.query)
    }

    @Test
    fun `should parse File URL correctly`() {
        val testFile = File.createTempFile("test", ".txt")
        testFile.deleteOnExit()
        
        val url = UrlUtil.parseUrl("file://${testFile.absolutePath}")
        assertEquals("file", url.protocol)
        assertTrue(File(url.path).exists())
    }

    @Test
    fun `should parse Classpath URL correctly`() {
        val resourcePath = "test-resource.txt"
        javaClass.classLoader.getResourceAsStream(resourcePath).use { stream ->
            assertNotNull(stream, "Test resource file not found")
        }

        val url = UrlUtil.parseUrl("classpath://$resourcePath")
        // 不再假设具体的协议，因为可能是 file 或 jar
        assertTrue(url.protocol in setOf("file", "jar"), 
            "Protocol should be either 'file' or 'jar', but was '${url.protocol}'")
        assertTrue(url.toString().endsWith(resourcePath), 
            "URL should end with resource path")
    }

    @Test
    fun `should throw exception when classpath resource not found`() {
        assertThrows<IllegalArgumentException> {
            UrlUtil.parseUrl("classpath://non-existent-resource.txt")
        }
    }

    @Test
    fun `should throw exception for unsupported protocol`() {
        assertThrows<IllegalArgumentException> {
            UrlUtil.parseUrl("ftp://example.com")
        }
    }

    @Test
    fun `should throw exception for invalid URL format`() {
        assertThrows<IllegalArgumentException> {
            UrlUtil.parseUrl("invalid-url")
        }
    }
}
