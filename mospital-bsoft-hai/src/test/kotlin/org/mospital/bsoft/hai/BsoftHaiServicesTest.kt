package org.mospital.bsoft.hai

import org.junit.jupiter.api.Test
import org.mospital.jackson.JacksonKit
import java.time.LocalDate
import java.time.LocalDateTime

class BsoftHaiServicesTest {

    @Test
    fun testGetEmployeeInformation() {
        val response: EmployeeInformationResponse = BsoftHaiServices.getEmployeeInformation()
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.employees))
    }

    @Test
    fun testGetPatientInfoByErhcCardNo() {
        val response: PatientInfoResponse = BsoftHaiServices.getPatientInfo(
            form = PatientInfoForm(
                cardNo = "6568F49C31EF30894A7267C8F3EC4048C5A3506760C5F1773708E944C282BC04",
                cardType = PatientInfoForm.CARD_TYPE_JKK,
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testGetPatientInfoByJzCardNo() {
        val response: PatientInfoResponse = BsoftHaiServices.getPatientInfo(
            form = PatientInfoForm(
                cardNo = "********",
                cardType = PatientInfoForm.CARD_TYPE_JZK,
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response))
    }

    @Test
    fun testGetPatientInfoByIdCardNo() {
        val response: PatientInfoResponse = BsoftHaiServices.getPatientInfo(
            form = PatientInfoForm(
                patientIdCard = "G80315471",
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testGetPatientInfoByPatientId() {
        val response: PatientInfoResponse = BsoftHaiServices.getPatientInfo(
            form = PatientInfoForm(
                patientId = "2198692",
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testQueryBalanceRecords() {
        val response: BalanceRecordResponse = BsoftHaiServices.queryBalanceRecords(
            form = BalanceRecordForm(
                patientId = "1883811",
                startTime = LocalDateTime.parse("2023-07-01T00:00:00"),
                endTime = LocalDateTime.parse("2023-07-02T00:00:00"),
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testGetZhuyuanHistoryListByIdCardNo() {
        val response: ZhuyuanHistoryResponse = BsoftHaiServices.getZhuyuanHistoryListByIdCardNo(
            idCardNo = "650102197104041220",
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.zhuyuanHistoryList))
    }

    @Test
    fun testGetZhuyuanPatientInfo() {
        val response: ZhuyuanPatientInfoResponse = BsoftHaiServices.getZhuyuanPatientInfo(
            form = ZhuyuanPatientInfoForm(
                admissionNumber = "1268866",
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testGetZhuyuanFeeList() {
        val response: ZhuyuanFeeResponse = BsoftHaiServices.getZhuyuanFeeList(
            form = ZhuyuanFeeForm(
                admissionNo = "66974",
                startTime = LocalDateTime.parse("2023-04-17T00:00:00"),
                endTime = LocalDateTime.parse("2023-04-30T00:00:00"),
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testGetZhuyuanBillList() {
        val response: ZhuyuanBillResponse = BsoftHaiServices.getZhuyuanBillList(
            form = ZhuyuanBillForm(
                admissionNo = "208750"
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testGetReservationDeptList() {
        val response: ReservationDeptResponse = BsoftHaiServices.getReservationDeptList(
            form = ReservationDeptForm()
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testGetReservationDoctorList() {
        val response: ReservationDoctorResponse = BsoftHaiServices.getReservationDoctorList(
            form = ReservationDoctorForm(
                deptId = "4416",
                reserveTime = "2023-10-08",
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.reservationDoctorList))
    }

    @Test
    fun testGetReservationComingDoctorList() {
        val response: ReservationComingDoctorResponse = BsoftHaiServices.getReservationComingDoctorList(
            form = ReservationComingDoctorForm(
                doctorId = "334",
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.reservationComingDoctorList))
    }

    @Test
    fun testGetReservationTicketList() {
        val response: ReservationTicketResponse = BsoftHaiServices.getReservationTicketList(
            form = ReservationTicketForm(
                deptId = "334",
                doctorId = "",
                registerDate = "2023-10-08",
                dateType = 1,
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.reservationTicketList))
    }

    @Test
    fun testGetMaterialList() {
        val response: MaterialResponse = BsoftHaiServices.getMaterialList(
            form = MaterialForm(
                materialCode = ""
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.materialList))
    }

    @Test
    fun testGetDrugList() {
        val response: DrugResponse = BsoftHaiServices.getDrugList(
            form = DrugForm(
                code = ""
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.drugList))
    }

    @Test
    fun testGetMenzhenFeeList() {
        val response: MenzhenFeeResponse = BsoftHaiServices.getMenzhenFeeList(
            form = MenzhenFeeForm(
                patientId = "2086616"
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response))
    }

    @Test
    fun testGetBillDetailList() {
        val response: BillDetailResponse = BsoftHaiServices.getBillDetailList(
            form = BillDetailForm(
                startDate = "2023-10-07",
                endDate = "2023-10-08",
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.billDetailList))
    }

    @Test
    fun testQueryImageList() {
        val response: ImageQueryResponse = BsoftHaiServices.queryImageList(
            form = ImageQueryForm(
                idCardNo = "372524198109265660",
            )
        )
        assert(response.imageDataList.isNotEmpty())
    }

    @Test
    fun testGetCheckReports() {
        val response: CheckReportResponse = BsoftHaiServices.getCheckReports(
            form = CheckReportForm(
                patientId = "2086616",
            )
        )
        assert(response.data.total > 0)
    }

    @Test
    fun testGetElectronicBillList() {
        val response: ElectronicBillResponse = BsoftHaiServices.getElectronicBillList(
            form = ElectronicBillForm(
                patientIdCard = "650121197010253280",
                outpatientInpatient = 2,
            )
        )
        assert(response.isOk()) { response.message }
        println(JacksonKit.writeValueAsString(response.electronicBillList))
    }

    @Test
    fun testCreateAccountAndCard() {
        val response: AccountAndCardResponse = BsoftHaiServices.createAccountAndCard(
            form = AccountAndCardForm(
                patientIdCard = "650105201806290043",
                patientName = "何银璐",
                patientNation = "汉族",
                patientPhone = "***********",
                cardNo = "650105201806290043",
                careerCode = "31",
                debitAmount = "0",
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testCreateAccountAndCardByPassport() {
        val response: AccountAndCardResponse = BsoftHaiServices.createAccountAndCard(
            form = AccountAndCardForm(
                patientIdCard = "G75528730",
                patientIdCardType = "10",
                patientName = "李武",
                patientNation = "汉族",
                patientPhone = "***********",
                cardNo = "G75528730",
                patientSex = 1,
                patientBirthday = "1995-05-09",
                careerCode = "31",
                debitAmount = "0",
            )
        )
        assert(response.isOk()) { response.message }
    }

    @Test
    fun testGetCdrJianChaBaoGaoListForMenZhen() {
        val response = BsoftHaiServices.getCdrJianChaBaoGaoList(
            form = CdrJianChaBaoGaoListForm.menZhen(
                cardNo = "********",
                reportStartDate = LocalDate.of(2024, 9, 1),
                reportEndDate = LocalDate.of(2024, 10, 11)
            )
        )
        println(JacksonKit.writeValueAsString(response.normalize()))
    }

    @Test
    fun testGetCdrJianChaBaoGaoListForZhuYuan() {
        val response = BsoftHaiServices.getCdrJianChaBaoGaoList(
            form = CdrJianChaBaoGaoListForm.zhuYuan(
                admissionNumber = "1268866",
                reportStartDate = LocalDate.of(2024, 10, 9),
                reportEndDate = LocalDate.of(2024, 10, 11)
            )
        )
        println(JacksonKit.writeValueAsString(response.normalize()))
    }

    @Test
    fun testGetCdrJianChaBaoGaoDetail() {
        val response = BsoftHaiServices.getCdrJianChaBaoGaoDetail(
            form = CdrJianChaBaoGaoDetailForm(
                reportId = "MR241009146"
            )
        )
        println(JacksonKit.writeValueAsString(response.normalize()))
    }

    @Test
    fun testGetCdrJianYanBaoGaoListForMenZhen() {
        val response = BsoftHaiServices.getCdrJianYanBaoGaoList(
            form = CdrJianYanBaoGaoListForm.menZhen(
                cardNo = "********",
                reportStartDate = LocalDate.of(2024, 9, 1),
                reportEndDate = LocalDate.of(2024, 10, 1)
            )
        )
        println(JacksonKit.writeValueAsString(response.normalize()))
    }

    @Test
    fun testGetCdrJianYanBaoGaoListForZhuYuan() {
        val response = BsoftHaiServices.getCdrJianYanBaoGaoList(
            form = CdrJianYanBaoGaoListForm.zhuYuan(
                admissionNumber = "1268866",
                reportStartDate = LocalDate.of(2024, 10, 11),
                reportEndDate = LocalDate.of(2024, 10, 16)
            )
        )
        println(JacksonKit.writeValueAsString(response.normalize()))
    }

    @Test
    fun testGetCdrChangGuiJianYanBaoGaoDetail() {
        val response = BsoftHaiServices.getCdrChangGuiJianYanBaoGaoDetail(
            form = CdrChangGuiJianYanBaoGaoDetailForm(
                barcodeNo = "060306174200",

            )
        )
        println(JacksonKit.writeValueAsString(response.normalize()))
    }
}