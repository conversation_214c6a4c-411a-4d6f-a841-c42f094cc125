package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ImageQueryRequest() : Request(msgType = "ODS_zy_30", msgVersion = "3.1") {

    @field:XmlElement(name = "Visit")
    var form: ImageQueryForm = ImageQueryForm()

    constructor(form: ImageQueryForm) : this() {
        this.form = form
    }

}

class ImageQueryForm(
    @field:XmlElement(name = "IdCard")
    val idCardNo: String = ""
): Request.BaseForm()