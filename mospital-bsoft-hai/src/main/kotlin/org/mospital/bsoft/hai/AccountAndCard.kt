package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class AccountAndCardResponse : Response() {

    @field:XmlElement(name = "data")
    var accountAndCard: AccountAndCard = AccountAndCard()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class AccountAndCard {

    @field:XmlElement(name = "patientId")
    var patientId: String = ""

    @field:XmlElement(name = "patientName")
    var patientName: String = ""

    @field:XmlElement(name = "cardBlance")
    var cardBalance: String = ""

    @field:XmlElement(name = "cardNo")
    var cardNo: String = ""

}