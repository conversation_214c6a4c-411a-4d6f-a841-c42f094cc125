package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ElectronicBillResponse : Response() {

    @field:XmlElement(name = "data")
    var electronicBillList: MutableList<ElectronicBill> = mutableListOf()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ElectronicBill {

    @field:XmlElement(name = "serialNumber")
    var serialNumber: String = ""

    @field:XmlElement(name = "einvoiceCode")
    var einvoiceCode: String = ""

    @field:XmlElement(name = "einvoiceNumber")
    var einvoiceNumber: String = ""

    @field:XmlElement(name = "randomNumber")
    var randomNumber: String = ""

    @field:XmlElement(name = "printTimes")
    var printTimes: String = ""

    @field:XmlElement(name = "billDate")
    var billDate: String = ""

}