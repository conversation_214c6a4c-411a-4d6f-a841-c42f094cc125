package org.mospital.bsoft.hai

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenRechargeRequest(): Request(msgType = "ODS_zy_10") {

    @field:XmlElement(name = "Visit")
    var form: MenzhenRechargeForm = MenzhenRechargeForm()

    constructor(form: MenzhenRechargeForm) : this() {
        this.form = form
    }

}

class MenzhenRechargeForm(
    @field:XmlElement(name = "bankNo")
    val bankNo: String = "",

    @field:XmlElement(name = "batchNumber")
    val batchNumber: String = "",

    @field:XmlElement(name = "debitAmount")
    val debitAmount: BigDecimal = BigDecimal.ZERO,

    @field:XmlElement(name = "operationType")
    val operationType: String = "20",

    @field:XmlElement(name = "orderNumber")
    val orderNumber: String = "",

    @field:XmlElement(name = "patientCard")
    val patientCard: String = "",


    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    @field:XmlElement(name = "payType")
    val payType: String = "16",

    @field:XmlElement(name = "referenceNumber")
    val referenceNumber: String = "",

    @field:XmlElement(name = "serialBatchNumber")
    val serialBatchNumber: String = "",

    @field:XmlElement(name = "szmxId")
    val szmxId: String = "",

    @field:XmlElement(name = "szmxIdFlag")
    val szmxIdFlag: String = "",

    @field:XmlElement(name = "terminalNumber")
    val terminalNumber: String = "",
) : Request.BaseForm()