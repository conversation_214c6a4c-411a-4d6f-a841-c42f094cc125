package org.mospital.bsoft.hai

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class MaterialResponse : Response() {

    @field:XmlElement(name = "data")
    var materialList: MutableList<Material> = mutableListOf()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class Material {

    @field:XmlElement(name = "projectNumber")
    var id: String = ""

    @field:XmlElement(name = "projectName")
    var name: String = ""

    @field:XmlElement(name = "pinyinCode")
    val pinyinCode: String = ""

    @field:XmlElement(name = "projectType")
    var typeCode: String = ""

    @field:XmlElement(name = "projectConsolidation")
    var typeName: String = ""

    @field:XmlElement(name = "projectUnit")
    var unit: String = ""

    @field:XmlElement(name = "standarPrice")
    var price: BigDecimal = BigDecimal.ZERO
}