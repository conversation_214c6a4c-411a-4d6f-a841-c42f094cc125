package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ExamineDetailRequest() : Request(msgType = "ODS_zy_29", msgVersion = "3.1") {

    @field:XmlElement(name = "Visit")
    var form: ExamineDetailForm = ExamineDetailForm()

    constructor(form: ExamineDetailForm) : this() {
        this.form = form
    }

}

class ExamineDetailForm(
    @field:XmlElement(name = "report_id")
    val reportId: String = "",
) : Request.BaseForm()