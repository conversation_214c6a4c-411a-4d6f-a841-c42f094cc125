package org.mospital.bsoft.hai

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class DischargeInstructionResponse : Response() {

    @field:XmlElement(name = "data")
    var dischargeInstruction: DischargeInstruction = DischargeInstruction()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class DischargeInstruction {

    @field:XmlElement(name = "inHospitalRecordNumber")
    var inHospitalRecordNumber: String = ""

    @field:XmlElement(name = "inHospitalRecordCode")
    var inHospitalRecordCode: String = ""

    @field:XmlElement(name = "patientName")
    val patientName: String = ""

    @field:XmlElement(name = "inDate")
    var inDate: String = ""

    @field:XmlElement(name = "outDate")
    var outDate: String = ""

    @field:XmlElement(name = "patientAge")
    var patientAge: String = ""

    @field:XmlElement(name = "departmentName")
    var departmentName: String = ""

    @field:XmlElement(name = "detailsItemsdetailsItems")
    var dischargeInstructionDrugLis: MutableList<DischargeInstructionDrug> = mutableListOf()
}

@XmlRootElement(name = "detailsItemsdetailsItems")
@XmlAccessorType(XmlAccessType.FIELD)
class DischargeInstructionDrug {

    @field:XmlElement(name = "itemCode")
    var itemCode: String = ""

    @field:XmlElement(name = "itemName")
    var itemName: String = ""

    @field:XmlElement(name = "itemType")
    var itemType: String = ""

    @field:XmlElement(name = "drugDose")
    var drugDose: Int = 0

    @field:XmlElement(name = "doseUnit")
    var doseUnit: String = ""

    @field:XmlElement(name = "frequencyName")
    var frequencyName: String = ""

    @field:XmlElement(name = "usageName")
    var usageName: String = ""

    @field:XmlElement(name = "itemNumber")
    var itemNumber: String = ""

    @field:XmlElement(name = "recipeNumber")
    var recipeNumber: String = ""

    @field:XmlElement(name = "price")
    var price: BigDecimal = BigDecimal.ZERO

    @field:XmlElement(name = "fee")
    var fee: BigDecimal = BigDecimal.ZERO

    @field:XmlElement(name = "unit")
    var unit: String = ""

    @field:XmlElement(name = "specifications")
    var specifications: String = ""
}