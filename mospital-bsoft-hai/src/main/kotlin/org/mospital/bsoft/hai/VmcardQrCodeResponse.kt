package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class VmcardQrCodeResponse : Response() {

    @field:XmlElement(name = "data")
    var vmcardQrCode: VmcardQrCode = VmcardQrCode()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class VmcardQrCode {

    @field:XmlElement(name = "erhcCardNo")
    var erhcCardNo: String = ""

    @field:XmlElement(name = "patientId")
    var patientId: String = ""

    @field:XmlElement(name = "patientIdCard")
    var patientIdCard: String = ""

}