package org.mospital.bsoft.hai

import org.mospital.common.jaxb.JAXBKit
import jakarta.xml.bind.annotation.*

@XmlTransient
@XmlAccessorType(XmlAccessType.PROPERTY)
open class Request(msgType: String, msgVersion: String = "3.0") {

    @field:XmlElement(name = "MsgHeader")
    var header: Header = Header(msgType = msgType, msgVersion = msgVersion)

    open fun toXML(): String = JAXBKit.marshal(this)

    override fun toString(): String = toXML()

    @XmlRootElement(name = "MsgHeader")
    @XmlAccessorType(XmlAccessType.FIELD)
    data class Header(
        @field:XmlElement(name = "Sender")
        var sender: String = "YQTL",
        @field:XmlElement(name = "MsgType")
        var msgType: String = "",
        @field:XmlElement(name = "MsgVersion")
        var msgVersion: String = "3.0"
    )

    @XmlTransient
    @XmlAccessorType(XmlAccessType.PROPERTY)
    open class BaseForm() {
        @field:XmlElement(name = "hospitalId")
        open val hospitalId: String = BsoftHaiSetting.hisHospitalId

        @field:XmlElement(name = "hospitalName")
        open val hospitalName: String = ""

        @field:XmlElement(name = "operator")
        open val operator: String = ""

        @field:XmlElement(name = "userId")
        open val userId: String = BsoftHaiSetting.hisUserId
    }
}
