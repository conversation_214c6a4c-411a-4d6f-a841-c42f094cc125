package org.mospital.bsoft.hai
import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class DrugResponse : Response() {

    @field:XmlElement(name = "data")
    var drugList: MutableList<Drug> = mutableListOf()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class Drug {

    @field:XmlElement(name = "drugNumber")
    var id: String = ""

    @field:XmlElement(name = "drugName")
    var name: String = ""

    @field:XmlElement(name = "drugType")
    var type: String = ""

    @field:XmlElement(name = "drugSpecification")
    var specification: String = ""

    @field:XmlElement(name = "drugUnit")
    var unit: String = ""

    @field:XmlElement(name = "purchasePrice")
    var price: BigDecimal = BigDecimal.ZERO
}