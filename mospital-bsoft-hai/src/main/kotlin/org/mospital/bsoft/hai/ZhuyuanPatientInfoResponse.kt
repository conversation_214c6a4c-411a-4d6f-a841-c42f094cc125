package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanPatientInfoResponse: Response() {

    @field:XmlElement(name = "data")
    var zhuyuanPatientInfo: ZhuyuanPatientInfo = ZhuyuanPatientInfo()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanPatientInfo {

    /**
     * 住院号
     */
    @field:XmlElement(name = "admissionNo")
    var admissionNo: String = ""

    /**
     * 住院号码
     */
    @field:XmlElement(name = "admissionNumber")
    var admissionNumber: String = ""

    /**
     * 入院登记日期
     */
    @field:XmlElement(name = "admissionTime")
    var admissionTime: String = ""

    /**
     * 病案号码
     */
    @field:XmlElement(name = "archivesNumber")
    var archivesNumber: String = ""

    /**
     * 住院余额
     */
    @field:XmlElement(name = "balance")
    var balance: String = ""

    /**
     * 科室名称
     */
    @field:XmlElement(name = "departmentName")
    var departmentName: String = ""

    /**
     * 未结金额
     */
    @field:XmlElement(name = "depoAmount")
    var depoAmount: String = ""

    /**
     * 医院ID
     */
    @field:XmlElement(name = "hospitalId")
    var hospitalId: String = ""

    /**
     * 住院医生
     */
    @field:XmlElement(name = "inpatientDoctor")
    var inpatientDoctor: String = ""

    /**
     * 病人床号
     */
    @field:XmlElement(name = "patientBedNumber")
    var patientBedNumber: String = ""

    /**
     * 出生年月
     */
    @field:XmlElement(name = "patientBirthday")
    var patientBirthday: String = ""

    /**
     * 病人科室
     */
    @field:XmlElement(name = "patientDepartMent")
    var patientDepartMent: String = ""

    /**
     * 病人ID
     */
    @field:XmlElement(name = "patientId")
    var patientId: String = ""

    /**
     * 身份证号码
     */
    @field:XmlElement(name = "patientIdCard")
    var patientIdCard: String = ""

    /**
     * 病人姓名
     */
    @field:XmlElement(name = "patientName")
    var patientName: String = ""

    /**
     * 民族代码
     */
    @field:XmlElement(name = "patientNation")
    var patientNation: String = ""

    /**
     * 联系电话
     */
    @field:XmlElement(name = "patientPhone")
    var patientPhone: String = ""

    /**
     * 病人性别
     */
    @field:XmlElement(name = "patientSex")
    var patientSex: String = ""

    /**
     * 病人性质
     */
    @field:XmlElement(name = "patientType")
    var patientType: String = ""

    /**
     * 入院计费开始日期
     */
    @field:XmlElement(name = "startTime")
    var startTime: String = ""

    /**
     * 未结金额
     */
    @field:XmlElement(name = "totalFee")
    var totalFee: String = ""

}