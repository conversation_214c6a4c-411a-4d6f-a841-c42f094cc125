package org.mospital.bsoft.hai

import org.mospital.common.jaxb.LocalDateTimeJaxbAdapter
import java.time.LocalDateTime
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class BalanceRecordRequest() : Request(msgType = "ODS_zy_02") {
    @field:XmlElement(name = "Visit")
    var form: BalanceRecordForm = BalanceRecordForm()

    constructor(form: BalanceRecordForm) : this() {
        this.form = form
    }

}

class BalanceRecordForm(
    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    @field:XmlElement(name = "operationType")
    val operationType: String = OPERATION_TYPE_ALL,

    @field:XmlJavaTypeAdapter(LocalDateTimeJaxbAdapter::class)
    @field:XmlElement(name = "startTime")
    val startTime: LocalDateTime? = null,

    @field:XmlJavaTypeAdapter(LocalDateTimeJaxbAdapter::class)
    @field:XmlElement(name = "endTime")
    val endTime: LocalDateTime? = null
) : Request.BaseForm() {
    companion object {
        const val OPERATION_TYPE_RECHARGE = "1"
        const val OPERATION_TYPE_REFUND = "2"
        const val OPERATION_TYPE_ALL = ""
    }
}