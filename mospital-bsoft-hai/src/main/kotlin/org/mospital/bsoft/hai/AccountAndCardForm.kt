package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.dromara.hutool.core.data.IdcardUtil

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class AccountAndCardRequest() : Request(msgType = "ODS_zy_08") {

    @field:XmlElement(name = "Visit")
    lateinit var form: AccountAndCardForm

    constructor(form: AccountAndCardForm) : this() {
        this.form = form
    }

}

class AccountAndCardForm(
    @field:XmlElement(name = "patientIdCard")
    val patientIdCard: String = "",

    // 01:身份证, 10:护照（外国人）
    @field:XmlElement(name = "patientIdCardType")
    val patientIdCardType: String = "01",

    @field:XmlElement(name = "patientName")
    val patientName: String = "",

    @field:XmlElement(name = "patientNation")
    val patientNation: String = "",

    @field:XmlElement(name = "patientPhone")
    val patientPhone: String = "",

    @get:XmlElement(name = "patientBirthday")
    var patientBirthday: String = "",

    @get:XmlElement(name = "patientSex")
    var patientSex: Int = 9,

    @field:XmlElement(name = "cardNo")
    val cardNo: String = "",

    @field:XmlElement(name = "careerCode")
    val careerCode: String = "",

    @field:XmlElement(name = "debitAmount")
    val debitAmount: String = "",

    @field:XmlElement(name = "operationType")
    val operationType: String = "20",

    @field:XmlElement(name = "address")
    val address: String = "",

    @field:XmlElement(name = "bankNo")
    val bankNo: String = "",

    @field:XmlElement(name = "batchNumber")
    val batchNumber: String = "",

    @field:XmlElement(name = "cardPass")
    val cardPass: String = "",

    @field:XmlElement(name = "orderNumber")
    val orderNumber: String = "",

    //无身份证儿童需要填写出生日期
    @field:XmlElement(name = "childBirthDay")
    val childBirthDay: String = "",

    @field:XmlElement(name = "payType")
    val payType: Int = 0,

    @field:XmlElement(name = "referenceNumber")
    val referenceNumber: String = "",

    @field:XmlElement(name = "serialBatchNumber")
    val serialBatchNumber: String = "",

    @field:XmlElement(name = "serialNumber")
    val serialNumber: String = "",

    @field:XmlElement(name = "terminalNumber")
    val terminalNumber: String = "",

    //1:就诊卡;2:电子健康卡（虚拟卡）
    @field:XmlElement(name = "cardType")
    val cardType: String = "2",

    @field:XmlElement(name = "guarderIDCard")
    val guarderIDCard: String = "",

    @field:XmlElement(name = "guarderName")
    val guarderName: String = "",

    @field:XmlElement(name = "guarderPhoneNumber")
    val guarderPhoneNumber: String = "",
) : Request.BaseForm() {

    init {
        if (patientBirthday.isBlank()) {
            if (childBirthDay.isBlank()) {
                require(patientIdCardType == "01" && IdcardUtil.isValidCard(patientIdCard)) { "非外国人或儿童的，请填写有效的身份证号码" }
                patientBirthday = IdcardUtil.getBirthDate(patientIdCard).toDateStr()
            } else {
                patientBirthday = childBirthDay
            }
        }

        if (patientSex == 9) {
            patientSex =
                if (patientIdCard.isBlank()) 9
                else when (IdcardUtil.getGender(patientIdCard)) {
                    0 -> 2  // 女
                    1 -> 1  // 男
                    else -> 9   // 未知
                }
        }
    }

}
