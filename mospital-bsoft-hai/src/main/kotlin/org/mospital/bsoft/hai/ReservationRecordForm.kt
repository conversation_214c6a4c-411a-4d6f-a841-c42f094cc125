package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationRecordRequest() : Request(msgType = "ODS_zy_17") {

    @field:XmlElement(name = "Visit")
    var form: ReservationRecordForm = ReservationRecordForm()

    constructor(form: ReservationRecordForm) : this() {
        this.form = form
    }

}

class ReservationRecordForm(

    @field:XmlElement(name = "startTime")
    val startTime: String = "",

    @field:XmlElement(name = "endTime")
    val endTime: String = "",

    @field:XmlElement(name = "operationType")
    val operationType: String = "0",

    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    ) : Request.BaseForm()