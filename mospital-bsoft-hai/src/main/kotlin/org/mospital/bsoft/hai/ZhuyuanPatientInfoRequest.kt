package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanPatientInfoRequest() : Request(msgType = "ODS_zy_04") {

    @field:XmlElement(name = "Visit")
    var form: ZhuyuanPatientInfoForm = ZhuyuanPatientInfoForm()

    constructor(form: ZhuyuanPatientInfoForm) : this() {
        this.form = form
    }

}

class ZhuyuanPatientInfoForm(

    @field:XmlElement(name = "admissionNumber")
    val admissionNumber: String = "",

    @field:XmlElement(name = "patientIdCard")
    val patientIdCard: String = "",
) : Request.BaseForm()