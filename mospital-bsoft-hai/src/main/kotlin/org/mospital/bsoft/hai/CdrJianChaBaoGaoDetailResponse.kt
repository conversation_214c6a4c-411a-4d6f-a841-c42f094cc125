package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.jackson.DateTimeFormatters
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Suppress("unused")
@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CdrJianChaBaoGaoDetailResponse {

    @field:XmlElement(name = "MsgHeader")
    var header: CdrMsgHeader = CdrMsgHeader()

    @field:XmlElement(name = "ExamReport")
    var detail: CdrJianChaBaoGaoDetail = CdrJianChaBaoGaoDetail()

    fun normalize(): CdrJianChaBaoGaoDetailResponse {
        this.detail.normalize()
        return this
    }

    fun isOk(): Boolean = true.toString().equals(this.header.status, ignoreCase = true)
    val errorMessage: String
        get() = arrayOf(this.header.errCode, this.header.detail).filterNot { it.isEmpty() }.joinToString(separator = "-")

    @XmlAccessorType(XmlAccessType.FIELD)
    class CdrJianChaBaoGaoDetail {

        @field:XmlElement(name = "DCId")
        var dcid: String = ""

        @field:XmlElement(name = "ReportId")
        var reportId: String = ""

        @field:XmlElement(name = "AuthorOrganization")
        var authorOrganization: String = ""

        @field:XmlElement(name = "PatientType")
        var patientTypeCode: String = ""

        @field:XmlElement(name = "ClinicId")
        var clinicId: String = ""

        @field:XmlElement(name = "HospizationId")
        var hospizationId: String = ""

        @field:XmlElement(name = "Name")
        var name: String = ""

        @field:XmlElement(name = "Sex")
        var sexCode: String = ""

        @field:XmlElement(name = "Age")
        var age: String = ""

        @field:XmlElement(name = "DeptName")
        var deptName: String = ""

        @field:XmlElement(name = "WardName")
        var wardName: String = ""

        @field:XmlElement(name = "DiagnoseName")
        var diagnoseName: String = ""

        @field:XmlElement(name = "ExaminationItem")
        var examinationItem: String = ""

        @field:XmlElement(name = "RequestDoctor")
        var requestDoctor: String = ""

        @field:XmlElement(name = "RequestDateTime")
        var requestTime: String = ""

        @field:XmlElement(name = "ExaminationMethod")
        var examinationMethod: String = ""

        @field:XmlElement(name = "ScanDirection")
        var scanDirection: String = ""

        @field:XmlElement(name = "ExaminationDisplay")
        var examinationDisplay: String = ""

        @field:XmlElement(name = "ExaminationResult")
        var examinationResult: String = ""

        @field:XmlElement(name = "Suggestion")
        var suggestion: String = ""

        @field:XmlElement(name = "ExecuteDateTime")
        var executeTime: String = ""

        @field:XmlElement(name = "ReportDoctor")
        var reportDoctor: String = ""

        @field:XmlElement(name = "ReportDateTime")
        var reportDate: String = ""

        @field:XmlElement(name = "ReviewDoctor")
        var reviewDoctor: String = ""

        @field:XmlElement(name = "ReviewDateTime")
        var reviewTime: String = ""

        @field:XmlElement(name = "ReportStatus")
        var reportStatusCode: String = ""

        @field:XmlElement(name = "PrintFlag")
        var printStatusCode: String = ""

        @field:XmlElement(name = "ReportDept")
        var reportDept: String = ""

        @field:XmlElement(name = "Image")
        var image: Image = Image()

        val patientTypeName: String
            get() = CdrPatientType.fromCode(this.patientTypeCode).description

        val sexName: String
            get() = when (this.sexCode) {
                "1" -> "男"
                "2" -> "女"
                else -> ""
            }

        val printStatusName: String
            get() = when (this.printStatusCode) {
                "0" -> "未打印"
                "1" -> "已打印"
                else -> ""
            }

        val reportStatusName: String
            get() = when (this.reportStatusCode) {
                "1" -> "初步报告"
                "2" -> "确认报告"
                else -> ""
            }

        fun normalize(): CdrJianChaBaoGaoDetail {
            this.requestTime = try {
                LocalDateTime.parse(this.requestTime, DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss"))
                    .format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            } catch (e: Exception) {
                this.requestTime
            }

            this.executeTime = try {
                LocalDateTime.parse(this.executeTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"))
                    .format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            } catch (e: Exception) {
                this.executeTime
            }

            this.reportDate = try {
                LocalDate.parse(this.reportDate, DateTimeFormatters.PURE_DATE_FORMATTER)
                    .format(DateTimeFormatters.NORM_DATE_FORMATTER)
            } catch (e: Exception) {
                this.reportDate
            }

            this.reviewTime = try {
                LocalDateTime.parse(this.reviewTime, DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss"))
                    .format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            } catch (e: Exception) {
                this.reviewTime
            }

            return this
        }

        @XmlAccessorType(XmlAccessType.FIELD)
        class Image {
            @field:XmlElement(name = "ImageNo")
            var imageNo: String = ""

            @field:XmlElement(name = "DisplayOrder")
            var displayOrder: String = ""

            @field:XmlElement(name = "ImageLocalURL")
            var imageLocalURL: String = ""

            @field:XmlElement(name = "ImageFormat")
            var imageFormat: String = ""
        }
    }
}