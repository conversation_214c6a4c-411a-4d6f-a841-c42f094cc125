package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CdrJianChaBaoGaoDetailRequest() : Request(msgType = "CDR_0909", msgVersion = "3.0") {

    @field:XmlElement(name = "QueryCond")
    var form: CdrJianChaBaoGaoDetailForm = CdrJianChaBaoGaoDetailForm()

    constructor(form: CdrJianChaBaoGaoDetailForm) : this() {
        this.form = form
    }

}

/**
 * 检查报告详情的查询表单
 *
 * @property dcid 文档唯一号
 * @property reportId 报告单号
 * @property authorOrganization 就诊机构代码
 */
class CdrJianChaBaoGaoDetailForm(

    @field:XmlElement(name = "DCID")
    val dcid: String = "",

    @field:XmlElement(name = "ReportId")
    val reportId: String = "",

    @field:XmlElement(name = "AuthorOrganization")
    val authorOrganization: String = BsoftHaiSetting.authorOrganization,
)