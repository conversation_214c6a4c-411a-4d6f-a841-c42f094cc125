package org.mospital.bsoft.hai

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenRefundRequest(): Request(msgType = "ODS_zy_11", msgVersion = "3.1") {

    @field:XmlElement(name = "Visit")
    var form: MenzhenRefundForm = MenzhenRefundForm()

    constructor(form: MenzhenRefundForm) : this() {
        this.form = form
    }

}

class MenzhenRefundForm(
    @field:XmlElement(name = "businessSerialumber")
    val businessSerialumber: String = "",

    @field:XmlElement(name = "creditAmount")
    val creditAmount: BigDecimal = BigDecimal.ZERO,

    @field:XmlElement(name = "oldbOusinessSerialumber")
    val oldbOusinessSerialumber: String = "",

    @field:XmlElement(name = "operationType")
    val operationType: String = "20",

    @field:XmlElement(name = "orderNumber")
    val orderNumber: String = "",

    @field:XmlElement(name = "patientCard")
    val patientCard: String = "",

    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    @field:XmlElement(name = "payType")
    val payType: String = "16",

    @field:XmlElement(name = "recordNumber")
    val recordNumber: String = "",

    @field:XmlElement(name = "referenceNumber")
    val referenceNumber: String = "",
): Request.BaseForm()