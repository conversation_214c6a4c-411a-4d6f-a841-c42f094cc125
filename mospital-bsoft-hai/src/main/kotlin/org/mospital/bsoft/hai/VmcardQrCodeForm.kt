package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class VmcardQrCodeRequest() : Request(msgType = "ODS_zy_09") {

    @field:XmlElement(name = "Visit")
    var form: VmcardQrCodeForm = VmcardQrCodeForm()

    constructor(form: VmcardQrCodeForm) : this() {
        this.form = form
    }

}

class VmcardQrCodeForm(
    @field:XmlElement(name = "erhcCardNo")
    val erhcCardNo: String="",

    @field:XmlElement(name = "empi")
    val empi: String = "",

    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    @field:XmlElement(name = "cardNo")
    val cardNo: String = "",
): Request.BaseForm()