package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class UnpaidMedicalTechnologyResponse : Response() {

    @field:XmlElement(name = "data")
    var unpaidMedicalTechnologyList: MutableList<UnpaidMedicalTechnology> = mutableListOf()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class UnpaidMedicalTechnology {

    @field:XmlElement(name = "patientId")
    var patientId: String = ""

    @field:XmlElement(name = "patientName")
    var patientName: String = ""

    @field:XmlElement(name = "costName")
    val costName: String = ""

    @field:XmlElement(name = "costDate")
    var costDate: String = ""

    @field:XmlElement(name = "itemsType")
    var itemsType: String = ""

    @field:XmlElement(name = "depart")
    var depart: String = ""

    @field:XmlElement(name = "doctor")
    var doctor: String = ""

    @field:XmlElement(name = "prescriptionCode")
    var prescriptionCode: String = ""

    @field:XmlElement(name = "projectMoney")
    var projectMoney: String = ""
}
