package org.mospital.bsoft.hai

import org.mospital.jackson.ConfigLoader

object BsoftHaiSetting {

    private val config: BsoftHaiConfig by lazy {
        ConfigLoader.loadConfig("bsoft-hai.yaml", BsoftHaiConfig::class.java, BsoftHaiSetting::class.java)
    }

    /**
     * wsdl地址
     */
    val url: String get() = config.url

    val urid: String get() = config.urid
    val pwd: String get() = config.pwd

    val version: String get() = config.version

    val hisHospitalId: String get() = config.hisHospitalId
    val hisUserId: String get() = config.hisUserId
    val authorOrganization: String get() = config.authorOrganization

}