package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class MaterialRequest() : Request(msgType = "ODS_zy_19") {

    @field:XmlElement(name = "Visit")
    var form: MaterialForm = MaterialForm()

    constructor(form: MaterialForm) : this() {
        this.form = form
    }

}

class MaterialForm(
    @field:XmlElement(name = "materialCode")
    val materialCode: String = ""
): Request.BaseForm()