package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class PatientInfoRequest() : Request(msgType = "ODS_zy_01") {
    @field:XmlElement(name = "Visit")
    var form: PatientInfoForm = PatientInfoForm()

    constructor(form: PatientInfoForm) : this() {
        this.form = form
    }

}

class PatientInfoForm(
    @field:XmlElement(name = "cardNo")
    val cardNo: String = "",

    /**
     * 卡类型，1=就诊卡，2=健康卡
     */
    @field:XmlElement(name = "cardType")
    val cardType: String = "",

    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    @field:XmlElement(name = "patientIdCard")
    val patientIdCard: String = "",

    @field:XmlElement(name = "patientName")
    val patientName: String = "",

    @field:XmlElement(name = "qrCode")
    val qrCode: String = ""
) : Request.BaseForm() {
    companion object {
        const val CARD_TYPE_JZK = "1"
        const val CARD_TYPE_JKK = "2"
    }
}