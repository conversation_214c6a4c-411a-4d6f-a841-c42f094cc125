package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlTransient

@XmlTransient
@XmlAccessorType(XmlAccessType.FIELD)
open class Response {
    @field:XmlElement(name = "code")
    open var code: String = ""

    @field:XmlElement(name = "message")
    open var message: String = ""

    open fun isOk(): Boolean = code == "200"

}
