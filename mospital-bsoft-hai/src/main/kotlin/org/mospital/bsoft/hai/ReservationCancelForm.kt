package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationCancelRequest() : Request(msgType = "ODS_zy_18") {

    @field:XmlElement(name = "Visit")
    var form: ReservationCancelForm = ReservationCancelForm()

    constructor(form: ReservationCancelForm) : this() {
        this.form = form
    }

}

class ReservationCancelForm(

    @field:XmlElement(name = "reservationNumber")
    val reservationNumber: String = "",

    @field:XmlElement(name = "registerType")
    val registerType: Int = 0,

    @field:XmlElement(name = "patientId")
    val patientId: String = ""

) : Request.BaseForm()