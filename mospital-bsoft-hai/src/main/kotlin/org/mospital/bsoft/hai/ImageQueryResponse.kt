package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.*

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ImageQueryResponse {

    @field:XmlElementWrapper(name = "_li")
    @field:XmlElement(name = "t")
    var imageDataList: List<ImageData> = mutableListOf()

    @XmlAccessorType(XmlAccessType.FIELD)
    class ImageData {

        @field:XmlElement(name = "IMAGELOCALURL")
        var imageLocalUrl: String = ""

        @field:XmlElement(name = "IDCARD")
        var idCard: String = ""

        @field:XmlElement(name = "HEALTHRECORDID")
        var healthRecordId: String = ""

        @field:XmlElement(name = "DCID")
        var dcId: String = ""

        @field:XmlElement(name = "SEX")
        var sex: String = ""

        @field:XmlElement(name = "DATASTORAGEMODE")
        var dataStorageMode: String = ""

        @field:XmlElement(name = "DISPLAYORDER")
        var displayOrder: String = ""

        @field:XmlElement(name = "NAME")
        var name: String = ""

        @field:XmlElement(name = "REVIEWDATETIME")
        var reviewDateTime: String = ""

        @field:XmlElement(name = "DOCID")
        var docId: String = ""

        @field:XmlElement(name = "IMAGEFORMAT")
        var imageFormat: String = ""

        @field:XmlElement(name = "IMAGESIZE")
        var imageSize: String = ""

        @field:XmlElement(name = "IMAGENOTES")
        var imageNotes: String = ""

        @field:XmlElement(name = "IMAGENO")
        var imageNo: String = ""
    }
}
