package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class RechargeResponse : Response() {

    @field:XmlElement(name = "data")
    var recharge: Recharge = Recharge()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class Recharge {

    @field:XmlElement(name = "cardBlance")
    var cardBalance: String = ""

    @field:XmlElement(name = "orderNo")
    var orderNo: String = ""

    @field:XmlElement(name = "patientId")
    var patientId: String = ""

    @field:XmlElement(name = "patientName")
    var patientName: String = ""

    @field:XmlElement(name = "businessSerialNumber")
    var businessSerialNumber: String = ""

    @field:XmlElement(name = "hisNo")
    var hisNo: String = ""

}