package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class EmployeeInformationResponse : Response() {

    @field:XmlElement(name = "data")
    var employees: MutableList<Employee> = mutableListOf()
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class Employee {

    /**
     * 工号
     */
    @field:XmlElement(name = "userId")
    var userId: String = ""

    /**
     * 姓名
     */
    @field:XmlElement(name = "userName")
    var userName: String = ""

    /**
     * 科室编码
     */
    @field:XmlElement(name = "deptId")
    val deptId: String = ""

    /**
     * 科室名称
     */
    var deptName: String = ""

}