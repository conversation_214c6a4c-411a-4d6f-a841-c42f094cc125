package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReserveRequest() : Request(msgType = "ODS_zy_16") {

    @field:XmlElement(name = "Visit")
    var form: ReserveForm = ReserveForm()

    constructor(form: ReserveForm) : this() {
        this.form = form
    }

}

class ReserveForm(

    @field:XmlElement(name = "deptId")
    val deptId: String = "",

    @field:XmlElement(name = "doctorId")
    val doctorId: String = "",

    /**
     * 操作类型；0：预约，1：现场挂号
     */
    @field:XmlElement(name = "opetationType")
    val operationType: Int = 0,

    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    /**
     * 挂号类别：1=普通门诊，2=急诊门诊，3=专家门诊，4=专科门诊
     */
    @field:XmlElement(name = "registerType")
    val registerType: Int = 0,

    @field:XmlElement(name = "sourceDate")
    val date: String = "",

    @field:XmlElement(name = "timeInterval")
    val period: String = "",

    @field:XmlElement(name = "feeNo")
    val feeNo: String = "",

    @field:XmlElement(name = "registerFee")
    val registerFee: String = "",
) : Request.BaseForm()