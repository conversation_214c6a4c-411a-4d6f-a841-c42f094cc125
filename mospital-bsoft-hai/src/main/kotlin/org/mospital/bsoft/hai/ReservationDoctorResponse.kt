package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationDoctorResponse : Response() {
    @field:XmlElement(name = "data")
    var reservationDoctorList: List<ReservationDoctor> = mutableListOf()
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationDoctor {

    @field:XmlElement(name = "dateType")
    var dateType: String = ""

    @field:XmlElement(name = "doctorId")
    var doctorId: String = ""

    @field:XmlElement(name = "doctorName")
    var doctorName: String = ""

    @field:XmlElement(name = "registerFee")
    var registerFee: String = ""

    /**
     * 挂号类别：1=普通门诊，2=急诊门诊，3=专家门诊，4=专科门诊
     */
    @field:XmlElement(name = "registerType")
    var registerTypeCode: String = ""

    @field:XmlElement(name = "name")
    var name: String = ""

    @field:XmlElement(name = "feeNo")
    var feeNo: String = ""

    @field:XmlElement(name = "pedRatio")
    var pedRatio: String = ""

    @field:XmlElement(name = "introduction")
    var introduction: String = ""

    val ampm: AMPM
        get() = AMPM.fromCode(dateType.toInt())!!

    val registerType: RegisterType
        get() = RegisterType.fromCode(registerTypeCode.toInt())!!
}
