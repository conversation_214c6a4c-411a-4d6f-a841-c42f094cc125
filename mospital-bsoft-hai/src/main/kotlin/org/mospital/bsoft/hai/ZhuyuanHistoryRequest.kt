package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanHistoryRequest() : Request(msgType = "ODS_zy_03") {

    @field:XmlElement(name = "Visit")
    var form: ZhuyuanHistoryForm = ZhuyuanHistoryForm()

    constructor(form: ZhuyuanHistoryForm) : this() {
        this.form = form
    }

}

class ZhuyuanHistoryForm(

    @field:XmlElement(name = "patientIdCard")
    val patientIdCard: String="",

    @field:XmlElement(name = "admissionNumber")
    val admissionNumber: String = "",

    @field:XmlElement(name = "patientId")
    val patientId: String = "",
) : Request.BaseForm()
