package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.jackson.DateTimeFormatters
import java.time.LocalDate

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CdrJianChaBaoGaoListRequest() : Request(msgType = "CDR_0909", msgVersion = "3.0") {

    @field:XmlElement(name = "QueryCond")
    var form: CdrJianChaBaoGaoListForm = CdrJianChaBaoGaoListForm()

    constructor(form: CdrJianChaBaoGaoListForm) : this() {
        this.form = form
    }

}

/**
 * 检查报告列表的查询表单
 * 就诊机构代码、门（急）诊号、住院号，三者至少提供一项
 * isFindByDate=1，查询病人一段时间内所有报告
 * isFindByDate为空，查询单次就诊的报告
 *
 *
 * @property mpiId 患者主索引号
 * @property patientId 患者交叉索引号
 * @property visitId CDR就诊唯一号
 * @property authorOrganization 就诊机构代码
 * @property clinicId 门（急）诊号
 * @property hospizationId 住院号
 * @property sourceVisitId HIS就诊流水号
 * @property visitType 就诊类型
 * @property reportDate 报告日期，格式为yyyyMMdd
 * @property isFindByDate 是否按时间段查询
 * @property reportStartDate 报告开始日期，格式为yyyyMMdd
 * @property reportEndDate 报告截止日期，格式为yyyyMMdd
 */
class CdrJianChaBaoGaoListForm(

    @field:XmlElement(name = "MpiId")
    val mpiId: String = "",

    @field:XmlElement(name = "PatientId")
    val patientId: String = "",

    @field:XmlElement(name = "VisitId")
    val visitId: String = "",

    @field:XmlElement(name = "AuthorOrganization")
    val authorOrganization: String = BsoftHaiSetting.authorOrganization,

    @field:XmlElement(name = "ClinicId")
    val clinicId: String = "",

    @field:XmlElement(name = "HospizationId")
    val hospizationId: String = "",

    @field:XmlElement(name = "SourceVisitId")
    val sourceVisitId: String = "",

    @field:XmlElement(name = "VisitType")
    val visitType: String = "",

    @field:XmlElement(name = "ReportDate")
    val reportDate: String = "",

    @field:XmlElement(name = "IsFindByDate")
    val isFindByDate: String = "",

    @field:XmlElement(name = "ReportStartDate")
    val reportStartDate: String = "",

    @field:XmlElement(name = "ReportEndDate")
    val reportEndDate: String = "",
) {
    companion object {

        /**
         * Generates a CdrJianChaBaoGaoListForm object with provided parameters.
         *
         * @param cardNo 就诊卡号
         * @param reportStartDate 开始日期
         * @param reportEndDate 截止日期
         * @return A CdrJianChaBaoGaoListForm object initialized with the given parameters.
         */
        fun menZhen(
            cardNo: String,
            reportStartDate: LocalDate,
            reportEndDate: LocalDate
        ): CdrJianChaBaoGaoListForm {
            return CdrJianChaBaoGaoListForm(
                clinicId = cardNo,
                isFindByDate = "1",
                reportStartDate = reportStartDate.format(DateTimeFormatters.PURE_DATE_FORMATTER),
                reportEndDate = reportEndDate.format(DateTimeFormatters.PURE_DATE_FORMATTER),
            )
        }

        /**
         * Generates a CdrJianChaBaoGaoListForm instance for hospitalizations filtered by a specific date range.
         *
         * @param admissionNumber 住院号
         * @param reportStartDate 开始日期
         * @param reportEndDate 截止日期
         * @return A CdrJianChaBaoGaoListForm instance populated with the provided parameters.
         */
        fun zhuYuan(
            admissionNumber: String,
            reportStartDate: LocalDate,
            reportEndDate: LocalDate
        ): CdrJianChaBaoGaoListForm {
            return CdrJianChaBaoGaoListForm(
                hospizationId = admissionNumber,
                isFindByDate = "1",
                reportStartDate = reportStartDate.format(DateTimeFormatters.PURE_DATE_FORMATTER),
                reportEndDate = reportEndDate.format(DateTimeFormatters.PURE_DATE_FORMATTER),
            )
        }
    }
}