package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement

@XmlAccessorType(XmlAccessType.FIELD)
class CdrMsgHeader {
    @field:XmlElement(name = "Sender")
    var sender: String = ""

    @field:XmlElement(name = "MsgType")
    var msgType: String = ""

    @field:XmlElement(name = "MsgVersion")
    var msgVersion: String = ""

    @field:XmlElement(name = "Status")
    var status: String = ""

    @field:XmlElement(name = "ErrCode")
    var errCode: String = ""

    @field:XmlElement(name = "Detail")
    var detail: String = ""

    @field:XmlElement(name = "ReturnCount")
    var returnCount: String = ""
}