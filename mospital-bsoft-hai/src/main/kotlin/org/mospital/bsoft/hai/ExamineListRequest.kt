package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ExamineListRequest() : Request(msgType = "ODS_zy_28", msgVersion = "3.1") {

    @field:XmlElement(name = "Visit")
    var form: ExamineListForm = ExamineListForm()

    constructor(form: ExamineListForm) : this() {
        this.form = form
    }

}

class ExamineListForm(
    @field:XmlElement(name = "outpatient_id")
    val outPatientId: String = "",
) : Request.BaseForm()