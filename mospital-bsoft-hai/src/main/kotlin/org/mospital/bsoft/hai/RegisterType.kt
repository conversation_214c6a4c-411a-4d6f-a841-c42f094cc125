package org.mospital.bsoft.hai

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonFormat

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
enum class RegisterType(val code: Int, val desc: String) {

    GENERAL(1, "普通门诊"),
    EMERGENCY(2, "急诊门诊"),
    EXPERT(3, "专家门诊"),
    SPECIALIST(4, "专科门诊"),
    EXAMINATION(5, "体检");

    companion object {
        private val mapByCode = values().associateBy { it.code }

        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun fromCode(code: Int): RegisterType? = mapByCode[code]
    }
}