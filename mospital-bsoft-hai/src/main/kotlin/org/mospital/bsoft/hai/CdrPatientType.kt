package org.mospital.bsoft.hai

enum class CdrPatientType(
    val code: String,
    val description: String,
) {

    MEN_ZHEN("01", "门诊"),
    JI_ZHEN("02", "急诊"),
    TI_JIAN("03", "体检"),
    ZHU_YUAN("04", "住院"),
    XIN_SHENG_ER("05", "新生儿"),
    LIU_GUAN("06", "留观"),
    QI_TA("09", "其他");

    companion object {
        private val mapByCode = CdrPatientType.entries.associateBy { it.code }
        fun fromCode(code: String): CdrPatientType = mapByCode[code] ?: QI_TA
    }

}