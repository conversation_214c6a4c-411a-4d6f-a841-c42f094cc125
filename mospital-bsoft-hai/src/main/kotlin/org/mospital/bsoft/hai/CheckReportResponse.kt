package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CheckReportResponse : Response() {

    @field:XmlElement(name = "data")
    var data: Data = Data()

    @XmlAccessorType(XmlAccessType.FIELD)
    class Data {
        @field:XmlElement(name = "total")
        var total: Int = 0

        @field:XmlElement(name = "list")
        var list: List<Report> = mutableListOf()
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    class Report {
        @field:XmlElement(name = "departmentName")
        var departmentName: String = ""

        @field:XmlElement(name = "patientName")
        var patientName: String = ""

        @field:XmlElement(name = "checkOddNumbers")
        var checkOddNumbers: String = ""

        @field:XmlElement(name = "departmentCode")
        var departmentCode: String = ""

        @field:XmlElement(name = "hospitalName")
        var hospitalName: String = ""

        @field:XmlElement(name = "checkName")
        var checkName: String = ""

        @field:XmlElement(name = "hospitalCode")
        var hospitalCode: String = ""

        @field:XmlElement(name = "doctorName")
        var doctorName: String = ""

        @field:XmlElement(name = "checkTime")
        var checkTime: String = ""

        @field:XmlElement(name = "executeDepartmentCode")
        var executeDepartmentCode: String = ""

        @field:XmlElement(name = "doctorCode")
        var doctorCode: String = ""

        @field:XmlElement(name = "reportContents")
        var reportContents: ReportContents = ReportContents()

        @field:XmlElement(name = "executeDepartmentName")
        var executeDepartmentName: String = ""

        @field:XmlElement(name = "examTime")
        var examTime: String = ""

        @field:XmlElement(name = "checkId")
        var checkId: String = ""

        @field:XmlElement(name = "reportTime")
        var reportTime: String = ""
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    class ReportContents {
        @field:XmlElement(name = "itemName")
        var itemName: String = ""

        @field:XmlElement(name = "itemContent")
        var itemContent: String = ""
    }
}
