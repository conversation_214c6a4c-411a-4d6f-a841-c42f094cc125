package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationDeptResponse : Response() {
    @field:XmlElement(name = "data")
    var reservationDeptList: List<ReservationDept> = mutableListOf()
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationDept {

    @field:XmlElement(name = "dateType")
    var dateType: String = ""

    @field:XmlElement(name = "deptId")
    var deptId: String = ""

    @field:XmlElement(name = "deptName")
    var deptName: String = ""

    /**
     * 科室和号源标志：0=特殊科室（号源），1=门诊科室
     */
    @field:XmlElement(name = "deptOrDoctor")
    var deptOrDoctor: String = ""

    @field:XmlElement(name = "registerFee")
    var registerFee: String = ""

    /**
     * 挂号类别：1=普通门诊，2=急诊门诊，3=专家门诊，4=专科门诊
     */
    @field:XmlElement(name = "registerType")
    var registerType: String = ""

}