package org.mospital.bsoft.hai

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenFeeResponse : Response() {

    @field:XmlElement(name = "data")
    var menzhenFeeList: MutableList<MenzhenFee> = mutableListOf()

    fun toFeeGroups(): List<FeeGroup> {
        var map: MutableMap<FeeGroup, MutableList<FeeItem>> =
            menzhenFeeList.groupByTo(mutableMapOf(), { it.toFeeGroup() }, { it.toFeeItem() })
        return map.map {
            it.key.apply {
                feeItems.addAll(it.value)
            }
        }
    }
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenFee {

    /**
     * 费用类型名称
     */
    @field:XmlElement(name = "feeTypeName")
    var feeTypeName: String = ""

    /**
     * 费用名称
     */
    @field:XmlElement(name = "feeName")
    var feeName: String = ""

    /**
     * 费用单位
     */
    @field:XmlElement(name = "feeUnit")
    val feeUnit: String = ""

    /**
     * 费用规格
     */
    @field:XmlElement(name = "feeSpecifications")
    var feeSpecifications: String = ""

    /**
     * 费用单价
     */
    @field:XmlElement(name = "feePrice")
    var feePrice: String = ""

    /**
     * 费用数量
     */
    @field:XmlElement(name = "feeNumber")
    var feeNumber: String = ""

    /**
     * 总金额
     */
    @field:XmlElement(name = "totalMoney")
    var totalMoney: String = ""

    /**
     * 开单时间
     */
    @field:XmlElement(name = "billingDate")
    var billingDate: String = ""

    /**
     * 开单科室
     */
    @field:XmlElement(name = "prescriptionDept")
    var prescriptionDept: String = ""

    /**
     * 执行科室
     */
    @field:XmlElement(name = "implementDept")
    var implementDept: String = ""

    /**
     * 发票号
     */
    @field:XmlElement(name = "invoiceNumber")
    var invoiceNumber: String = ""

    fun toFeeGroup(): FeeGroup {
        return FeeGroup(
            invoiceNumber = invoiceNumber,
            prescriptionDept = prescriptionDept,
            implementDept = implementDept,
            billingDate = billingDate
        )
    }

    fun toFeeItem(): FeeItem {
        return FeeItem(
            feeTypeName = feeTypeName,
            feeName = feeName,
            feeUnit = feeUnit,
            feeSpecifications = feeSpecifications,
            feePrice = feePrice,
            feeNumber = feeNumber,
            totalMoney = BigDecimal(totalMoney),
        )
    }
}

data class FeeGroup(
    val invoiceNumber: String,
    val prescriptionDept: String,
    val implementDept: String,
    val billingDate: String,
    val feeItems: MutableList<FeeItem> = mutableListOf()
) {

    fun addFeeItem(feeItem: FeeItem) {
        feeItems.add(feeItem)
    }

    val totalMoney: BigDecimal
        get() = feeItems.sumOf { it.totalMoney }
}

data class FeeItem(
    val feeTypeName: String,
    val feeName: String,
    val feeUnit: String,
    val feeSpecifications: String,
    val feePrice: String,
    val feeNumber: String,
    val totalMoney: BigDecimal,
)