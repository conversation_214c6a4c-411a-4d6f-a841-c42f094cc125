package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CheckReportRequest() : Request(msgType = "ODS_zy_27", msgVersion = "3.1") {

    @field:XmlElement(name = "Visit")
    var form: CheckReportForm = CheckReportForm()

    constructor(form: CheckReportForm) : this() {
        this.form = form
    }

}

class CheckReportForm(
    @field:XmlElement(name = "cardNumber")
    val cardNumber: String = "",

    @field:XmlElement(name = "cardType")
    val cardType: String = "",

    @field:XmlElement(name = "patientId")
    val patientId: String = "",
) : Request.BaseForm()