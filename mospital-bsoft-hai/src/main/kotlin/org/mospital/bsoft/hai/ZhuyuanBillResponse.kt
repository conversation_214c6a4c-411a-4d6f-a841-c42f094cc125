package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanBillResponse : Response() {

    @field:XmlElement(name = "data")
    var zhuyuanBillList: List<ZhuyuanBill> = mutableListOf()

    override fun isOk(): Boolean {
        return super.isOk() || message.contains("未查询到")
    }
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanBill {

    /**
     * 住院号
     */
    @field:XmlElement(name = "admissionNo")
    var admissionNo: String = ""

    /**
     * 住院号码
     */
    @field:XmlElement(name = "admissionNumber")
    var admissionNumber: String = ""

    /**
     * 支票号码
     */
    @field:XmlElement(name = "checkNumber")
    var checkNumber: String = ""

    @field:XmlElement(name = "hospitalId")
    var hospitalId: String = ""

    /**
     * 住院类别
     */
    @field:XmlElement(name = "inpatientType")
    var inpatientType: String = ""

    /**
     * 作废判别: 0:正常，1：作废
     */
    @field:XmlElement(name = "invalidStatu")
    var invalidStatus: String = ""

    /**
     * 操作病区
     */
    @field:XmlElement(name = "operationArea")
    var operationArea: String = ""

    /**
     * 操作类型
     */
    @field:XmlElement(name = "operationType")
    var operationType: String = ""

    /**
     * 订单号
     */
    @field:XmlElement(name = "orderNo")
    var orderNo: String = ""

    /**
     * 病人床号
     */
    @field:XmlElement(name = "patientBedNumber")
    var patientBedNumber: String = ""

    /**
     * 病人科室
     */
    @field:XmlElement(name = "patientDepartment")
    var patientDepartment: String = ""

    /**
     * 病人病区
     */
    @field:XmlElement(name = "patientInpatientArea")
    var patientInpatientArea: String = ""

    /**
     * 病人姓名
     */
    @field:XmlElement(name = "patientName")
    var patientName: String = ""

    /**
     * 缴款金额
     */
    @field:XmlElement(name = "paymentMoney")
    var paymentMoney: String = ""

    /**
     * 缴款号码
     */
    @field:XmlElement(name = "paymentNo")
    var paymentNo: String = ""

    /**
     * 缴款时间
     */
    @field:XmlElement(name = "paymentTime")
    var paymentTime: String = ""

    /**
     * 缴款方式：6：现金；8：银行卡；21：微信；15：支付宝,18:医保支付
     */
    @field:XmlElement(name = "paymentType")
    var paymentType: String = ""

    /**
     * 收据号码
     */
    @field:XmlElement(name = "receiptNumber")
    var receiptNumber: String = ""

    /**
     * 退费关联订单号
     */
    @field:XmlElement(name = "refundcorrelationNo")
    var refundcorrelationNo: String = ""

    /**
     * 交易流水号
     */
    @field:XmlElement(name = "serialNo")
    var serialNo: String = ""

    /**
     * 结账日期
     */
    @field:XmlElement(name = "settleTime")
    var settleTime: String = ""

    /**
     * 结算次数
     */
    @field:XmlElement(name = "settleTimes")
    var settleTimes: String = ""

    /**
     * 汇总日期
     */
    @field:XmlElement(name = "summaryDate")
    var summaryDate: String = ""

    /**
     * 转存判别
     */
    @field:XmlElement(name = "transferStatu")
    var transferStatus: String = ""

    /**
     * 操作工号
     */
    @field:XmlElement(name = "workId")
    var userId: String = ""

}