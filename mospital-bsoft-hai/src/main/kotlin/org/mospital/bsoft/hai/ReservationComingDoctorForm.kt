package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationComingDoctorRequest() : Request(msgType = "ODS_zy_14") {

    @field:XmlElement(name = "Visit")
    var form: ReservationComingDoctorForm = ReservationComingDoctorForm()

    constructor(form: ReservationComingDoctorForm) : this() {
        this.form = form
    }

}

class ReservationComingDoctorForm(

    @field:XmlElement(name = "doctorId")
    val doctorId: String = ""

) : Request.BaseForm()
