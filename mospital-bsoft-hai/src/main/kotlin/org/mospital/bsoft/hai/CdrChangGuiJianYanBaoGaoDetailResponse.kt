package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.jackson.DateTimeFormatters
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Suppress("unused")
@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CdrChangGuiJianYanBaoGaoDetailResponse {

    @field:XmlElement(name = "MsgHeader")
    var header: CdrMsgHeader = CdrMsgHeader()

    fun isOk(): Boolean = true.toString().equals(this.header.status, ignoreCase = true)
    val errorMessage: String
        get() = arrayOf(this.header.errCode, this.header.detail).filterNot { it.isEmpty() }.joinToString(separator = "-")

    @field:XmlElement(name = "LabReport")
    var detail: CdrChangGuiJianYanBaoGaoDetail = CdrChangGuiJianYanBaoGaoDetail()

    fun normalize(): CdrChangGuiJianYanBaoGaoDetailResponse {
        this.detail.normalize()
        return this
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    class CdrChangGuiJianYanBaoGaoDetail {

        @field:XmlElement(name = "PatientType")
        var patientTypeCode: String = ""

        @field:XmlElement(name = "DCId")
        var dcid: String = ""

        @field:XmlElement(name = "AuthorOrganization")
        var authorOrganization: String = ""

        @field:XmlElement(name = "LabOrdersItemName")
        var labOrdersItemName: String = ""

        @field:XmlElement(name = "ReportDateTime")
        var reportDate: String = ""

        @field:XmlElement(name = "Sex")
        var sexCode: String = ""

        @field:XmlElement(name = "SampleExecuteTime")
        var sampleExecuteTime: String = ""

        @field:XmlElement(name = "PrintFlag")
        var printStatusCode: String = ""

        @field:XmlElement(name = "SampleTypeName")
        var sampleTypeName: String = ""

        @field:XmlElement(name = "RequestDoctor")
        var requestDoctor: String = ""

        @field:XmlElement(name = "Name")
        var name: String = ""

        @field:XmlElement(name = "ReportDoctor")
        var reportDoctor: String = ""

        @field:XmlElement(name = "HospizationId")
        var hospizationId: String = ""

        @field:XmlElement(name = "ReportReviewTime")
        var reportReviewTime: String = ""

        @field:XmlElement(name = "SampleReceiveTime")
        var sampleReceiveTime: String = ""

        @field:XmlElement(name = "ReportReviewDoctor")
        var reportReviewDoctor: String = ""

        @field:XmlElement(name = "DeptName")
        var deptName: String = ""

        @field:XmlElement(name = "ReportStatus")
        var reportStatusCode: String = ""

        @field:XmlElement(name = "Age")
        var age: String = ""

        @field:XmlElement(name = "ReportType")
        var reportType: String = ""

        @field:XmlElement(name = "DiagnoseName")
        var diagnoseName: String = ""

        @field:XmlElement(name = "RequestTime")
        var requestTime: String = ""

        @field:XmlElement(name = "SampleNo")
        var sampleNo: String = ""

        @field:XmlElement(name = "ItemResult")
        var itemResults: MutableList<ItemResult> = mutableListOf()

        @field:XmlElement(name = "BarcodeNo")
        var barcodeNo: String = ""

        @field:XmlElement(name = "WardName")
        var wardName: String = ""

        @field:XmlElement(name = "ClinicId")
        var clinicId: String = ""

        val patientTypeName: String
            get() = CdrPatientType.fromCode(this.patientTypeCode).description

        val sexName: String
            get() = when (this.sexCode) {
                "1" -> "男"
                "2" -> "女"
                else -> ""
            }

        val printStatusName: String
            get() = when (this.printStatusCode) {
                "0" -> "未打印"
                "1" -> "已打印"
                else -> ""
            }

        val reportStatusName: String
            get() = when (this.reportStatusCode) {
                "1" -> "初步报告"
                "2" -> "确认报告"
                else -> ""
            }

        fun normalize(): CdrChangGuiJianYanBaoGaoDetail {
            val datetimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss")
            this.reportDate = try {
                LocalDateTime.parse(this.reportDate, datetimeFormatter).format(DateTimeFormatters.NORM_DATE_FORMATTER)
            } catch (e: Exception) {
                this.reportDate
            }

            this.sampleExecuteTime = try {
                LocalDateTime.parse(this.sampleExecuteTime, datetimeFormatter).format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            } catch (e: Exception) {
                this.sampleExecuteTime
            }

            this.reportReviewTime = try {
                LocalDateTime.parse(this.reportReviewTime, datetimeFormatter).format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            } catch (e: Exception) {
                this.reportReviewTime
            }

            this.sampleReceiveTime = try {
                LocalDateTime.parse(this.sampleReceiveTime, datetimeFormatter).format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            } catch (e: Exception) {
                this.sampleReceiveTime
            }

            this.requestTime = try {
                LocalDateTime.parse(this.requestTime, datetimeFormatter).format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            } catch (e: Exception) {
                this.requestTime
            }

            return this
        }

        @XmlAccessorType(XmlAccessType.FIELD)
        class ItemResult {
            @field:XmlElement(name = "TestId")
            var testId: String = ""

            @field:XmlElement(name = "TestName")
            var testName: String = ""

            @field:XmlElement(name = "TestResult")
            var testResult: String = ""

            @field:XmlElement(name = "ResultUnit")
            var resultUnit: String = ""

            @field:XmlElement(name = "ResultQualition")
            var resultQualification: String = ""

            @field:XmlElement(name = "ResultMessage")
            var resultMessage: String = ""

            @field:XmlElement(name = "ReferenceLow")
            var referenceLow: String = ""

            @field:XmlElement(name = "ReferenceHeight")
            var referenceHeight: String = ""

            @field:XmlElement(name = "ReferenceRange")
            var referenceRange: String = ""

            @field:XmlElement(name = "ResultCode")
            var resultCode: String = ""

            val resultCodeDescription: String
                get() = when (resultCode) {
                    "1" -> "正常"
                    "2" -> "异常"
                    "3" -> "不确定"
                    else -> ""
                }
        }
    }
}