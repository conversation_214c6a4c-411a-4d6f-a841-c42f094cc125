package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class DischargeInstructionRequest() : Request(msgType = "ODS_zy_21") {

    @field:XmlElement(name = "Visit")
    var form: DischargeInstructionForm = DischargeInstructionForm()

    constructor(form: DischargeInstructionForm) : this() {
        this.form = form
    }

}

class DischargeInstructionForm(
    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    @field:XmlElement(name = "admissionNo")
    val admissionNo: String = "",
): Request.BaseForm()