package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanFeeResponse : Response() {

    @field:XmlElement(name = "data")
    var zhuyuanFeeList: List<ZhuyuanFee> = mutableListOf()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanFee {

    /**
     * 医生
     */
    @field:XmlElement(name = "doctor")
    var doctor: String = ""

    /**
     * 执行科室
     */
    @field:XmlElement(name = "executDept")
    var executDept: String = ""

    /**
     * 费用数量
     */
    @field:XmlElement(name = "expenseAccount")
    var expenseAccount: String = ""

    /**
     * 费用科室
     */
    @field:XmlElement(name = "expenseDept")
    var expenseDept: String = ""

    /**
     * 费用名称
     */
    @field:XmlElement(name = "expenseName")
    var expenseName: String = ""

    /**
     * 费用单价
     */
    @field:XmlElement(name = "expensePrice")
    var expensePrice: String = ""

    /**
     * 费用时间
     */
    @field:XmlElement(name = "expenseTime")
    var expenseTime: String = ""

    /**
     * 结账时间
     */
    @field:XmlElement(name = "settleTime")
    var settleTime: String = ""

    /**
     * 划价金额
     */
    @field:XmlElement(name = "totalMoney")
    var totalMoney: String = ""

}