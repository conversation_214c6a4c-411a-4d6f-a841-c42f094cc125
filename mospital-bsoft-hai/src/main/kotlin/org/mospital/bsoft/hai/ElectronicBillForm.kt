package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ElectronicBillRequest() : Request(msgType = "ODS_zy_26", msgVersion = "3.1") {

    @field:XmlElement(name = "Visit")
    var form: ElectronicBillForm = ElectronicBillForm()

    constructor(form: ElectronicBillForm) : this() {
        this.form = form
    }

}

class ElectronicBillForm(
    /**
     * 身份证号
     */
    @field:XmlElement(name = "patientIdCard")
    val patientIdCard: String = "",

    /**
     * 1=门诊，2=住院
     */
    @field:XmlElement(name = "outpatientInpatient")
    val outpatientInpatient: Int = 0,

    /**
     * 开始时间
     */
    @field:XmlElement(name = "startTime")
    val startTime: String = "",

    /**
     * 结束时间
     */
    @field:XmlElement(name = "endTime")
    val endTime: String = ""
) : Request.BaseForm()