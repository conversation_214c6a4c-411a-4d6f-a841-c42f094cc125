package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class BillDetailRequest() : Request(msgType = "ODS_zy_24") {
    @field:XmlElement(name = "Visit")
    var form: BillDetailForm = BillDetailForm()

    constructor(form: BillDetailForm) : this() {
        this.form = form
    }

}

class BillDetailForm(
    /**
     * 交易类型：账户充值，账户退款，银医充值，银医退费，制卡扣费
     */
    @field:XmlElement(name = "bizType")
    val bizType: String = "",


    @field:XmlElement(name = "startDate")
    val startDate: String = "",

    @field:XmlElement(name = "endDate")
    val endDate: String = "",

    /**
     * 门诊住院标志：1-门诊，2-住院
     */
    @field:XmlElement(name = "outpatientSign")
    val outpatientSign: String = ""

) : Request.BaseForm()
