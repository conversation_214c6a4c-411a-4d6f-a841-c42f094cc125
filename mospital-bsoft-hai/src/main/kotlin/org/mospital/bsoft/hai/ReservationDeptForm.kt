package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationDeptRequest() : Request(msgType = "ODS_zy_12") {

    @field:XmlElement(name = "Visit")
    var form: ReservationDeptForm = ReservationDeptForm()

    constructor(form: ReservationDeptForm) : this() {
        this.form = form
    }

}

class ReservationDeptForm(

    @field:XmlElement(name = "deptName")
    val deptName: String="",

    @field:XmlElement(name = "reserveTime")
    val reserveTime: String = "",

    /**
     * sourceFlag 号源标志: 0=预约号源，1=现场号源
     */
    @field:XmlElement(name = "sourceFlag")
    val sourceFlag: Int = 0,
) : Request.BaseForm()