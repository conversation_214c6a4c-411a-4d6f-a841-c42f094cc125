package org.mospital.bsoft.hai

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanRechargeRequest() : Request(msgType = "ODS_zy_06") {

    @field:XmlElement(name = "Visit")
    var form: ZhuyuanRechargeForm = ZhuyuanRechargeForm()

    constructor(form: ZhuyuanRechargeForm) : this() {
        this.form = form
    }

}

/**
 * 门诊充值
 * @param debitAmount 充值金额
 * @param operationType 10=银医充值，20=掌医充值，30=微信公众号
 * @param orderNumber 订单号
 * @param payType 支付方式，1=现金，23=银行卡，16=微信，14=支付宝
 */
class ZhuyuanRechargeForm(
    @field:XmlElement(name = "admissionNo")
    val admissionNo: String = "",

    @field:XmlElement(name = "bankNo")
    val bankNo: String = "",

    @field:XmlElement(name = "batchNumber")
    val batchNumber: String = "",

    @field:XmlElement(name = "debitAmount")
    val debitAmount: BigDecimal = BigDecimal.ZERO,

    @field:XmlElement(name = "operationType")
    val operationType: String = "20",

    @field:XmlElement(name = "orderNumber")
    val orderNumber: String = "",

    @field:XmlElement(name = "payType")
    val payType: String = "16",

    @field:XmlElement(name = "referenceNumber")
    val referenceNumber: String = "",

    @field:XmlElement(name = "serialBatchNumber")
    val serialBatchNumber: String = "",

    @field:XmlElement(name = "serialNumber")
    val serialNumber: String = "",

    @field:XmlElement(name = "terminalNumber")
    val terminalNumber: String = "",

    ) : Request.BaseForm()