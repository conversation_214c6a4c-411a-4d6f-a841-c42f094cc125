package org.mospital.bsoft.hai

import org.mospital.common.PatientDataToken
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.jackson.DateTimeFormatters
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Suppress("unused")
@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CdrJianYanBaoGaoListResponse {

    @field:XmlElement(name = "MsgHeader")
    var header: CdrMsgHeader = CdrMsgHeader()

    @field:XmlElement(name = "LabReportList")
    var list: MutableList<CdrJianYanBaoGaoRecord> = mutableListOf()

    fun normalize(): CdrJianYanBaoGaoListResponse {
        this.list.forEach { it.normalize() }
        return this
    }

    fun isOk(): Boolean = true.toString().equals(this.header.status, ignoreCase = true)
    val errorMessage: String
        get() = arrayOf(this.header.errCode, this.header.detail).filterNot { it.isEmpty() }
            .joinToString(separator = "-")

    @XmlAccessorType(XmlAccessType.FIELD)
    class CdrJianYanBaoGaoRecord {

        @field:XmlElement(name = "PatientType")
        var patientTypeCode: String = ""

        @field:XmlElement(name = "AuthorOrganization")
        var authorOrganization: String = ""

        @field:XmlElement(name = "LabOrdersItemName")
        var labOrdersItemName: String = ""

        @field:XmlElement(name = "DCId")
        var dcid: String = ""

        @field:XmlElement(name = "ReportDateTime")
        var reportDate: String = ""

        @field:XmlElement(name = "Sex")
        var sexCode: String = ""

        @field:XmlElement(name = "PrintFlag")
        var printStatusCode: String = ""

        @field:XmlElement(name = "VisitId")
        var visitId: String = ""

        @field:XmlElement(name = "LabOrderItemName")
        var labOrderItemName: String = ""

        @field:XmlElement(name = "Name")
        var name: String = ""

        @field:XmlElement(name = "MpiId")
        var mpiId: String = ""

        @field:XmlElement(name = "SampleNo")
        var sampleNo: String = ""

        @field:XmlElement(name = "ReportTime")
        var reportTime: String = ""

        @field:XmlElement(name = "HospizationId")
        var hospizationId: String = ""

        @field:XmlElement(name = "ReportReviewTime")
        var reportReviewTime: String = ""

        @field:XmlElement(name = "BarcodeNo")
        var barcodeNo: String = ""

        @field:XmlElement(name = "PatientId")
        var patientId: String = ""

        @field:XmlElement(name = "ClinicId")
        var clinicId: String = ""

        @field:XmlElement(name = "DeptName")
        var deptName: String = ""

        @field:XmlElement(name = "Age")
        var age: String = ""

        @field:XmlElement(name = "ReportType")
        var reportTypeCode: String = ""

        @field:XmlElement(name = "SourceVisitId")
        var sourceVisitId: String = ""

        val patientTypeName: String
            get() = CdrPatientType.fromCode(this.patientTypeCode).description

        val sexName: String
            get() = when (this.sexCode) {
                "1" -> "男"
                "2" -> "女"
                else -> ""
            }

        val printStatusName: String
            get() = when (this.printStatusCode) {
                "0" -> "未打印"
                "1" -> "已打印"
                else -> ""
            }

        val reportTypeName: String
            get() = when (this.reportTypeCode) {
                "1" -> "常规报告"
                "5" -> "微生物报告"
                else -> ""
            }

        var token: String = ""

        fun normalize(): CdrJianYanBaoGaoRecord {
            this.reportDate = try {
                LocalDateTime.parse(this.reportDate, DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss"))
                    .format(DateTimeFormatters.NORM_DATE_FORMATTER)
            } catch (e: Exception) {
                this.reportDate
            }

            this.reportReviewTime = try {
                LocalDateTime.parse(this.reportReviewTime, DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss"))
                    .format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            } catch (e: Exception) {
                this.reportReviewTime
            }

            this.token = PatientDataToken(
                dataId = this.dcid,
                patientId = this.clinicId + "," + this.hospizationId
            ).generateToken()

            return this
        }
    }
}