package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.*

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ExamineDetailResponse {

    @field:XmlElementWrapper(name = "_li")
    @field:XmlElement(name = "t")
    var reports: List<Report> = mutableListOf()

    @XmlAccessorType(XmlAccessType.FIELD)
    class Report {
        @field:XmlElement(name = "unit")
        var unit: String = ""

        @field:XmlElement(name = "test_item_reference")
        var testItemReference: String = ""

        @field:XmlElement(name = "report_id")
        var reportId: String = ""

        @field:XmlElement(name = "item_name")
        var itemName: String = ""

        @field:XmlElement(name = "result_value")
        var resultValue: String = ""
    }
}
