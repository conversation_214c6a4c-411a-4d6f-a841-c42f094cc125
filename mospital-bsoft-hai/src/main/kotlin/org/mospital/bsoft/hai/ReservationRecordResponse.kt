package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationRecordResponse : Response() {
    @field:XmlElement(name = "data")
    var reservationRecordList: List<ReservationRecord> = mutableListOf()
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationRecord {

    @field:XmlElement(name = "dateType")
    var dateType: String = ""

    @field:XmlElement(name = "deptId")
    var deptId: String = ""

    @field:XmlElement(name = "deptName")
    var deptName: String = ""

    @field:XmlElement(name = "startTime")
    var startTime: String = ""

    @field:XmlElement(name = "endTime")
    var endTime: String = ""

    @field:XmlElement(name = "registerFlag")
    var registerFlag: String = ""

    @field:XmlElement(name = "registerType")
    var registerTypeCode: String = ""

    @field:XmlElement(name = "reservationNumber")
    var reservationNumber: String = ""

    @field:XmlElement(name = "reservationTime")
    var reservationTime: String = ""

    @field:XmlElement(name = "sourceId")
    var sourceId: String = ""

    val ampm: AMPM
        get() = AMPM.fromCode(dateType.toInt())!!

    val registerType: RegisterType
        get() = RegisterType.fromCode(registerTypeCode.toInt())!!
}