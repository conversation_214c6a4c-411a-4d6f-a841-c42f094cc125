package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class PatientInfoResponse: Response() {

    @field:XmlElement(name = "data")
    var patientInfo: PatientInfo = PatientInfo()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class PatientInfo {
    @field:XmlElement(name = "patientId")
    var patientId: String = ""

    @field:XmlElement(name = "patientIdCard")
    var patientIdCard: String=""

    @field:XmlElement(name = "cardNo")
    var cardNo: String = ""

    @field:XmlElement(name = "empi")
    var empi: String=""

    @field:XmlElement(name = "erhccardno")
    var erhcCardNo: String = ""

    @field:XmlElement(name = "patientName")
    var patientName: String = ""

    @field:XmlElement(name = "cardBalance")
    var cardBalance: String = ""

    @field:XmlElement(name = "outpatientNumber")
    var outpatientNumber: String = ""

    @field:XmlElement(name = "patientBirthday")
    var patientBirthday: String? = ""

    /**
     * 民族编码
     */
    @field:XmlElement(name = "patientNation")
    var patientNation: String? = ""

    @field:XmlElement(name = "patientPhone")
    var patientPhone: String? = ""

    /**
     * 性别，1=男
     */
    @field:XmlElement(name = "patientSex")
    var patientSex: String? = ""

    @field:XmlElement(name = "patientType")
    var patientType: String? = ""
}