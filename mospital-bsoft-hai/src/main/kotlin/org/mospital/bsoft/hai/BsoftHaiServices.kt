package org.mospital.bsoft.hai

import org.dromara.hutool.core.lang.Singleton
import org.dromara.hutool.http.client.engine.ClientEngine
import org.dromara.hutool.http.client.engine.ClientEngineFactory
import org.dromara.hutool.http.webservice.SoapClient
import org.mospital.common.IdUtil
import org.mospital.common.jaxb.JAXBKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Suppress("unused")
object BsoftHaiServices {

    private val log: Logger = LoggerFactory.getLogger(BsoftHaiServices::class.java)

    init {
        // 强制使用 OkHttp 引擎
        val okHttpEngine = ClientEngineFactory.createEngine("OkHttp")
        Singleton.put(ClientEngine::class.java.name, okHttpEngine)
    }

    private fun newClient(serviceName: String, xml: String): SoapClient =
        SoapClient.of(BsoftHaiSetting.url)
            .setMethod("ws:invoke", "http://ws.access.hai/")
            .setParam("service", serviceName, false)
            .setParam("urid", BsoftHaiSetting.urid, false)
            .setParam("pwd", BsoftHaiSetting.pwd, false)
            .setParam("parameter", if (xml.isBlank()) "" else "<![CDATA[$xml]]>", false)

    fun sendRequest(serviceName: String, xml: String): String {
        val traceId: String = IdUtil.simpleUUID()

        val soapClient = newClient(serviceName, xml)
        log.debug("Request#$traceId: ${soapClient.getMsgStr(false)}")

        val responseText: String = soapClient.send().bodyText
        log.debug("Response#$traceId: $responseText")

        return responseText
    }

    fun getEmployeeInformation(): EmployeeInformationResponse {
        val xml: String = sendRequest(serviceName = "getEmployeeInformation_ZY", xml = "")
        return JAXBKit.unmarshal(EmployeeInformationResponse::class.java, xml)
    }

    fun getPatientInfo(form: PatientInfoForm): PatientInfoResponse {
        val request = PatientInfoRequest(form = form)
        val xml: String = sendRequest(serviceName = "getPatientInfo_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(PatientInfoResponse::class.java, xml)
    }

    fun getPatientInfoByJzCardNo(jzCardNo: String): PatientInfoResponse = getPatientInfo(
        form = PatientInfoForm(
            cardNo = jzCardNo,
            cardType = PatientInfoForm.CARD_TYPE_JZK,
        )
    )

    fun getPatientInfoByErhcCardNo(erhcCardNo: String): PatientInfoResponse = getPatientInfo(
        form = PatientInfoForm(
            cardNo = erhcCardNo,
            cardType = PatientInfoForm.CARD_TYPE_JKK,
        )
    )

    fun getPatientInfoByIdCardNo(idCardNo: String): PatientInfoResponse = getPatientInfo(
        form = PatientInfoForm(
            patientIdCard = idCardNo,
        )
    )

    fun getPatientInfoByPatientId(patientId: String): PatientInfoResponse = getPatientInfo(
        form = PatientInfoForm(
            patientId = patientId,
        )
    )

    fun queryBalanceRecords(form: BalanceRecordForm): BalanceRecordResponse {
        val request = BalanceRecordRequest(form = form)
        val xml: String = sendRequest(serviceName = "queryBalanceRecord_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(BalanceRecordResponse::class.java, xml)
    }

    fun getZhuyuanHistoryList(form: ZhuyuanHistoryForm): ZhuyuanHistoryResponse {
        val request = ZhuyuanHistoryRequest(form = form)
        val xml: String = sendRequest(serviceName = "getInpatientHistory_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanHistoryResponse::class.java, xml)
    }

    fun getZhuyuanHistoryListByIdCardNo(idCardNo: String): ZhuyuanHistoryResponse {
        val form = ZhuyuanHistoryForm(patientIdCard = idCardNo)
        return getZhuyuanHistoryList(form = form)
    }

    fun getZhuyuanPatientInfo(form: ZhuyuanPatientInfoForm): ZhuyuanPatientInfoResponse {
        val request = ZhuyuanPatientInfoRequest(form = form)
        val xml: String = sendRequest(serviceName = "getInpatientInfo_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanPatientInfoResponse::class.java, xml)
    }

    fun getZhuyuanFeeList(form: ZhuyuanFeeForm): ZhuyuanFeeResponse {
        val request = ZhuyuanFeeRequest(form = form)
        val xml: String = sendRequest(serviceName = "getInpatientFeeList_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanFeeResponse::class.java, xml)
    }

    fun zhuyuanRecharge(form: ZhuyuanRechargeForm): RechargeResponse {
        val request = ZhuyuanRechargeRequest(form = form)
        val xml: String = sendRequest(serviceName = "inpatientAddDeposit_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(RechargeResponse::class.java, xml)
    }

    fun menzhenRecharge(form: MenzhenRechargeForm): RechargeResponse {
        val request = MenzhenRechargeRequest(form = form)
        val xml: String = sendRequest(serviceName = "outpatientRecharge_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(RechargeResponse::class.java, xml)
    }

    fun getZhuyuanBillList(form: ZhuyuanBillForm): ZhuyuanBillResponse {
        val request = ZhuyuanBillRequest(form = form)
        val xml: String = sendRequest(serviceName = "getRechargeRecord_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanBillResponse::class.java, xml)
    }

    fun createAccountAndCard(form: AccountAndCardForm): AccountAndCardResponse {
        val request = AccountAndCardRequest(form = form)
        val xml: String = sendRequest(serviceName = "createAccountAndCard_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(AccountAndCardResponse::class.java, xml)
    }

    fun createVmcardQrCode(form: VmcardQrCodeForm): VmcardQrCodeResponse {
        val request = VmcardQrCodeRequest(form = form)
        val xml: String = sendRequest(serviceName = "createVmcardQRcode_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(VmcardQrCodeResponse::class.java, xml)
    }

    fun menzhenRefund(form: MenzhenRefundForm): MenzhenRefundResponse {
        val request = MenzhenRefundRequest(form = form)
        val xml: String = sendRequest(serviceName = "outpatientRefund_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(MenzhenRefundResponse::class.java, xml)
    }

    fun getReservationDeptList(form: ReservationDeptForm): ReservationDeptResponse {
        val request = ReservationDeptRequest(form = form)
        val xml: String = sendRequest(serviceName = "getReservationDept_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ReservationDeptResponse::class.java, xml)
    }

    fun getReservationDoctorList(form: ReservationDoctorForm): ReservationDoctorResponse {
        val request = ReservationDoctorRequest(form = form)
        val xml: String = sendRequest(serviceName = "getReservationDoctor_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ReservationDoctorResponse::class.java, xml)
    }

    fun getReservationComingDoctorList(form: ReservationComingDoctorForm): ReservationComingDoctorResponse {
        val request = ReservationComingDoctorRequest(form = form)
        val xml: String = sendRequest(serviceName = "getReservationComingDoctor_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ReservationComingDoctorResponse::class.java, xml)
    }

    fun getReservationTicketList(form: ReservationTicketForm): ReservationTicketResponse {
        val request = ReservationTicketRequest(form = form)
        val xml: String = sendRequest(serviceName = "getSourceList_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ReservationTicketResponse::class.java, xml)
    }

    fun reserve(form: ReserveForm): ReservationResponse {
        val request = ReserveRequest(form = form)
        val xml: String = sendRequest(serviceName = "reservationRegister_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ReservationResponse::class.java, xml)
    }

    fun getReservationRecordList(form: ReservationRecordForm): ReservationRecordResponse {
        val request = ReservationRecordRequest(form = form)
        val xml: String = sendRequest(serviceName = "getReservationList_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ReservationRecordResponse::class.java, xml)
    }

    fun cancelReservation(form: ReservationCancelForm): ReservationCancelResponse {
        val request = ReservationCancelRequest(form = form)
        val xml: String = sendRequest(serviceName = "cancelReserve_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ReservationCancelResponse::class.java, xml)
    }

    fun getMaterialList(form: MaterialForm): MaterialResponse {
        val request = MaterialRequest(form = form)
        val xml: String = sendRequest(serviceName = "queryMaterialInfo_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(MaterialResponse::class.java, xml)
    }

    fun getDrugList(form: DrugForm): DrugResponse {
        val request = DrugRequest(form = form)
        val xml: String = sendRequest(serviceName = "queryDrugsInfo_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(DrugResponse::class.java, xml)
    }

    fun getDischargeInstruction(form: DischargeInstructionForm): DischargeInstructionResponse {
        val request = DischargeInstructionRequest(form = form)
        val xml: String = sendRequest(serviceName = "getDischargeMedicationInformation_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(DischargeInstructionResponse::class.java, xml)
    }

    fun getUnpaidMedicalTechnologyList(form: UnpaidMedicalTechnologyForm): UnpaidMedicalTechnologyResponse {
        val request = UnpaidMedicalTechnologyRequest(form = form)
        val xml: String = sendRequest(serviceName = "getUnpaidMedicalTechnology_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(UnpaidMedicalTechnologyResponse::class.java, xml)
    }

    fun getMenzhenFeeList(form: MenzhenFeeForm): MenzhenFeeResponse {
        val request = MenzhenFeeRequest(form = form)
        val xml: String = sendRequest(serviceName = "queryOutpatientFeeDetail_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(MenzhenFeeResponse::class.java, xml)
    }

    fun getBillDetailList(form: BillDetailForm, readTimeout: Int = 80000): BillDetailResponse {
        val request = BillDetailRequest(form = form)
        val xml: String = sendRequest(serviceName = "getAccountFeeList_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(BillDetailResponse::class.java, xml)
    }

    fun getElectronicBillList(form: ElectronicBillForm): ElectronicBillResponse {
        val request = ElectronicBillRequest(form = form)
        val xml: String = sendRequest(serviceName = "getElectronicBill_ZY", xml = request.toXML())
        return JAXBKit.unmarshal(ElectronicBillResponse::class.java, xml)
    }

    fun getCheckReports(form: CheckReportForm): CheckReportResponse {
        val request = CheckReportRequest(form = form)
        val xml: String = sendRequest(serviceName = "listCheckReport", xml = request.toXML())
        return JAXBKit.unmarshal(CheckReportResponse::class.java, xml)
    }

    fun getExamineList(form: ExamineListForm): ExamineListResponse {
        val request = ExamineListRequest(form = form)
        val xml: String = sendRequest(serviceName = "queryExamineList", xml = request.toXML())
        return JAXBKit.unmarshal(ExamineListResponse::class.java, xml)
    }

    fun getExamineDetail(form: ExamineDetailForm): ExamineDetailResponse {
        val request = ExamineDetailRequest(form = form)
        val xml: String = sendRequest(serviceName = "queryExamineDetails", xml = request.toXML())
        return JAXBKit.unmarshal(ExamineDetailResponse::class.java, xml)
    }

    fun queryImageList(form: ImageQueryForm): ImageQueryResponse {
        val request = ImageQueryRequest(form = form)
        val xml: String = sendRequest(serviceName = "ImageQuery", xml = request.toXML())
        return JAXBKit.unmarshal(ImageQueryResponse::class.java, xml)
    }

    fun getCdrJianChaBaoGaoList(form: CdrJianChaBaoGaoListForm): CdrJianChaBaoGaoListResponse {
        val request = CdrJianChaBaoGaoListRequest(form = form)
        val xml: String = sendRequest(serviceName = "GetExamReportListByPatId", xml = request.toXML())
        return JAXBKit.unmarshal(CdrJianChaBaoGaoListResponse::class.java, xml)
    }

    fun getCdrJianChaBaoGaoDetail(form: CdrJianChaBaoGaoDetailForm): CdrJianChaBaoGaoDetailResponse {
        val request = CdrJianChaBaoGaoDetailRequest(form = form)
        val xml: String = sendRequest(serviceName = "GetExamReportById", xml = request.toXML())
        return JAXBKit.unmarshal(CdrJianChaBaoGaoDetailResponse::class.java, xml)
    }

    fun getCdrJianYanBaoGaoList(form: CdrJianYanBaoGaoListForm): CdrJianYanBaoGaoListResponse {
        val request = CdrJianYanBaoGaoListRequest(form = form)
        val xml: String = sendRequest(serviceName = "GetLabReportListByPatId", xml = request.toXML())
        return JAXBKit.unmarshal(CdrJianYanBaoGaoListResponse::class.java, xml)
    }

    fun getCdrChangGuiJianYanBaoGaoDetail(form: CdrChangGuiJianYanBaoGaoDetailForm): CdrChangGuiJianYanBaoGaoDetailResponse {
        val request = CdrChangGuiJianYanBaoGaoDetailRequest(form = form)
        val xml: String = sendRequest(serviceName = "GetLabReportById_Com", xml = request.toXML())
        return JAXBKit.unmarshal(CdrChangGuiJianYanBaoGaoDetailResponse::class.java, xml)
    }

}