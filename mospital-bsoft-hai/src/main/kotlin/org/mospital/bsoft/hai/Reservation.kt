package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationResponse : Response() {
    @field:XmlElement(name = "data")
    var reservation: Reservation = Reservation()
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class Reservation {

    @field:XmlElement(name = "dateType")
    var dateType: String = ""

    @field:XmlElement(name = "deptAddress")
    var deptAddress: String = ""

    @field:XmlElement(name = "deptName")
    var deptName: String = ""

    @field:XmlElement(name = "patientId")
    var patientId: String = ""

    @field:XmlElement(name = "patientName")
    var patientName: String = ""

    /**
     * 挂号类别：1=普通门诊，2=急诊门诊，3=专家门诊，4=专科门诊
     */
    @field:XmlElement(name = "registerType")
    var registerTypeCode: String = ""

    @field:XmlElement(name = "reservationNumber")
    var reservationNumber: String = ""

    @field:XmlElement(name = "reservationTime")
    var reservationTime: String = ""

    @field:XmlElement(name = "visitNumber")
    var visitNumber: String = ""

    var alipayEnergy: Long = 0L

    val ampm: AMPM
        get() = AMPM.fromCode(dateType.toInt())!!

    val registerType: RegisterType
        get() = RegisterType.fromCode(registerTypeCode.toInt())!!
}