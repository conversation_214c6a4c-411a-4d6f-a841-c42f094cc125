package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationTicketRequest() : Request(msgType = "ODS_zy_15") {

    @field:XmlElement(name = "Visit")
    var form: ReservationTicketForm = ReservationTicketForm()

    constructor(form: ReservationTicketForm) : this() {
        this.form = form
    }

}

class ReservationTicketForm(

    @field:XmlElement(name = "deptId")
    val deptId: String="",

    @field:XmlElement(name = "doctorId")
    val doctorId: String = "",

    @field:XmlElement(name = "registerDate")
    val registerDate: String = "",

    /**
     * dateType 日期类型: 1=上午，2=下午，3=晚上
     */
    @field:XmlElement(name = "dateType")
    val dateType: Int = 0,

    /**
     * sourceFlag 号源标志: 0=预约号源，1=现场号源
     */
    @field:XmlElement(name = "sourceFlag")
    val sourceFlag: Int = 0,
) : Request.BaseForm()