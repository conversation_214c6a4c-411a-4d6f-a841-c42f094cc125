package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.common.PatientDataToken
import org.mospital.jackson.DateTimeFormatters
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Suppress("unused")
@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CdrJianChaBaoGaoListResponse {

    @field:XmlElement(name = "MsgHeader")
    var header: CdrMsgHeader = CdrMsgHeader()

    @field:XmlElement(name = "ExamReportList")
    var list: MutableList<CdrJianChaBaoGaoRecord> = mutableListOf()

    fun normalize(): CdrJianChaBaoGaoListResponse {
        this.list.forEach { it.normalize() }
        return this
    }

    fun isOk(): Boolean = true.toString().equals(this.header.status, ignoreCase = true)
    val errorMessage: String
        get() = arrayOf(this.header.errCode, this.header.detail).filterNot { it.isEmpty() }
            .joinToString(separator = "-")

    @XmlAccessorType(XmlAccessType.FIELD)
    class CdrJianChaBaoGaoRecord {

        @field:XmlElement(name = "PatientType")
        var patientTypeCode: String = ""

        @field:XmlElement(name = "AuthorOrganization")
        var authorOrganization: String = ""

        @field:XmlElement(name = "DCId")
        var dcid: String = ""

        @field:XmlElement(name = "ReportDateTime")
        var reportDate: String = ""

        @field:XmlElement(name = "Sex")
        var sexCode: String = ""

        @field:XmlElement(name = "ExaminationType")
        var examinationTypeCode: String = ""

        @field:XmlElement(name = "ReportId")
        var reportId: String = ""

        @field:XmlElement(name = "PrintFlag")
        var printStatusCode: String = ""

        @field:XmlElement(name = "VisitId")
        var visitId: String = ""

        @field:XmlElement(name = "URL")
        var url: String = ""

        @field:XmlElement(name = "Name")
        var name: String = ""

        @field:XmlElement(name = "MpiId")
        var mpiId: String = ""

        @field:XmlElement(name = "HospizationId")
        var hospizationId: String = ""

        @field:XmlElement(name = "PatientId")
        var patientId: String = ""

        @field:XmlElement(name = "ClinicId")
        var clinicId: String = ""

        @field:XmlElement(name = "ExaminationItem")
        var examinationItem: String = ""

        @field:XmlElement(name = "DeptName")
        var deptName: String = ""

        @field:XmlElement(name = "Age")
        var age: String = ""

        @field:XmlElement(name = "SourceVisitId")
        var sourceVisitId: String = ""

        val patientTypeName: String
            get() = CdrPatientType.fromCode(this.patientTypeCode).description

        val sexName: String
            get() = when (this.sexCode) {
                "1" -> "男"
                "2" -> "女"
                else -> ""
            }

        val examinationTypeName: String
            get() = when (this.examinationTypeCode) {
                "01" -> "超声类"
                "02" -> "放射类"
                "021" -> "X线"
                "022" -> "CT"
                "023" -> "MRI"
                "024" -> "DSA"
                "025" -> "造影"
                "03" -> "内镜类"
                "04" -> "病理类"
                "05" -> "心电图"
                "06" -> "脑电图"
                "07" -> "肌电图"
                "08" -> "核医学"
                "09" -> "胃肠动力"
                "10" -> "肺功能"
                "99" -> "其他检查"
                else -> ""
            }

        val printStatusName: String
            get() = when (this.printStatusCode) {
                "0" -> "未打印"
                "1" -> "已打印"
                else -> ""
            }

        var token: String = ""

        fun normalize(): CdrJianChaBaoGaoRecord {
            this.reportDate = try {
                LocalDateTime.parse(this.reportDate, DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss"))
                    .format(DateTimeFormatters.NORM_DATE_FORMATTER)
            } catch (e: Exception) {
                this.reportDate
            }

            this.token = PatientDataToken(
                dataId = this.reportId,
                patientId = this.clinicId + "," + this.hospizationId
            ).generateToken()

            return this
        }
    }
}