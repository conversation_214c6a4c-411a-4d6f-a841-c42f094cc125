package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationTicketResponse : Response() {
    @field:XmlElement(name = "data")
    var reservationTicketList: List<ReservationTicket> = mutableListOf()
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationTicket {

    @field:XmlElement(name = "sourceAccount")
    var amount: Int = 0

    @field:XmlElement(name = "sourceDate")
    var date: String = ""

    @field:XmlElement(name = "timeInterval")
    var period: String = ""

}
