package org.mospital.bsoft.hai

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenRefundResponse : Response() {

    @field:XmlElement(name = "data")
    var menzhenRefund: MenzhenRefund = MenzhenRefund()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenRefund {

    @field:XmlElement(name = "cardBlance")
    var cardBalance: BigDecimal = BigDecimal.ZERO

    @field:XmlElement(name = "orderNo")
    var orderNo: String = ""

    @field:XmlElement(name = "receiptNo")
    var receiptNo: String = ""

}