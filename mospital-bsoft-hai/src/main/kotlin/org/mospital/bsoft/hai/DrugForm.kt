package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class DrugRequest() : Request(msgType = "ODS_zy_20") {

    @field:XmlElement(name = "Visit")
    var form: DrugForm = DrugForm()

    constructor(form: DrugForm) : this() {
        this.form = form
    }

}

class DrugForm(
    @field:XmlElement(name = "materialCode")
    val code: String = ""
): Request.BaseForm()