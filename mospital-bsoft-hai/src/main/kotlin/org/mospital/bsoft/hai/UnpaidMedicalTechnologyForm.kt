package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class UnpaidMedicalTechnologyRequest() : Request(msgType = "ODS_zy_22") {

    @field:XmlElement(name = "Visit")
    var form: UnpaidMedicalTechnologyForm = UnpaidMedicalTechnologyForm()

    constructor(form: UnpaidMedicalTechnologyForm) : this() {
        this.form = form
    }

}

class UnpaidMedicalTechnologyForm(
    @field:XmlElement(name = "isPayFlag")
    val isPayFlag: String = "0",

    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    @field:XmlElement(name = "startTime")
    val startTime: String = "",

    @field:XmlElement(name = "endTime")
    val endTime: String = "",
) : Request.BaseForm()