package org.mospital.bsoft.hai

import org.mospital.common.jaxb.LocalDateTimeJaxbAdapter
import java.time.LocalDateTime
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanBillRequest() : Request(msgType = "ODS_zy_07") {
    @field:XmlElement(name = "Visit")
    var form: ZhuyuanBillForm = ZhuyuanBillForm()

    constructor(form: ZhuyuanBillForm) : this() {
        this.form = form
    }

}

class ZhuyuanBillForm(

    @field:XmlElement(name = "admissionNo")
    val admissionNo: String="",

    @field:XmlJavaTypeAdapter(LocalDateTimeJaxbAdapter::class)
    @field:XmlElement(name = "startTime")
    val startTime: LocalDateTime? = null,

    @field:XmlJavaTypeAdapter(LocalDateTimeJaxbAdapter::class)
    @field:XmlElement(name = "endTime")
    val endTime: LocalDateTime? = null
) : Request.BaseForm()