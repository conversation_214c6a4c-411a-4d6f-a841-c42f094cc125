package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.*

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ExamineListResponse {

    @field:XmlElementWrapper(name = "_li")
    @field:XmlElement(name = "t")
    var reports: List<Report> = mutableListOf()

    @XmlAccessorType(XmlAccessType.FIELD)
    class Report {
        @field:XmlElement(name = "gender")
        var gender: String = ""

        @field:XmlElement(name = "purpose")
        var purpose: String = ""

        @field:XmlElement(name = "report_time")
        var reportTime: String = ""

        @field:XmlElement(name = "outpatient_id")
        var outpatientId: String = ""

        @field:XmlElement(name = "exam_time")
        var examTime: String = ""

        @field:XmlElement(name = "name")
        var name: String = ""

        @field:XmlElement(name = "id")
        var id: String = ""

        @field:XmlElement(name = "type")
        var type: String = ""
    }
}
