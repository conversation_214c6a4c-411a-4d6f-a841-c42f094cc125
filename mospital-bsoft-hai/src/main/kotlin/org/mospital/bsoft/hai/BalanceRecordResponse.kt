package org.mospital.bsoft.hai

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class BalanceRecordResponse : Response() {

    @field:XmlElement(name = "data")
    var balanceRecords: MutableList<BalanceRecord> = mutableListOf()

    override fun isOk(): Boolean {
        return super.isOk() || message.contains("无记录")
    }
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class BalanceRecord {
    @field:XmlElement(name = "operationDate")
    var operationDate: String = ""

    @field:XmlElement(name = "payType")
    var payType: String = ""

    @field:XmlElement(name = "debitAmount")
    val debitAmount: String = ""

    @field:XmlElement(name = "creditAmount")
    val creditAmount: String = ""

    @field:XmlElement(name = "operationType")
    val operationType: String = ""

    @field:XmlElement(name = "orderNumber")
    val orderNumber: String = ""

    val payTypeText: String
        get() = when (payType) {
            "1" -> "现金"
            "23" -> "银行卡"
            "16" -> "微信"
            "14" -> "支付宝"
            else -> "其它"
        }

    val amount: BigDecimal
        get() = if (creditAmount.toBigDecimal() > BigDecimal.ZERO) {
            creditAmount.toBigDecimal().negate()
        } else {
            debitAmount.toBigDecimal()
        }

    fun isRecharge(): Boolean {
        return amount > BigDecimal.ZERO
    }

    fun isRefund(): Boolean {
        return amount < BigDecimal.ZERO
    }
}

enum class ClientType {
    WXXCX,
    ZFBXCX,
    ZZJ
}

data class MenzhenBillForRefund(
    val orderId: String,
    val orderTime: String,
    val clientType: ClientType,
    val amount: BigDecimal,
    var exrefund: BigDecimal = BigDecimal.ZERO,
) {
    val unrefund: BigDecimal
        get() = amount - exrefund

    fun increExrefund(increment: BigDecimal) {
        exrefund += increment
    }

}