package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class CdrChangGuiJianYanBaoGaoDetailRequest() : Request(msgType = "CDR_0909", msgVersion = "3.0") {

    @field:XmlElement(name = "QueryCond")
    var form: CdrChangGuiJianYanBaoGaoDetailForm = CdrChangGuiJianYanBaoGaoDetailForm()

    constructor(form: CdrChangGuiJianYanBaoGaoDetailForm) : this() {
        this.form = form
    }

}

/**
 * 常规检验报告详情的查询表单
 * 样本编号、条码号二选一传入即可
 *
 * @property dcid 文档唯一号
 * @property sampleNo 样本编号
 * @property barcodeNo 条码号
 * @property authorOrganization 就诊机构代码
 */
class CdrChangGuiJianYanBaoGaoDetailForm(

    @field:XmlElement(name = "DCID")
    val dcid: String = "",

    @field:XmlElement(name = "SampleNo")
    val sampleNo: String = "",

    @field:XmlElement(name = "BarcodeNo")
    val barcodeNo: String = "",

    @field:XmlElement(name = "AuthorOrganization")
    val authorOrganization: String = BsoftHaiSetting.authorOrganization,
)