package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class BillDetailResponse : Response() {

    @field:XmlElement(name = "data")
    var billDetailList: MutableList<BillDetail> = mutableListOf()

}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class BillDetail {

    /**
     * 交易时间
     */
    @field:XmlElement(name = "accountTime")
    var accountTime: String = ""

    @field:XmlElement(name = "userId")
    var userId: String = ""

    /**
     * 支付方式：16=微信，14=支付宝，21=微信，15=支付宝
     */
    @field:XmlElement(name = "paymentType")
    var paymentType: String = ""

    /**
     * 交易类型，掌医充值、掌医退费等
     */
    @field:XmlElement(name = "bizType")
    var bizType: String = ""

    /**
     * 账户类型：1=门诊，2=住院
     */
    @field:XmlElement(name = "accountType")
    var accountType: String = ""

    /**
     * 就诊卡号
     */
    @field:XmlElement(name = "visitCard")
    var visitCard: String = ""

    @field:XmlElement(name = "patientId")
    var patientId: String = ""

    @field:XmlElement(name = "platformTransId")
    var platformTransId: String = ""

    @field:XmlElement(name = "paymentMoney")
    var paymentMoney: String = ""

    @field:XmlElement(name = "moneyType")
    var moneyType: String = ""

    val isMenzhen: Boolean
        get() = accountType == "1"

    val isZhuyuan: Boolean
        get() = accountType == "2"

    val isRecharge: Boolean
        get() = moneyType == "0"

    val isRefund: Boolean
        get() = moneyType == "1"

    val isWechat: Boolean
        get() = paymentType == "16" || paymentType == "21"

    val isAlipay: Boolean
        get() = paymentType == "14" || paymentType == "15"
}