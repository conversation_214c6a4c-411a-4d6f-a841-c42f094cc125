package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenFeeRequest() : Request(msgType = "ODS_zy_23") {

    @field:XmlElement(name = "Visit")
    var form: MenzhenFeeForm = MenzhenFeeForm()

    constructor(form: MenzhenFeeForm) : this() {
        this.form = form
    }

}

class MenzhenFeeForm(
    @field:XmlElement(name = "patientId")
    val patientId: String = "",

    @field:XmlElement(name = "startTime")
    val startTime: String = "",

    @field:XmlElement(name = "endTime")
    val endTime: String = ""
): Request.BaseForm()