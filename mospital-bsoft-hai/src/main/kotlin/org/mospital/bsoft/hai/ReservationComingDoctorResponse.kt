package org.mospital.bsoft.hai

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "BSXml")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationComingDoctorResponse : Response() {
    @field:XmlElement(name = "data")
    var reservationComingDoctorList: List<ReservationComingDoctor> = mutableListOf()
}

@XmlRootElement(name = "data")
@XmlAccessorType(XmlAccessType.FIELD)
class ReservationComingDoctor {

    @field:XmlElement(name = "reserveTime")
    var reserveTime: String = ""

    @field:XmlElement(name = "sourceType")
    var sourceType: String = ""

}
