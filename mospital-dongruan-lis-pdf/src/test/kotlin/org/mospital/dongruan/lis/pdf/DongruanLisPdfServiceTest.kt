package org.mospital.dongruan.lis.pdf

import org.junit.jupiter.api.Test
import org.mospital.jackson.DateTimeFormatters

import java.time.LocalDate

class DongruanLisPdfServiceTest {

    @Test
    fun testGetTestFormPDFByPatientID() {
        DongruanLisPdfService.getTestFormPDFByPatientID(
            patientId = "**********",
            patientType = "门诊",
            beginDate = LocalDate.now().minusDays(300),
            endDate = LocalDate.now()
        ).forEach { println(it) }
    }

    @Test
    fun testGetRTPDF() {
        DongruanLisPdfService.getRTPDF("SH_Roche_b123", "823", LocalDate.parse("2023-02-01", DateTimeFormatters.NORM_DATE_FORMATTER))
    }

}