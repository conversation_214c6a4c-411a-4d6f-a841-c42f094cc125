package org.mospital.dongruan.lis.pdf


// 20230110|SH_IL_GEM3000|94|血气分析|RT

/**
 * @param reportStatus 报告状态：5=审核，2=核收，0=接收，9=待查
 */
data class LisReportForm(
    val reportDate: String,
    val machineId: String,
    val sampleId: String,
    val reportTitle: String,
    val reportType: String

) {
    companion object {
        fun fromString(s: String): LisReportForm {
            val attributes = s.split("|")
            return LisReportForm(
                reportDate = attributes[0],
                machineId = attributes[1],
                sampleId = attributes[2],
                reportTitle = attributes[3],
                reportType = attributes[4]

            )
        }
    }

}
