package org.mospital.dongruan.lis.pdf

import org.mospital.common.IdUtil
import org.mospital.dongruan.lis.pdf.client.NeuLisExportPDFService
import org.mospital.dongruan.lis.pdf.client.NeuLisExportPDFServiceSoap
import org.mospital.jackson.DateTimeFormatters
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.LocalDate

object DongruanLisPdfService {

    private val log: Logger = LoggerFactory.getLogger(DongruanLisPdfService::class.java)
    private val client: NeuLisExportPDFServiceSoap =
        NeuLisExportPDFService(Configuration.wsdlUrl).neuLisExportPDFServiceSoap

    fun getTestFormPDFByPatientID(
        patientId: String,
        patientType: String,
        beginDate: LocalDate,
        endDate: LocalDate
    ): List<LisReportForm> {
        val traceId = IdUtil.simpleUUID()

        val formattedBeginDate = beginDate.format(DateTimeFormatters.NORM_DATE_FORMATTER)
        val formattedEndDate = endDate.format(DateTimeFormatters.NORM_DATE_FORMATTER)
        log.info("GetTestFormPDFByPatientID#$traceId: url=${Configuration.wsdlUrl}, patientId=$patientId, patientType=$patientType, beginDate=$formattedBeginDate, endDate=$formattedEndDate")

        val strings = client.getTestFormPDFByPatientID(patientId, patientType, formattedBeginDate, formattedEndDate)
        return strings.string.map { LisReportForm.fromString(it) }
    }

    fun getRTPDF(machineId: String, sampleId: String, testDate: LocalDate): ByteArray {
        val traceId = IdUtil.simpleUUID()

        val formattedTestDate = testDate.format(DateTimeFormatters.PURE_DATE_FORMATTER)
        log.info("ExportTestFormPDF_RT#$traceId: url=${Configuration.wsdlUrl}, machineId=$machineId, sampleId=$sampleId, testDate=$formattedTestDate")
        return client.exportTestFormPDFRT(machineId, sampleId, formattedTestDate)
    }

    fun getGMPDF(machineId: String, sampleId: String, testDate: LocalDate): ByteArray {
        val traceId = IdUtil.simpleUUID()

        val formattedTestDate = testDate.format(DateTimeFormatters.PURE_DATE_FORMATTER)
        log.info("ExportTestFormPDF_GM#$traceId: url=${Configuration.wsdlUrl}, machineId=$machineId, sampleId=$sampleId, testDate=$formattedTestDate")
        return client.exportTestFormPDFGM(machineId, sampleId, formattedTestDate)
    }

}