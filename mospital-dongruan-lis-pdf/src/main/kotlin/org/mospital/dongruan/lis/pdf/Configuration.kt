package org.mospital.dongruan.lis.pdf

import org.mospital.jackson.ConfigLoader
import java.net.URI
import java.net.URL

object Configuration {

    private val config: DongruanLisPdfConfig by lazy {
        ConfigLoader.loadConfig("dongruan-lis-pdf.yaml", DongruanLisPdfConfig::class.java, Configuration::class.java)
    }

    val wsdlUrl: URL get() = URI.create(config.neuLisPdfServiceWsdlUrl).toURL()
}