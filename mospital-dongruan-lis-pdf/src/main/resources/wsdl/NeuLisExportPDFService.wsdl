<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/"
                  xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  targetNamespace="http://tempuri.org/">
    <wsdl:types>
        <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
            <s:element name="HelloWorld">
                <s:complexType/>
            </s:element>
            <s:element name="HelloWorldResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="OpenCon">
                <s:complexType/>
            </s:element>
            <s:element name="OpenConResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="1" maxOccurs="1" name="OpenConResult" type="s:int"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_byID">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                        <s:element minOccurs="1" maxOccurs="1" name="testType" type="tns:TestType"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:simpleType name="TestType">
                <s:restriction base="s:string">
                    <s:enumeration value="RT"/>
                    <s:enumeration value="GM"/>
                    <s:enumeration value="MB"/>
                </s:restriction>
            </s:simpleType>
            <s:element name="ExportTestFormPDF_byIDResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_byIDResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:complexType name="ArrayOfString">
                <s:sequence>
                    <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string"/>
                </s:sequence>
            </s:complexType>
            <s:element name="ExportTestFormPDF_TESTTYPE">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testType" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_TESTTYPEResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_TESTTYPEResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_RT">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_RTResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_RTResult" type="s:base64Binary"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_RTLanguage">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                        <s:element minOccurs="1" maxOccurs="1" name="isMultilingual" type="s:boolean"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_RTLanguageResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_RTLanguageResult" type="s:base64Binary"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_GM">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_GMResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_GMResult" type="s:base64Binary"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_GMLanguage">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                        <s:element minOccurs="1" maxOccurs="1" name="isMultilingual" type="s:boolean"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_GMLanguageResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_GMLanguageResult" type="s:base64Binary"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_ByPatientSeq">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientSeq" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientType" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="path" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_ByPatientSeqResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_ByPatientSeqResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientType" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtReg" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtEnd" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDFResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDFResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetItemInfoFromUndoReport">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientseq" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetItemInfoFromUndoReportResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetItemInfoFromUndoReportResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_RTForNoPaper">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_RTForNoPaperResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_RTForNoPaperResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_GMForNoPaper">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="machineId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="sampleId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="testdate" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ExportTestFormPDF_GMForNoPaperResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ExportTestFormPDF_GMForNoPaperResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTestFormPDF">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientSeq" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientType" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTestFormPDFResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetTestFormPDFResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTestFormPDFByPatientID">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientType" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="beginDate" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="endDate" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTestFormPDFByPatientIDResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetTestFormPDFByPatientIDResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTestForm">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="patientId" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="patientType" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtReg" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="dtEnd" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="includeNoAccept" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="GetTestFormResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="GetTestFormResult" type="tns:ArrayOfString"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="string" nillable="true" type="s:string"/>
            <s:element name="int" type="s:int"/>
            <s:element name="ArrayOfString" nillable="true" type="tns:ArrayOfString"/>
            <s:element name="base64Binary" nillable="true" type="s:base64Binary"/>
        </s:schema>
    </wsdl:types>
    <wsdl:message name="HelloWorldSoapIn">
        <wsdl:part name="parameters" element="tns:HelloWorld"/>
    </wsdl:message>
    <wsdl:message name="HelloWorldSoapOut">
        <wsdl:part name="parameters" element="tns:HelloWorldResponse"/>
    </wsdl:message>
    <wsdl:message name="OpenConSoapIn">
        <wsdl:part name="parameters" element="tns:OpenCon"/>
    </wsdl:message>
    <wsdl:message name="OpenConSoapOut">
        <wsdl:part name="parameters" element="tns:OpenConResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_byIDSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_byID"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_byIDSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_byIDResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_TESTTYPESoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_TESTTYPE"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_TESTTYPESoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_TESTTYPEResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_RT"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_RTResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTLanguageSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_RTLanguage"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTLanguageSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_RTLanguageResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_GM"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_GMResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMLanguageSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_GMLanguage"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMLanguageSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_GMLanguageResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_ByPatientSeqSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_ByPatientSeq"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_ByPatientSeqSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_ByPatientSeqResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDFSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDFSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDFResponse"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportSoapIn">
        <wsdl:part name="parameters" element="tns:GetItemInfoFromUndoReport"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportSoapOut">
        <wsdl:part name="parameters" element="tns:GetItemInfoFromUndoReportResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTForNoPaperSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_RTForNoPaper"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTForNoPaperSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_RTForNoPaperResponse"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMForNoPaperSoapIn">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_GMForNoPaper"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMForNoPaperSoapOut">
        <wsdl:part name="parameters" element="tns:ExportTestFormPDF_GMForNoPaperResponse"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFSoapIn">
        <wsdl:part name="parameters" element="tns:GetTestFormPDF"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFSoapOut">
        <wsdl:part name="parameters" element="tns:GetTestFormPDFResponse"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFByPatientIDSoapIn">
        <wsdl:part name="parameters" element="tns:GetTestFormPDFByPatientID"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFByPatientIDSoapOut">
        <wsdl:part name="parameters" element="tns:GetTestFormPDFByPatientIDResponse"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormSoapIn">
        <wsdl:part name="parameters" element="tns:GetTestForm"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormSoapOut">
        <wsdl:part name="parameters" element="tns:GetTestFormResponse"/>
    </wsdl:message>
    <wsdl:message name="HelloWorldHttpGetIn"/>
    <wsdl:message name="HelloWorldHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="OpenConHttpGetIn"/>
    <wsdl:message name="OpenConHttpGetOut">
        <wsdl:part name="Body" element="tns:int"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_byIDHttpGetIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="testType" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_byIDHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_TESTTYPEHttpGetIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testType" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_TESTTYPEHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTHttpGetIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTHttpGetOut">
        <wsdl:part name="Body" element="tns:base64Binary"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTLanguageHttpGetIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="isMultilingual" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTLanguageHttpGetOut">
        <wsdl:part name="Body" element="tns:base64Binary"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMHttpGetIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMHttpGetOut">
        <wsdl:part name="Body" element="tns:base64Binary"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMLanguageHttpGetIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="isMultilingual" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMLanguageHttpGetOut">
        <wsdl:part name="Body" element="tns:base64Binary"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_ByPatientSeqHttpGetIn">
        <wsdl:part name="patientSeq" type="s:string"/>
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="path" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_ByPatientSeqHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDFHttpGetIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDFHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportHttpGetIn">
        <wsdl:part name="patientseq" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTForNoPaperHttpGetIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTForNoPaperHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMForNoPaperHttpGetIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMForNoPaperHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFHttpGetIn">
        <wsdl:part name="patientSeq" type="s:string"/>
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFByPatientIDHttpGetIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="beginDate" type="s:string"/>
        <wsdl:part name="endDate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFByPatientIDHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormHttpGetIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
        <wsdl:part name="includeNoAccept" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormHttpGetOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="HelloWorldHttpPostIn"/>
    <wsdl:message name="HelloWorldHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="OpenConHttpPostIn"/>
    <wsdl:message name="OpenConHttpPostOut">
        <wsdl:part name="Body" element="tns:int"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_byIDHttpPostIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="testType" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_byIDHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_TESTTYPEHttpPostIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testType" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_TESTTYPEHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTHttpPostIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTHttpPostOut">
        <wsdl:part name="Body" element="tns:base64Binary"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTLanguageHttpPostIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="isMultilingual" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTLanguageHttpPostOut">
        <wsdl:part name="Body" element="tns:base64Binary"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMHttpPostIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMHttpPostOut">
        <wsdl:part name="Body" element="tns:base64Binary"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMLanguageHttpPostIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
        <wsdl:part name="isMultilingual" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMLanguageHttpPostOut">
        <wsdl:part name="Body" element="tns:base64Binary"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_ByPatientSeqHttpPostIn">
        <wsdl:part name="patientSeq" type="s:string"/>
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="path" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_ByPatientSeqHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDFHttpPostIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDFHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportHttpPostIn">
        <wsdl:part name="patientseq" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetItemInfoFromUndoReportHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTForNoPaperHttpPostIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_RTForNoPaperHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMForNoPaperHttpPostIn">
        <wsdl:part name="machineId" type="s:string"/>
        <wsdl:part name="sampleId" type="s:string"/>
        <wsdl:part name="testdate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ExportTestFormPDF_GMForNoPaperHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFHttpPostIn">
        <wsdl:part name="patientSeq" type="s:string"/>
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFByPatientIDHttpPostIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="beginDate" type="s:string"/>
        <wsdl:part name="endDate" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormPDFByPatientIDHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormHttpPostIn">
        <wsdl:part name="patientId" type="s:string"/>
        <wsdl:part name="patientType" type="s:string"/>
        <wsdl:part name="dtReg" type="s:string"/>
        <wsdl:part name="dtEnd" type="s:string"/>
        <wsdl:part name="includeNoAccept" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="GetTestFormHttpPostOut">
        <wsdl:part name="Body" element="tns:ArrayOfString"/>
    </wsdl:message>
    <wsdl:portType name="NeuLisExportPDFServiceSoap">
        <wsdl:operation name="HelloWorld">
            <wsdl:input message="tns:HelloWorldSoapIn"/>
            <wsdl:output message="tns:HelloWorldSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <wsdl:input message="tns:OpenConSoapIn"/>
            <wsdl:output message="tns:OpenConSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_byID">
            <wsdl:input message="tns:ExportTestFormPDF_byIDSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_byIDSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_TESTTYPE">
            <wsdl:input message="tns:ExportTestFormPDF_TESTTYPESoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_TESTTYPESoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RT">
            <wsdl:input message="tns:ExportTestFormPDF_RTSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTLanguage">
            <wsdl:input message="tns:ExportTestFormPDF_RTLanguageSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTLanguageSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GM">
            <wsdl:input message="tns:ExportTestFormPDF_GMSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMLanguage">
            <wsdl:input message="tns:ExportTestFormPDF_GMLanguageSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMLanguageSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_ByPatientSeq">
            <wsdl:input message="tns:ExportTestFormPDF_ByPatientSeqSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_ByPatientSeqSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF">
            <wsdl:input message="tns:ExportTestFormPDFSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDFSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">未出报告返回项目编码等信息</wsdl:documentation>
            <wsdl:input message="tns:GetItemInfoFromUndoReportSoapIn"/>
            <wsdl:output message="tns:GetItemInfoFromUndoReportSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTForNoPaper">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">无纸化归档（常规）</wsdl:documentation>
            <wsdl:input message="tns:ExportTestFormPDF_RTForNoPaperSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTForNoPaperSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMForNoPaper">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">无纸化归档（微生物）</wsdl:documentation>
            <wsdl:input message="tns:ExportTestFormPDF_GMForNoPaperSoapIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMForNoPaperSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDF">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取检验单列表</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormPDFSoapIn"/>
            <wsdl:output message="tns:GetTestFormPDFSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDFByPatientID">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取检验单列表（通过PATIENTID）</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormPDFByPatientIDSoapIn"/>
            <wsdl:output message="tns:GetTestFormPDFByPatientIDSoapOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获得检验单对象信息，返回接口要求格式的数组内容</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormSoapIn"/>
            <wsdl:output message="tns:GetTestFormSoapOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:portType name="NeuLisExportPDFServiceHttpGet">
        <wsdl:operation name="HelloWorld">
            <wsdl:input message="tns:HelloWorldHttpGetIn"/>
            <wsdl:output message="tns:HelloWorldHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <wsdl:input message="tns:OpenConHttpGetIn"/>
            <wsdl:output message="tns:OpenConHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_byID">
            <wsdl:input message="tns:ExportTestFormPDF_byIDHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_byIDHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_TESTTYPE">
            <wsdl:input message="tns:ExportTestFormPDF_TESTTYPEHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_TESTTYPEHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RT">
            <wsdl:input message="tns:ExportTestFormPDF_RTHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTLanguage">
            <wsdl:input message="tns:ExportTestFormPDF_RTLanguageHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTLanguageHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GM">
            <wsdl:input message="tns:ExportTestFormPDF_GMHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMLanguage">
            <wsdl:input message="tns:ExportTestFormPDF_GMLanguageHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMLanguageHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_ByPatientSeq">
            <wsdl:input message="tns:ExportTestFormPDF_ByPatientSeqHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_ByPatientSeqHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF">
            <wsdl:input message="tns:ExportTestFormPDFHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDFHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">未出报告返回项目编码等信息</wsdl:documentation>
            <wsdl:input message="tns:GetItemInfoFromUndoReportHttpGetIn"/>
            <wsdl:output message="tns:GetItemInfoFromUndoReportHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTForNoPaper">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">无纸化归档（常规）</wsdl:documentation>
            <wsdl:input message="tns:ExportTestFormPDF_RTForNoPaperHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTForNoPaperHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMForNoPaper">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">无纸化归档（微生物）</wsdl:documentation>
            <wsdl:input message="tns:ExportTestFormPDF_GMForNoPaperHttpGetIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMForNoPaperHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDF">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取检验单列表</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormPDFHttpGetIn"/>
            <wsdl:output message="tns:GetTestFormPDFHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDFByPatientID">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取检验单列表（通过PATIENTID）</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormPDFByPatientIDHttpGetIn"/>
            <wsdl:output message="tns:GetTestFormPDFByPatientIDHttpGetOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获得检验单对象信息，返回接口要求格式的数组内容</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormHttpGetIn"/>
            <wsdl:output message="tns:GetTestFormHttpGetOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:portType name="NeuLisExportPDFServiceHttpPost">
        <wsdl:operation name="HelloWorld">
            <wsdl:input message="tns:HelloWorldHttpPostIn"/>
            <wsdl:output message="tns:HelloWorldHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <wsdl:input message="tns:OpenConHttpPostIn"/>
            <wsdl:output message="tns:OpenConHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_byID">
            <wsdl:input message="tns:ExportTestFormPDF_byIDHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_byIDHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_TESTTYPE">
            <wsdl:input message="tns:ExportTestFormPDF_TESTTYPEHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_TESTTYPEHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RT">
            <wsdl:input message="tns:ExportTestFormPDF_RTHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTLanguage">
            <wsdl:input message="tns:ExportTestFormPDF_RTLanguageHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTLanguageHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GM">
            <wsdl:input message="tns:ExportTestFormPDF_GMHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMLanguage">
            <wsdl:input message="tns:ExportTestFormPDF_GMLanguageHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMLanguageHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_ByPatientSeq">
            <wsdl:input message="tns:ExportTestFormPDF_ByPatientSeqHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_ByPatientSeqHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF">
            <wsdl:input message="tns:ExportTestFormPDFHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDFHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">未出报告返回项目编码等信息</wsdl:documentation>
            <wsdl:input message="tns:GetItemInfoFromUndoReportHttpPostIn"/>
            <wsdl:output message="tns:GetItemInfoFromUndoReportHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTForNoPaper">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">无纸化归档（常规）</wsdl:documentation>
            <wsdl:input message="tns:ExportTestFormPDF_RTForNoPaperHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_RTForNoPaperHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMForNoPaper">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">无纸化归档（微生物）</wsdl:documentation>
            <wsdl:input message="tns:ExportTestFormPDF_GMForNoPaperHttpPostIn"/>
            <wsdl:output message="tns:ExportTestFormPDF_GMForNoPaperHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDF">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取检验单列表</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormPDFHttpPostIn"/>
            <wsdl:output message="tns:GetTestFormPDFHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDFByPatientID">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取检验单列表（通过PATIENTID）</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormPDFByPatientIDHttpPostIn"/>
            <wsdl:output message="tns:GetTestFormPDFByPatientIDHttpPostOut"/>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获得检验单对象信息，返回接口要求格式的数组内容</wsdl:documentation>
            <wsdl:input message="tns:GetTestFormHttpPostIn"/>
            <wsdl:output message="tns:GetTestFormHttpPostOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="NeuLisExportPDFServiceSoap" type="tns:NeuLisExportPDFServiceSoap">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="HelloWorld">
            <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <soap:operation soapAction="http://tempuri.org/OpenCon" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_byID">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_byID" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_TESTTYPE">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_TESTTYPE" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RT">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_RT" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTLanguage">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_RTLanguage" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GM">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_GM" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMLanguage">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_GMLanguage" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_ByPatientSeq">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_ByPatientSeq" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <soap:operation soapAction="http://tempuri.org/GetItemInfoFromUndoReport" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTForNoPaper">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_RTForNoPaper" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMForNoPaper">
            <soap:operation soapAction="http://tempuri.org/ExportTestFormPDF_GMForNoPaper" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDF">
            <soap:operation soapAction="http://tempuri.org/GetTestFormPDF" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDFByPatientID">
            <soap:operation soapAction="http://tempuri.org/GetTestFormPDFByPatientID" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <soap:operation soapAction="http://tempuri.org/GetTestForm" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="NeuLisExportPDFServiceSoap12" type="tns:NeuLisExportPDFServiceSoap">
        <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="HelloWorld">
            <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <soap12:operation soapAction="http://tempuri.org/OpenCon" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_byID">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_byID" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_TESTTYPE">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_TESTTYPE" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RT">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_RT" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTLanguage">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_RTLanguage" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GM">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_GM" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMLanguage">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_GMLanguage" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_ByPatientSeq">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_ByPatientSeq" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <soap12:operation soapAction="http://tempuri.org/GetItemInfoFromUndoReport" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTForNoPaper">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_RTForNoPaper" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMForNoPaper">
            <soap12:operation soapAction="http://tempuri.org/ExportTestFormPDF_GMForNoPaper" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDF">
            <soap12:operation soapAction="http://tempuri.org/GetTestFormPDF" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDFByPatientID">
            <soap12:operation soapAction="http://tempuri.org/GetTestFormPDFByPatientID" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <soap12:operation soapAction="http://tempuri.org/GetTestForm" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="NeuLisExportPDFServiceHttpGet" type="tns:NeuLisExportPDFServiceHttpGet">
        <http:binding verb="GET"/>
        <wsdl:operation name="HelloWorld">
            <http:operation location="/HelloWorld"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <http:operation location="/OpenCon"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_byID">
            <http:operation location="/ExportTestFormPDF_byID"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_TESTTYPE">
            <http:operation location="/ExportTestFormPDF_TESTTYPE"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RT">
            <http:operation location="/ExportTestFormPDF_RT"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTLanguage">
            <http:operation location="/ExportTestFormPDF_RTLanguage"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GM">
            <http:operation location="/ExportTestFormPDF_GM"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMLanguage">
            <http:operation location="/ExportTestFormPDF_GMLanguage"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_ByPatientSeq">
            <http:operation location="/ExportTestFormPDF_ByPatientSeq"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF">
            <http:operation location="/ExportTestFormPDF"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <http:operation location="/GetItemInfoFromUndoReport"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTForNoPaper">
            <http:operation location="/ExportTestFormPDF_RTForNoPaper"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMForNoPaper">
            <http:operation location="/ExportTestFormPDF_GMForNoPaper"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDF">
            <http:operation location="/GetTestFormPDF"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDFByPatientID">
            <http:operation location="/GetTestFormPDFByPatientID"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <http:operation location="/GetTestForm"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="NeuLisExportPDFServiceHttpPost" type="tns:NeuLisExportPDFServiceHttpPost">
        <http:binding verb="POST"/>
        <wsdl:operation name="HelloWorld">
            <http:operation location="/HelloWorld"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="OpenCon">
            <http:operation location="/OpenCon"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_byID">
            <http:operation location="/ExportTestFormPDF_byID"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_TESTTYPE">
            <http:operation location="/ExportTestFormPDF_TESTTYPE"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RT">
            <http:operation location="/ExportTestFormPDF_RT"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTLanguage">
            <http:operation location="/ExportTestFormPDF_RTLanguage"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GM">
            <http:operation location="/ExportTestFormPDF_GM"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMLanguage">
            <http:operation location="/ExportTestFormPDF_GMLanguage"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_ByPatientSeq">
            <http:operation location="/ExportTestFormPDF_ByPatientSeq"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF">
            <http:operation location="/ExportTestFormPDF"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetItemInfoFromUndoReport">
            <http:operation location="/GetItemInfoFromUndoReport"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_RTForNoPaper">
            <http:operation location="/ExportTestFormPDF_RTForNoPaper"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ExportTestFormPDF_GMForNoPaper">
            <http:operation location="/ExportTestFormPDF_GMForNoPaper"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDF">
            <http:operation location="/GetTestFormPDF"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestFormPDFByPatientID">
            <http:operation location="/GetTestFormPDFByPatientID"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetTestForm">
            <http:operation location="/GetTestForm"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="NeuLisExportPDFService">
        <wsdl:port name="NeuLisExportPDFServiceSoap" binding="tns:NeuLisExportPDFServiceSoap">
            <soap:address location="http://127.0.0.1:7045/NeuLisExportPDFService.asmx"/>
        </wsdl:port>
        <wsdl:port name="NeuLisExportPDFServiceSoap12" binding="tns:NeuLisExportPDFServiceSoap12">
            <soap12:address location="http://127.0.0.1:7045/NeuLisExportPDFService.asmx"/>
        </wsdl:port>
        <wsdl:port name="NeuLisExportPDFServiceHttpGet" binding="tns:NeuLisExportPDFServiceHttpGet">
            <http:address location="http://127.0.0.1:7045/NeuLisExportPDFService.asmx"/>
        </wsdl:port>
        <wsdl:port name="NeuLisExportPDFServiceHttpPost" binding="tns:NeuLisExportPDFServiceHttpPost">
            <http:address location="http://127.0.0.1:7045/NeuLisExportPDFService.asmx"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>