package org.mospital.dongruan.pay

enum class UnifiedPayStatusEnum(val code: String, val msg: String) {
    SUCCESS("SUCCESS", "支付成功"),
    FAILURE("FAILURE", "支付失败"),
    USERPAYING("USERPAYING", "等待用户付款"),
    SYSTEMERROR("SYSTEMERROR", "未知异常"),
    CALL_FAILURE("CALL_FAILURE", "调用第三方失败，重试"),
    TRADE_FINISHED("TRADE_FINISHED", "交易结束")
    ;

    companion object {
        fun getByCode(code: String): UnifiedPayStatusEnum? {
            return values().find { it.code == code }
        }
    }
}