package org.mospital.dongruan.pay

import com.fasterxml.jackson.annotation.JsonIgnore

class UnifiedPayOrderQueryRequest (
    appid: String = DongruanPaySetting.APPID,
    method: String = "rop.trade.query",
    version: String = DongruanPaySetting.VERSION,
    charset: String = DongruanPaySetting.CHARSET,
    signType: String = DongruanPaySetting.SIGN_TYPE,
    @field:JsonIgnore val openid: String,
) : BaseForm(appid, method, version, charset, signType) {
    fun orderNo(orderNo: String): UnifiedPayOrderQueryRequest {
        this.bizContentMap["orderNo"] = orderNo
        return this
    }
}