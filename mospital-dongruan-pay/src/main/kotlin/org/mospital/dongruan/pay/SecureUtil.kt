package org.mospital.dongruan.pay

import org.dromara.hutool.core.codec.binary.Base64
import java.security.KeyFactory
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec

object SecureUtil {

    private val algorithm = "SHA1WithRSA"

    fun sign(rawText: String, privateKeyString: String): String {
        val pkcs8EncodedKeySpec = PKCS8EncodedKeySpec(Base64.decode(privateKeyString))
        val signature = Signature.getInstance(algorithm)
        signature.initSign(KeyFactory.getInstance("RSA").generatePrivate(pkcs8EncodedKeySpec))
        signature.update(rawText.toByteArray(charset = Charsets.UTF_8))
        return Base64.encodeUrlSafe(signature.sign())
    }

    fun sign(map: Map<String, String>, key: String): String {
        val rawText =
            map.filter { it.key != "sign" }.toSortedMap().entries.joinToString("&") { "${it.key}=${it.value}" }
        return sign(rawText, key)
    }
}
