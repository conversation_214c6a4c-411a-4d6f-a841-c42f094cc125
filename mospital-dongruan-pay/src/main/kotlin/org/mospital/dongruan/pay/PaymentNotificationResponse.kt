package org.mospital.dongruan.pay

/**
 * 支付通知响应数据类
 * 用于响应东软支付平台的支付通知请求
 */
data class PaymentNotificationResponse(
    /** 响应状态：SUCCESS-成功处理，FAIL-处理失败 */
    val status: String,
    
    /** 响应消息 */
    val message: String = ""
) {
    companion object {
        /** 成功响应状态 */
        const val STATUS_SUCCESS = "SUCCESS"
        
        /** 失败响应状态 */
        const val STATUS_FAIL = "FAIL"
        
        /**
         * 创建成功响应
         * @param message 响应消息
         */
        fun success(message: String = "处理成功"): PaymentNotificationResponse {
            return PaymentNotificationResponse(STATUS_SUCCESS, message)
        }
        
        /**
         * 创建失败响应
         * @param message 错误消息
         */
        fun fail(message: String = "处理失败"): PaymentNotificationResponse {
            return PaymentNotificationResponse(STATUS_FAIL, message)
        }
        
        /**
         * 创建成功响应（返回字符串格式，符合平台要求）
         * 根据注释，回调处理必须返回"success"字符串表示处理成功
         */
        fun successString(): String {
            return "success"
        }
        
        /**
         * 创建失败响应（返回字符串格式）
         */
        fun failString(message: String = "fail"): String {
            return message
        }
    }
    
    /**
     * 判断是否为成功响应
     */
    fun isSuccess(): Boolean {
        return status == STATUS_SUCCESS
    }
    
    /**
     * 转换为字符串响应（符合平台要求的格式）
     * 根据UnifiedOrderForm.kt中的注释，回调处理必须返回"success"字符串表示处理成功
     */
    fun toStringResponse(): String {
        return if (isSuccess()) {
            "success"
        } else {
            message.ifEmpty { "fail" }
        }
    }
} 