package org.mospital.dongruan.pay

import com.fasterxml.jackson.annotation.JsonAlias
import org.mospital.jackson.JacksonKit

class UnifiedOrderResult {

    var payInfo: String? = null

    @JsonAlias("payAppId", "payAppid")
    var payAppId: String? = null

    var insurancePayResult: String? = null

    data class WeixinPayInfo(
        val appId: String,
        val timeStamp: String,
        val nonceStr: String,
        @JsonAlias("package")
        val packageValue: String,
        val signType: String,
        val paySign: String,
    )

    fun getAsWeixinPayInfo(): WeixinPayInfo? {
        return if (payInfo == null) {
            null
        } else {
            try {
                JacksonKit.buildMapper().readValue<WeixinPayInfo>(payInfo, WeixinPayInfo::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }

}