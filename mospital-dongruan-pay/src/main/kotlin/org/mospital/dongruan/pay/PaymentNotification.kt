package org.mospital.dongruan.pay

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * 支付通知数据类
 * 用于接收东软支付平台发送的支付结果通知
 */
data class PaymentNotification(
    /** 字符编码 */
    val charset: String = "",

    /** 应用ID */
    val appId: String = "",

    /** 业务内容 */
    val bizContent: BizContent = BizContent(),

    /** 签名类型 */
    val signType: String = "",

    /** 签名 */
    @JsonProperty("Sign")
    val sign: String = "",

    /** 类型ID */
    val typeId: String = "",

    /** 附加数据 */
    val attach: String? = null
) {

    /**
     * 业务内容数据类
     * 包含支付的详细信息
     */
    data class BizContent(
        /** 现金支付金额（元） */
        val cashFee: String = "0.00",

        /** 医保支付金额（元） */
        val insuranceFee: String = "0.00",

        /** 医保统筹支付金额（元） */
        val insuranceFundFee: String = "0.00",

        /** 医保其他支付金额（元） */
        val insuranceOtherFee: String = "0.00",

        /** 医保个人账户支付金额（元） */
        val insuranceSelfFee: String = "0.00",

        /** 通知描述 */
        val notifyDesc: String = "",

        /** 通知结果 */
        val notifyResult: String = "",

        /** 通知时间，格式：yyyyMMddHHmmss，示例：20250617210549 */
        val notifyTime: String = "",

        /** 订单号 */
        val orderNo: String = "",

        /** 商户订单号 */
        val outTradeNo: String = "",

        /** 参数 */
        val params: String? = null,

        /** 支付方式类型 */
        val payWayType: String = "",

        /** 聚合订单号 */
        val polymerizationNo: String? = null,

        /** 结算日期，格式：yyyyMMddHHmmss，示例：20250617210428 */
        val settleDate: String = "",

        /** 交易描述 */
        val tradeDesc: String? = null,

        /** 交易金额（元） */
        val tradeFee: String = "0.00",

        /** 第三方交易号 */
        val tradeNo: String = "",

        /** 交易流水号 */
        val tradeSeq: String = "",

        /** 交易状态 */
        val tradeStatus: String = "",

        /** 交易时间，格式：yyyyMMddHHmmss，示例：20250617210543 */
        val tradeTime: String = "",

        /** 交易类型：1-支付，2-退款 */
        val tradeType: String = ""
    )

    /**
     * 判断是否为支付成功通知
     */
    fun isPaymentSuccess(): Boolean {
        return bizContent.notifyResult == "true" &&
                bizContent.tradeStatus == "2" &&
                bizContent.notifyDesc == "SUCCESS"
    }

    /**
     * 判断是否为退款通知
     */
    fun isRefundNotification(): Boolean {
        return bizContent.tradeType == "2"
    }

    /**
     * 获取交易金额（元）
     */
    fun getTradeAmountInYuan(): Double {
        return try {
            bizContent.tradeFee.toDouble()
        } catch (e: NumberFormatException) {
            0.0
        }
    }

    /**
     * 获取现金支付金额（元）
     */
    fun getCashAmountInYuan(): Double {
        return try {
            bizContent.cashFee.toDouble()
        } catch (e: NumberFormatException) {
            0.0
        }
    }

    /**
     * 获取医保支付金额（元）
     */
    fun getInsuranceAmountInYuan(): Double {
        return try {
            bizContent.insuranceFee.toDouble()
        } catch (e: NumberFormatException) {
            0.0
        }
    }

    /**
     * 获取通知时间（LocalDateTime）
     * 将字符串格式的通知时间转换为LocalDateTime对象
     */
    fun getNotifyDateTime(): LocalDateTime? {
        return parseDateTime(bizContent.notifyTime)
    }

    /**
     * 获取结算日期时间（LocalDateTime）
     * 将字符串格式的结算日期转换为LocalDateTime对象
     */
    fun getSettleDateTime(): LocalDateTime? {
        return parseDateTime(bizContent.settleDate)
    }

    /**
     * 获取交易时间（LocalDateTime）
     * 将字符串格式的交易时间转换为LocalDateTime对象
     */
    fun getTradeDateTime(): LocalDateTime? {
        return parseDateTime(bizContent.tradeTime)
    }

    /**
     * 解析日期时间字符串
     * @param dateTimeStr 格式为yyyyMMddHHmmss的日期时间字符串
     * @return LocalDateTime对象，解析失败时返回null
     */
    private fun parseDateTime(dateTimeStr: String): LocalDateTime? {
        return try {
            if (dateTimeStr.isNotEmpty() && dateTimeStr.length == 14) {
                LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    companion object {
        /** 日期时间格式 */
        const val DATETIME_PATTERN = "yyyyMMddHHmmss"

        /** 日期时间格式化器 */
        val DATETIME_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern(DATETIME_PATTERN)
    }
} 