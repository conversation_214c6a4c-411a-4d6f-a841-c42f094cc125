package org.mospital.dongruan.pay

import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.locks.ReentrantLock

object OrderNoGenerator {
    private val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
    private val timezone = ZoneId.of("GMT+8")
    private const val SEQUENCE_FORMAT = "%02d" // 2 位数字
    private const val PREFIX = "YQTL"
    private const val TIMESTAMP_LENGTH = 14 // yyyyMMddHHmmss
    private const val ORDER_NO_LENGTH = PREFIX.length + TIMESTAMP_LENGTH + 2
    private var lastTimestamp: String = ""
    private var sequence: AtomicInteger = AtomicInteger(0)
    private val lock = ReentrantLock()

    /**
     * 验证订单号格式是否有效
     * 仅做基本格式检查，不验证日期时间是否实际存在
     * @param orderNo 要验证的订单号
     * @return Boolean 格式是否有效
     */
    fun isValidOrderNo(orderNo: String?): Boolean {
        if (orderNo == null || orderNo.length != ORDER_NO_LENGTH) {
            return false
        }

        // 检查前缀
        if (!orderNo.startsWith(PREFIX)) {
            return false
        }

        // 检查时间戳部分（使用日期解析验证）
        try {
            val timestampStr = orderNo.substring(PREFIX.length, PREFIX.length + TIMESTAMP_LENGTH)
            dateTimeFormatter.parse(timestampStr)
        } catch (_: Exception) {
            return false
        }

        // 检查序列号部分（2位数字）
        try {
            val sequenceNum = orderNo.substring(PREFIX.length + TIMESTAMP_LENGTH).toInt()
            return sequenceNum in 0..99
        } catch (_: NumberFormatException) {
            return false
        }
    }

    private fun currentTimestamp(): String = ZonedDateTime.now(timezone).format(dateTimeFormatter)

    fun newOrderNo(): String {
        lock.lock()
        return try {
            var currentTimestamp = currentTimestamp()

            if (currentTimestamp == lastTimestamp) {
                val sequenceNo = sequence.incrementAndGet()
                if (sequenceNo > 99) {
                    // 等待直到下一秒
                    while (true) {
                        currentTimestamp = currentTimestamp()
                        if (currentTimestamp != lastTimestamp) {
                            lastTimestamp = currentTimestamp
                            sequence.set(0)
                            break
                        }
                        // 简单的主动等待，可以替换为更有效的方式
                        TimeUnit.MILLISECONDS.sleep(10)
                    }
                }
            } else {
                lastTimestamp = currentTimestamp
                sequence.set(0)
            }

            val sequenceNumber = String.format(SEQUENCE_FORMAT, sequence.get())
            "$PREFIX$currentTimestamp$sequenceNumber"
        } finally {
            lock.unlock()
        }
    }

}
