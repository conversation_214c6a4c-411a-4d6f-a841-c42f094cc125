package org.mospital.dongruan.pay

class UnifiedPayOrderQueryResult {

    /**
     * (SUCCESS)支付成功
     * (FAILURE)支付失败
     * (USERPAYING)等待用户付款
     * (SYSTEMERROR)未知异常
     * (CALL_FAILURE) 调用第三方失败，重试
     * (TRADE_FINISHED)交易结束
     * 如果返回(SYSTEMERROR)未知异常 请稍后再试。
     */
    var code: String? = null

    var income: Double? = null

    var caseFee: Double? = null

    var insuranceFee: Double? = null

    var msg: String? = null

    var tradeNo: String? = null

    var payWayType: Int? = null
}