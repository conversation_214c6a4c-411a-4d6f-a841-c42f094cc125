package org.mospital.dongruan.pay

import org.mospital.jackson.ConfigLoader

object DongruanPaySetting {

    private val config: DongruanPayConfig by lazy {
        ConfigLoader.loadConfig("dongruan-pay.yaml", DongruanPayConfig::class.java, DongruanPaySetting::class.java)
    }

    val URL: String get() = config.url
    val APPID: String get() = config.appId
    val VERSION: String get() = config.version
    val CHARSET: String get() = config.charset
    val SIGN_TYPE: String get() = config.signType // Corresponds to signType in DongruanPayConfig
    val PRIVATE_KEY: String get() = config.privateKey // Corresponds to privateKey in DongruanPayConfig

}