package org.mospital.dongruan.pay

import com.fasterxml.jackson.annotation.JsonIgnore
import org.mospital.jackson.JacksonKit
import java.math.BigDecimal
import java.math.RoundingMode

class UnifiedOrderForm(
    appid: String = DongruanPaySetting.APPID,
    method: String = "rop.trade.union.create",
    version: String = DongruanPaySetting.VERSION,
    charset: String = DongruanPaySetting.CHARSET,
    signType: String = DongruanPaySetting.SIGN_TYPE,
    @field:JsonIgnore val openid: String,
) : BaseForm(appid, method, version, charset, signType) {

    companion object {
        // 01-扫码支付 02-二维码支付 03-自助机 99-其他
        val PAY_METHOD_SCANNER = "01"
        val PAY_METHOD_QRCODE = "02"
        val PAY_METHOD_ATM = "03"
        val PAY_METHOD_OTHER = "99"

        // 01-挂号 02-处方 03-就诊卡充值 04-住院充值 99-其他
        val ORDER_GUA_HAO = "01"
        val ORDER_CHU_FANG = "02"
        val ORDER_JIU_ZHEN_KA_CHONG_ZHI = "03"
        val ORDER_ZHU_YUAN_CHONG_ZHI = "04"
        val ORDER_OTHER = "99"

        @JvmStatic
        fun newOrderNo(): String = OrderNoGenerator.newOrderNo()

        @JvmStatic 
        fun isValidOrderNo(orderNo: String?): Boolean = OrderNoGenerator.isValidOrderNo(orderNo)
    }

    var extras: String by map

    override fun sign() {
        this.extras = JacksonKit.writeValueAsString(
            mapOf(
                "openid" to openid
            )
        )
        super.sign()
    }

    fun orderNo(orderNo: String): UnifiedOrderForm {
        require(isValidOrderNo(orderNo)) { "Invalid orderNo format: $orderNo" }
        this.bizContentMap["orderNo"] = orderNo
        return this
    }

    /**
     * 1=院预交金订单, 2=普通门诊订单, 3=专科门诊挂号, 4=处方订单，8=院内卡充值订单，99=其他
     */
    fun orderType(orderType: Int): UnifiedOrderForm {
        this.bizContentMap["orderType"] = orderType
        return this
    }

    /**
     * 2=支付宝小程序，3=微信小程序，9=微信医保，10=支付宝医保，11=银联，32=微信公众号

     */
    fun payWayType(payWayType: Int): UnifiedOrderForm {
        this.bizContentMap["payWayType"] = payWayType
        return this
    }

    fun subject(subject: String): UnifiedOrderForm {
        this.bizContentMap["subject"] = subject
        return this
    }

    fun amount(amount: BigDecimal): UnifiedOrderForm {
        this.bizContentMap["amount"] = amount.setScale(2, RoundingMode.HALF_UP)
        return this
    }

    /**
     * 支付回调通知地址
     * 
     * 当支付成功时，平台会主动向商户服务器发送通知到指定的HTTP/HTTPS地址
     * 
     * @param notifyUrl 回调通知地址，例如：http://example.com/api/payment/notify
     * 
     * 注意事项：
     * - 回调处理必须返回"success"字符串表示处理成功
     * - 如果返回其他值，平台会继续重试通知
     * - 最多重试15次，超过后不再通知
     */
    fun notifyUrl(notifyUrl: String): UnifiedOrderForm {
        this.bizContentMap["notifyUrl"] = notifyUrl
        return this
    }

    fun attach(attach: String): UnifiedOrderForm {
        this.bizContentMap["attach"] = attach
        return this
    }

    fun clientIp(clientIp: String): UnifiedOrderForm {
        this.bizContentMap["spbillCreateIp"] = clientIp
        return this
    }

    fun hisBiz(hisBiz: String): UnifiedOrderForm {
        this.bizContentMap["hisBiz"] = hisBiz
        return this
    }

    fun zhuYuanChongZhiHisBiz(inpatientNo: String, inpatientSerialNo: String): UnifiedOrderForm {
        this.bizContentMap["hisBiz"] = JacksonKit.writeValueAsString(mapOf(
            "inpatientNo" to inpatientNo,
            "inpatientSeriNo" to inpatientSerialNo
        ))
        return this
    }

}