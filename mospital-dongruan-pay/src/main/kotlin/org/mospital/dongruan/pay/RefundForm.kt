package org.mospital.dongruan.pay

import java.math.BigDecimal

class RefundForm(
    appid: String = DongruanPaySetting.APPID,
    method: String = "rop.trade.refund",
    version: String = DongruanPaySetting.VERSION,
    charset: String = DongruanPaySetting.CHARSET,
    signType: String = DongruanPaySetting.SIGN_TYPE,
) : BaseForm(appid, method, version, charset, signType) {

    fun orderNo(orderNo: String): RefundForm {
        this.bizContentMap["orderNo"] = orderNo
        return this
    }

    fun tradeNo(tradeNo: String): RefundForm {
        this.bizContentMap["tradeNo"] = tradeNo
        return this
    }

    fun refundNo(refundNo: String): RefundForm {
        this.bizContentMap["batchNo"] = refundNo
        this.bizContentMap["cancelSerialNo"] = refundNo
        return this
    }

    fun refundAmount(refundAmount: BigDecimal): RefundForm {
        this.bizContentMap["refundFee"] = refundAmount
        return this
    }

    fun refundReason(refundReason: String): RefundForm {
        this.bizContentMap["tradeDesc"] = refundReason
        return this
    }
}