package org.mospital.jackson

import java.time.format.DateTimeFormatter

object DateTimeFormatters {

    @JvmStatic
    val PURE_DATE_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd")
    @JvmStatic
    val PURE_DATETIME_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
    @JvmStatic
    val PURE_DATETIME_MINUTE_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm")
    @JvmStatic
    val NORM_DATE_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    @JvmStatic
    val NORM_TIME_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss")
    @JvmStatic
    val NORM_DATETIME_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    @JvmStatic
    val NORM_DATETIME_MINUTE_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")

}
