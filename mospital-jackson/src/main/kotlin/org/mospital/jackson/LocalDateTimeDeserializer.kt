package org.mospital.jackson

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

class LocalDateTimeDeserializer : JsonDeserializer<LocalDateTime?>() {

    override fun deserialize(parser: JsonParser, context: DeserializationContext): LocalDateTime? {
        val dateTimeString = parser.text

        if (dateTimeString.isNullOrBlank()) {
            return null
        }

        // 根据字符串长度快速选择合适的格式化器
        return try {
            when (dateTimeString.length) {
                // "yyyyMMdd" 格式 - 转换为LocalDateTime，时间设为00:00:00
                8 -> LocalDate.parse(dateTimeString, DateTimeFormatters.PURE_DATE_FORMATTER).atStartOfDay()

                // "yyyy-MM-dd" 格式 - 转换为LocalDateTime，时间设为00:00:00
                10 -> LocalDate.parse(dateTimeString, DateTimeFormatters.NORM_DATE_FORMATTER).atStartOfDay()

                // "yyyyMMddHHmmss" 格式
                14 -> LocalDateTime.parse(dateTimeString, DateTimeFormatters.PURE_DATETIME_FORMATTER)

                // "yyyy-MM-dd HH:mm:ss" 格式
                19 -> LocalDateTime.parse(dateTimeString, DateTimeFormatters.NORM_DATETIME_FORMATTER)

                21 -> LocalDateTime.parse(dateTimeString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"))
                22 -> LocalDateTime.parse(dateTimeString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SS"))
                23 -> LocalDateTime.parse(dateTimeString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"))

                // 其他长度，回退到原来的循环尝试方式
                else -> parseWithAllFormatters(dateTimeString)
            }
        } catch (e: DateTimeParseException) {
            // 如果按长度选择的格式化器失败，回退到循环尝试
            parseWithAllFormatters(dateTimeString)
        }
    }

    /**
     * 回退方法：尝试所有支持的格式化器
     * 用于处理非标准长度或格式化失败的情况
     */
    private fun parseWithAllFormatters(dateTimeString: String): LocalDateTime? {
        val supportedFormatters = listOf(
            DateTimeFormatters.NORM_DATETIME_FORMATTER,
            DateTimeFormatters.PURE_DATETIME_FORMATTER,
            DateTimeFormatters.NORM_DATE_FORMATTER,
            DateTimeFormatters.PURE_DATE_FORMATTER
        )

        for (formatter in supportedFormatters) {
            try {
                return if (formatter == DateTimeFormatters.NORM_DATE_FORMATTER ||
                    formatter == DateTimeFormatters.PURE_DATE_FORMATTER
                ) {
                    // 日期格式需要转换为LocalDateTime，时间设为00:00:00
                    LocalDate.parse(dateTimeString, formatter).atStartOfDay()
                } else {
                    // 日期时间格式直接解析
                    LocalDateTime.parse(dateTimeString, formatter)
                }
            } catch (_: DateTimeParseException) {
                // 继续尝试下一个格式
                continue
            }
        }

        // 所有格式都失败时返回null
        return null
    }
}
