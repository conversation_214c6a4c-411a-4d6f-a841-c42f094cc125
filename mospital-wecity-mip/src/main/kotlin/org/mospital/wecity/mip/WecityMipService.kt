package org.mospital.wecity.mip

import kotlinx.coroutines.runBlocking
import org.dromara.hutool.crypto.digest.MD5
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Path

@Suppress("unused")
interface WecityMipService {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(WecityMipService::class.java)
        private val serviceCache = mutableMapOf<String, WecityMipService>()

        private fun createService(setting: WeixinMipSetting): WecityMipService {
            val httpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = 5_000,
                readTimeout = 10_000,
                useUuidRequestId = true
            ) {
                it.retryOnConnectionFailure(false)
                it.addInterceptor(WecityMipInterceptor(setting))
            }

            val retrofit = Retrofit.Builder()
                .baseUrl(setting.url)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(JacksonKit.buildMapper()))
                .build()
            return retrofit.create(WecityMipService::class.java)
        }

        @Synchronized
        private fun getService(setting: WeixinMipSetting): WecityMipService {
            val cacheKey = "${setting.javaClass.simpleName}_${setting.url}_${setting.hashCode()}"
            return serviceCache.getOrPut(cacheKey) { createService(setting) }
        }

        val ma: WecityMipService by lazy {
            getService(WeixinMipSetting.ma)
        }

        val mp: WecityMipService by lazy {
            getService(WeixinMipSetting.mp)
        }

        fun getInstance(clientType: ClientType): WecityMipService {
            return when (clientType) {
                ClientType.MA -> ma
                ClientType.MP -> mp
            }
        }

        fun queryUser(qrcode: String, openid: String, clientType: ClientType = ClientType.MA): UserQueryResult =
            runBlocking {
                getInstance(clientType).queryUser(
                    partnerId = WeixinMipSetting.getInstance(clientType).partnerId,
                    request = UserQueryRequest(
                        qrcode = qrcode,
                        openid = openid
                    )
                )
            }

        /**
         * 构建授权链接
         * @param clientType 客户端类型, ma=小程序，mp=公众号
         * @param accountType 账号类型, self=本人医保，family=亲属医保
         */
        fun buildAuthUrl(
            clientType: ClientType,
            accountType: String,
            userName: String? = null,
            userCardNo: String? = null
        ): String {
            if ("family".equals(accountType, true)) {
                require(userName.isNullOrBlank().not() && userCardNo.isNullOrBlank().not()) {
                    "亲属医保必须传入userName和userCardNo"
                }

                val familyId = MD5.of().digestHex(userName + userCardNo.takeLast(4))

                if (clientType == ClientType.MA) {
                    val setting = WeixinMipSetting.ma
                    return "auth/pages/bindcard/auth/index?openType=getAuthCode&cityCode=${setting.cityId}&channel=${setting.channelNo}&familyId=${familyId}&orgChnlCrtfCodg=${setting.orgChannelCode}&orgCodg=${setting.orgCode}&bizType=04107&orgAppId=${setting.orgAppId}"
                }
                if (clientType == ClientType.MP) {
                    val setting = WeixinMipSetting.mp
                    val url = if (setting.isProd()) {
                        "https://card.wecity.qq.com/oauth/code"
                    } else {
                        "https://exp.wecity.qq.com/oauth/code"
                    }
                    return "$url?authType=2&isDepart=2&appid=${setting.appId}&cityCode=${setting.cityId}&channel=${setting.channelNo}&familyId=${familyId}&orgChnlCrtfCodg=${setting.orgChannelCode}&orgCodg=${setting.orgCode}&bizType=04107&orgAppId=${setting.orgAppId}"
                }
            }

            if ("self".equals(accountType, true)) {
                if (clientType == ClientType.MA) {
                    val setting = WeixinMipSetting.ma
                    return "auth/pages/bindcard/auth/index?openType=getAuthCode&cityCode=${setting.cityId}&channel=${setting.channelNo}&orgChnlCrtfCodg=${setting.orgChannelCode}&orgCodg=${setting.orgCode}&bizType=04107&orgAppId=${setting.orgAppId}"
                }
                if (clientType == ClientType.MP) {
                    val setting = WeixinMipSetting.mp
                    val url = if (setting.isProd()) {
                        "https://card.wecity.qq.com/oauth/code"
                    } else {
                        "https://exp.wecity.qq.com/oauth/code"
                    }
                    return "$url?authType=2&isDepart=2&appid=${setting.appId}&cityCode=${setting.cityId}&channel=${setting.channelNo}&orgChnlCrtfCodg=${setting.orgChannelCode}&orgCodg=${setting.orgCode}&bizType=04107&orgAppId=${setting.orgAppId}"
                }
            }

            throw IllegalArgumentException("未知的账号类型")
        }
    }

    @POST("api/mipuserquery/userQuery/{partnerId}")
    suspend fun queryUser(@Path("partnerId") partnerId: String, @Body request: UserQueryRequest): UserQueryResult

}
