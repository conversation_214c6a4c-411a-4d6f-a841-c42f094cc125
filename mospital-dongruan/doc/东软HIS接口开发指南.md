# 东软HIS接口开发指南

本文档旨在指导开发人员如何开发新的东软HIS接口。基于现有接口实现的分析，总结出以下开发步骤和最佳实践。

## 开发步骤

### 1. 创建请求类（Request）

1. 在 `org.mospital.dongruan.bean` 包下创建请求类
2. 类名以 `Request` 结尾
3. 继承自基础 `Request` 类
4. 添加必要的注解：
   ```kotlin
   @XmlRootElement(name = "Request")
   @XmlAccessorType(XmlAccessType.FIELD)
   ```
5. 定义请求参数字段，使用 `@XmlElement` 注解指定XML元素名
6. 如果有日期类型字段，使用 `@XmlJavaTypeAdapter` 注解进行转换
7. 提供无参构造函数和带参构造函数

示例：
```kotlin
@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class MedicineOrderRequest() : Request() {
    @field:XmlElement(name = "CardNO")
    var cardNO: String = ""
    
    // 带参构造函数
    constructor(cardNO: String) : this() {
        this.cardNO = cardNO
    }
}
```

### 2. 创建响应类（Response）

1. 在 `org.mospital.dongruan.bean` 包下创建响应类
2. 类名以 `Response` 结尾
3. 继承自基础 `Response` 类
4. 添加必要的注解：
   ```kotlin
   @XmlRootElement(name = "Response")
   @XmlAccessorType(XmlAccessType.FIELD)
   ```
5. 定义响应字段，使用 `@XmlElement` 注解指定XML元素名
6. 如果响应包含复杂对象，创建内部类来表示
7. 为复杂类型添加适当的注解和字段说明

示例：
```kotlin
@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class HospitalCardResponse : Response() {
    @field:XmlElement(name = "CardNO")
    var cardNo: String = ""
    
    /**
     * 字段说明
     */
    @field:XmlElement(name = "Balance")
    var balance: String = ""
}
```

### 3. 在 DongruanService 中添加服务方法

1. 在 `DongruanService` 对象中添加新的方法
2. 方法名应清晰表达功能
3. 定义适当的参数
4. 创建请求对象
5. 调用 `sendRequest` 方法发送请求，指定：
   - code: 接口代码
   - xml: 请求对象的XML表示
   - testMode: 是否使用测试模式
6. 使用 `JAXBKit.unmarshal` 解析响应XML

示例：
```kotlin
fun queryMedicineOrder(request: MedicineOrderRequest, testMode: Boolean = false): MedicineOrderResponse {
    val responseXml: String = sendRequest(code = "1030", xml = request.toXML(), testMode = testMode)
    return JAXBKit.unmarshal(MedicineOrderResponse::class.java, responseXml)
}
```

## 最佳实践

1. **命名规范**
   - 类名使用驼峰命名法
   - XML元素名保持与接口文档一致
   - 方法名应体现业务含义

2. **注释规范**
   - 为重要字段添加注释说明
   - 说明字段的业务含义和可能的值

3. **类型转换**
   - 日期类型使用 `LocalDateJaxbAdapter`
   - 枚举类型使用自定义 `XmlAdapter`

4. **测试模式支持**
   - 在需要支持测试的接口中添加 `testMode` 参数
   - 将接口代码添加到 `testModeSupportedCodes` 集合中

5. **错误处理**
   - 继承自 `Response` 类自动包含错误处理
   - 使用 `isOk()` 方法检查响应状态

## 注意事项

1. 确保请求和响应类的XML结构与接口文档一致
2. 正确处理可选字段和必填字段
3. 注意字段类型的转换和格式化
4. 测试接口在正式环境和测试环境的表现
5. 遵循现有代码的风格和约定

## 示例代码

参考 `queryMedicineOrder` 和 `queryHospitalCard` 的完整实现：
- [MedicineOrderRequest.kt](../src/main/kotlin/org/mospital/dongruan/bean/MedicineOrderRequest.kt)
- [MedicineOrderResponse.kt](../src/main/kotlin/org/mospital/dongruan/bean/MedicineOrderResponse.kt)
- [HospitalCardRequest.kt](../src/main/kotlin/org/mospital/dongruan/bean/HospitalCardRequest.kt)
- [HospitalCardResponse.kt](../src/main/kotlin/org/mospital/dongruan/bean/HospitalCardResponse.kt)
