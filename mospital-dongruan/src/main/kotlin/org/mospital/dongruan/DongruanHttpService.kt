package org.mospital.dongruan

import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

/**
 * 东软HIS HTTP服务接口
 * 使用Retrofit定义HTTP API接口
 */
interface DongruanHttpService {

    /**
     * 处理SOAP请求
     * @param requestBody SOAP envelope请求体
     * @return SOAP响应体
     */
    @POST("/xcx/Service/op/Process")
    @Headers(
        "Content-Type: text/xml;charset=UTF-8",
        "SOAPAction: http://tempuri.org/Process"
    )
    fun process(@Body requestBody: RequestBody): retrofit2.Call<ResponseBody>
} 