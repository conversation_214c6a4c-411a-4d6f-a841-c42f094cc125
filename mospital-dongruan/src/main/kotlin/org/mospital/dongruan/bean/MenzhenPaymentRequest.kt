package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenPaymentRequest() : Request() {

    /**
     * 门诊流水号
     */
    @field:XmlElement(name = "ClinicNO")
    var clinicNo: String = ""

    /**
     * 处方号，多个处方号用'|'分隔
     */
    @field:XmlElement(name = "RecipeNO")
    var recipeNo: String = ""

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    constructor(clinicNo: String, recipeNo: String, transModel: TransModel) : this() {
        this.clinicNo = clinicNo
        this.recipeNo = recipeNo
        this.transModel = transModel
    }

}