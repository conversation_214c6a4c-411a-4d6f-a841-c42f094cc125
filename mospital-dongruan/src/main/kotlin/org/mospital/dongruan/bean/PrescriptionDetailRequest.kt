package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class PrescriptionDetailRequest() : Request() {

    @field:XmlElement(name = "ClinicNO")
    var clinicNo: String = ""

    @field:XmlElement(name = "RecipeNO")
    var prescriptionNo: String = ""

    constructor(clinicNo: String, prescriptionNo: String) : this() {
        this.clinicNo = clinicNo
        this.prescriptionNo = prescriptionNo
    }

}
