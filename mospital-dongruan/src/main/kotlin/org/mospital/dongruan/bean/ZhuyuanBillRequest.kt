package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanBillRequest() : Request() {

    @field:XmlElement(name = "InpatientNo")
    var inPatientNo: String = ""

    constructor(inPatientNo: String) : this() {
        this.inPatientNo = inPatientNo
    }

}
