package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class HospitalCardResponse : Response() {

    @field:XmlElement(name = "MarkNO")
    var markNo: String = ""

    /**
     * 1=实体卡,5=电子健康卡
     */
    @field:XmlElement(name = "MarkType")
    var markType: String = ""

    @field:XmlElement(name = "AccountNO")
    var accountNo: String = ""

    @field:XmlElement(name = "CardNO")
    var cardNo: String = ""

    @field:XmlElement(name = "ErhcCardNo")
    var erhcCardNo: String = ""

    @field:XmlElement(name = "Name")
    var name: String = ""

    @field:XmlElement(name = "Sex")
    @field:XmlJavaTypeAdapter(SexEnum.XmlAdaptor::class)
    var sex: SexEnum = SexEnum.NOT_GIVEN

    @field:XmlElement(name = "IDNO")
    var idCardNo: String = ""

    @field:XmlElement(name = "Vacancy")
    var balance: String = ""

    @field:XmlElement(name = "PhoneNum")
    var mobile: String = ""

    @field:XmlElement(name = "IsDonationPerson")
    var donationPersonFlag: String = ""

    val isDonationPerson: Boolean
        get() = donationPersonFlag == "1"

}
