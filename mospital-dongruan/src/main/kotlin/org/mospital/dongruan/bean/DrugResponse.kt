package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class DrugResponse: Response() {

    @field:XmlElement(name = "DrugItemList")
    var drugs: MutableList<Drug> = mutableListOf()

    @XmlRootElement(name = "DrugItemList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Drug {

        @field:XmlElement(name = "DrugCode")
        var code: String = ""

         @field:XmlElement(name = "TradeName")
        var name: String = ""

         @field:XmlElement(name = "FeeName")
        var feeType: String = ""

         @field:XmlElement(name = "Specs")
        var spec: String = ""

         @field:XmlElement(name = "Price")
        var price: String = ""

    }

}