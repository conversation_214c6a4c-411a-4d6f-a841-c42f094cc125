package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.dongruan.DongruanConfig

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class ReserveRequest(): Request() {

    /**
     * 就诊卡号或身份证号
     */
    @field:XmlElement(name = "CardNO")
    var cardNo: String = ""

    /**
     * 排班流水号
     */
    @field:XmlElement(name = "SchemaID")
    var schid: String = ""

    /**
     * 挂号模式：0=现场，1=预约
     * 排班为当前午别时，传0，否则传1
     */
    @field:XmlElement(name = "RegType")
    var regType: Int = 1

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    @field:XmlElement(name = "BookIndexID")
    var bookIndexId: String = ""

    constructor(cardNo: String, schid: String, regType: Int, bankTransNo: String, bookIndexId: String = ""): this() {
        this.cardNo = cardNo
        this.schid = schid
        this.regType = regType
        this.transModel = TransModel(bankTransNo)
        this.bookIndexId = bookIndexId
    }

    @XmlRootElement(name = "TransModel")
    @XmlAccessorType(XmlAccessType.FIELD)
    class TransModel() {

        /**
         * 自助机编号
         */
        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = DongruanConfig.me.operator

        /**
         * 交易流水号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        constructor(bankTransNo: String) : this() {
            this.bankTransNo = bankTransNo
        }
    }
}
