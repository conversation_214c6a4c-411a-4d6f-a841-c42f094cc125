@file:Suppress("unused")

package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class DoctorResponse : Response() {

    @field:XmlElement(name = "DoctList")
    var doctors: MutableList<Doctor> = mutableListOf()

    @XmlRootElement(name = "DoctList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Doctor {

        @field:XmlElement(name = "DoctCode")
        var code: String = ""

        @field:XmlElement(name = "DoctName")
        var name: String = ""

        @field:XmlElement(name = "DeptCode")
        var departmentCode: String = ""

        @field:XmlElement(name = "DoctSex")
        @field:XmlJavaTypeAdapter(SexEnum.XmlAdaptor::class)
        var sex: SexEnum = SexEnum.NOT_GIVEN

        @field:XmlElement(name = "DoctLevlName")
        var title: String = ""

        @field:XmlElement(name = "DoctEduName")
        var education: String = ""

        @field:XmlElement(name = "DoctSpecial")
        var expertise: String = ""

        @field:XmlElement(name = "DoctIntroduce")
        var intro: String = ""

        @field:XmlElement(name = "DoctPacture")
        var photo: String = ""

        /**
         * 专家号标志：1=专家号，0=普通号
         */
        @field:XmlElement(name = "Flag")
        var expertFlag: String = ""

        /**
         * 是否专家号
         */
        fun isExpert(): Boolean = expertFlag == "1"

    }

}
