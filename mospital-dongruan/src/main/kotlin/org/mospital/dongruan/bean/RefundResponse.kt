package org.mospital.dongruan.bean

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class RefundResponse : Response() {

    /**
     * 门诊余额
     */
    @field:XmlElement(name = "Vacancy")
    var balance: BigDecimal = BigDecimal.ZERO

}
