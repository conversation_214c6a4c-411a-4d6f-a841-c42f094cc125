package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

import org.mospital.common.jaxb.LocalDateJaxbAdapter
import org.mospital.common.jaxb.LocalDateTimeJaxbAdapter
import java.time.LocalDate
import java.time.LocalDateTime

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanPatientResponse : Response() {

    @field:XmlElement(name = "InpatientNo")
    var zhuyuanNo: String = ""

    @field:XmlElement(name = "InDate")
    @field:XmlJavaTypeAdapter(LocalDateTimeJaxbAdapter::class)
    var ruyuanTime: LocalDateTime? = null

    @field:XmlElement(name = "DeptName")
    var departmentName: String = ""

    @field:XmlElement(name = "BedNo")
    var bedNo: String = ""

    /**
     * 住院预交金合计
     */
    @field:XmlElement(name = "PrePayCost")
    var prepayCost: String = ""

    /**
     * 住院花费总金额
     */
    @field:XmlElement(name = "TotCost")
    var totalCost: String = ""

    @field:XmlElement(name = "InState")
    var zhuyuanState: String = ""

    @field:XmlElement(name = "PatientInfo")
    var patientInfo: ZhuyuanPatientInfo = ZhuyuanPatientInfo()

    @XmlRootElement(name = "PatientInfo")
    @XmlAccessorType(XmlAccessType.FIELD)
    class ZhuyuanPatientInfo() {

        @field:XmlElement(name = "Name")
        var name: String = ""

        @field:XmlElement(name = "Sex")
        @field:XmlJavaTypeAdapter(SexEnum.XmlAdaptor::class)
        var sex: SexEnum = SexEnum.NOT_GIVEN

        @field:XmlElement(name = "IDNO")
        var idCardNo: String = ""

        @field:XmlElement(name = "BirthDay")
        @field:XmlJavaTypeAdapter(LocalDateJaxbAdapter::class)
        var birthday: LocalDate? = null

        @field:XmlElement(name = "Phone")
        var mobile: String = ""

        @field:XmlElement(name = "Address")
        var address: String = ""
    }

}
