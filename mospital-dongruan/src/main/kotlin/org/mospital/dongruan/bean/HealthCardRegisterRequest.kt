package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.dongruan.DongruanConfig

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class HealthCardRegisterRequest(): Request() {

    @field:XmlElement(name = "Name")
    var name: String = ""

    @field:XmlElement(name = "Phone")
    var phone: String = ""

    @field:XmlElement(name = "IdNo")
    var idCardNo: String = ""

    @field:XmlElement(name = "Nation")
    var nation: String = ""

    /**
     * 性别：1=男,2=女,9=未知
     */
    @field:XmlElement(name = "Sex")
    var sex: String = ""

    @field:XmlElement(name = "Address")
    var address: String = ""

    /**
     * 伊犁友谊医院：传入健康卡卡号，参见 org.mospital.erhc.ylz.RegisterOrQuery.erhcCardNo
     * 新源县人民医院：传入健康卡关联卡号，参见 org.mospital.erhc.ylz.RegisterOrQuery.cardNo
     */
    @field:XmlElement(name = "MarkNo")
    var markNo: String = ""

    /**
     * 伊犁友谊医院：传入空字符串
     * 新源县人民医院：传入健康卡卡号，参见 org.mospital.erhc.ylz.RegisterOrQuery.erhcCardNo
     */
    @field:XmlElement(name = "QrCode")
    var qrCode: String = ""

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    @XmlRootElement(name = "TransModel")
    @XmlAccessorType(XmlAccessType.FIELD)
    class TransModel() {

        /**
         * 自助机编号
         */
        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = DongruanConfig.me.operator

        /**
         * 交易流水号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        constructor(bankTransNo: String) : this() {
            this.bankTransNo = bankTransNo
        }
    }

}
