package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenPatientsRequest(): Request() {

    /**
     * 姓名
     */
    @field:XmlElement(name = "Name")
    var name: String = ""

    /**
     * 查询类型：1=病历号，2=身份证号，3=物理卡号
     */
    @field:XmlElement(name = "QueryType")
    var queryType: String = ""

    /**
     * 查询类型值
     */
    @field:XmlElement(name = "QueryValue")
    var queryValue: String = ""

    constructor(name: String, queryType: String, queryValue: String): this() {
        this.name = name
        this.queryType = queryType
        this.queryValue = queryValue
    }

}
