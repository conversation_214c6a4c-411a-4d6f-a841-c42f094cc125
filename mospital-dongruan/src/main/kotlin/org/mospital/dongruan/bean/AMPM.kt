package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.adapters.XmlAdapter

enum class AMPM(val code: Int) {

    AM(1), PM(2), EVENING(3), OTHER(9);

    companion object {
        private val codeMap: Map<String, AMPM> = values().associateBy { it.code.toString() }
        fun fromCode(code: String): AMPM = codeMap[code] ?: OTHER
    }

    class XmlAdaptor : XmlAdapter<String, AMPM>() {
        override fun marshal(v: AMPM?): String = (v ?: OTHER).code.toString()
        override fun unmarshal(v: String?): AMPM = fromCode(v ?: "")
    }

}
