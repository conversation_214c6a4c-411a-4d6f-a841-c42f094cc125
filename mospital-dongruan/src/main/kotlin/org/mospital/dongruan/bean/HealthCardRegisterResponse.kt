package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class HealthCardRegisterResponse: Response() {

    @field:XmlElement(name = "MarkNo")
    var markNo: String = ""

    @field:XmlElement(name = "CardNo")
    var cardNo: String = ""

}
