package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class MaterialRequest(): Request() {

    /**
     * 材料名称或拼音码
     * 如果使用拼音码，必须是大写
     */
    @field:XmlElement(name = "ItemNameOrSpellCode")
    var code: String = ""

    constructor(code: String) : this() {
        this.code = code
    }

}