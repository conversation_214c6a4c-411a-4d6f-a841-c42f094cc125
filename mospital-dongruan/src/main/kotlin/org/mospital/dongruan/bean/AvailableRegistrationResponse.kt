package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@Suppress("unused")
@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class AvailableRegistrationResponse : Response() {

    @field:XmlElement(name = "RegList")
    var registrations: MutableList<Registration> = mutableListOf()

    @XmlRootElement(name = "RegList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Registration {
        /**
         * 门诊流水号
         */
        @field:XmlElement(name = "ClinicNO")
        var clinicNo: String = ""

        /**
         * 科室名称
         */
        @field:XmlElement(name = "DeptName")
        var departmentName: String = ""

        /**
         * 医生姓名
         */
        @field:XmlElement(name = "DoctName")
        var doctorName: String = ""

        /**
         * 挂号时间
         */
        @field:XmlElement(name = "RegDate")
        var registerTime: String = ""

        /**
         * 预约号标志：1=预约号，0=非预约号
         */
        @field:XmlElement(name = "BookFlag")
        var bookFlag: Int = 0

        /**
         * 科室位置
         */
        @field:XmlElement(name = "DeptAddress")
        var deptAddress: String = ""

        /**
         * List representing the prescriptions associated with a particular registration.
         */
        var prescriptions: MutableList<PrescriptionResponse.Prescription> = mutableListOf()
    }
}
