package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.dongruan.DongruanConfig

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class HealthCardRegisterRequestV2(): Request() {

    @field:XmlElement(name = "Name")
    var name: String = ""

    @field:XmlElement(name = "Phone")
    var phone: String = ""

    @field:XmlElement(name = "IdNo")
    var idCardNo: String = ""

    @field:XmlElement(name = "Nation")
    var nation: String = ""

    /**
     * 性别：1=男,2=女,9=未知
     */
    @field:XmlElement(name = "Sex")
    var sex: String = ""

    @field:XmlElement(name = "Address")
    var address: String = ""

    /**
     * 伊犁友谊医院：传入健康卡卡号，参见 org.mospital.erhc.ylz.RegisterOrQuery.erhcCardNo
     * 新源县人民医院：传入健康卡关联卡号，参见 org.mospital.erhc.ylz.RegisterOrQuery.cardNo
     */
    @field:XmlElement(name = "MarkNo")
    var markNo: String = ""

    @field:XmlElement(name = "BirthDay")
    var birthday: String = ""

    /**
     * 婴儿标记：0=否,1=是
     */
    @field:XmlElement(name = "BabyFlag")
    var babyFlag: Int = 0

    /**
     * 监护人身份证号
     */
    @field:XmlElement(name = "JhrIdNo")
    var jhrIdNo: String = ""

    /**
     * 监护人姓名
     */
    @field:XmlElement(name = "JhrName")
    var jhrName: String = ""

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    @XmlRootElement(name = "TransModel")
    @XmlAccessorType(XmlAccessType.FIELD)
    class TransModel() {

        /**
         * 自助机编号
         */
        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = DongruanConfig.me.operator

        /**
         * 交易流水号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        constructor(bankTransNo: String) : this() {
            this.bankTransNo = bankTransNo
        }
    }

}
