package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter
import org.mospital.dongruan.DongruanConfig
import org.mospital.jackson.DateTimeFormatters
import java.math.BigDecimal
import java.time.LocalDateTime

@Suppress("unused")
@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class ReserveRequestV1() : Request() {

    /**
     * 就诊卡号或身份证号
     */
    @field:XmlElement(name = "CardNO")
    var cardNo: String = ""

    /**
     * 排班流水号
     */
    @field:XmlElement(name = "SchemaID")
    var schid: String = ""

    /**
     * 挂号模式：0=现场，1=预约
     * 排班为当前午别时，传0，否则传1
     */
    @field:XmlElement(name = "RegType")
    var regType: Int = 1

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    @field:XmlElement(name = "BookIndexID")
    var bookIndexId: String = ""

    /**
     * 是否医保挂号: 0=否，1=是
     */
    @field:XmlElement(name = "IsYBHoldOnBooking")
    var isYiBaoPay: Int = 0

    constructor(
        cardNo: String,
        schid: String,
        regType: Int,
        bookIndexId: String = "",
        isYiBaoPay: Boolean = false,
        transModel: TransModel
    ) : this() {
        this.cardNo = cardNo
        this.schid = schid
        this.regType = regType
        this.bookIndexId = bookIndexId
        this.isYiBaoPay = if (isYiBaoPay) 1 else 0
        this.transModel = transModel
    }

    @XmlRootElement(name = "TransModel")
    @XmlAccessorType(XmlAccessType.FIELD)
    class TransModel() {

        /**
         * 银行名称
         */
        @field:XmlElement(name = "BankName")
        var bankName: String = ""

        /**
         * 银行卡号
         */
        @field:XmlElement(name = "BankAccountNO")
        var bankAccountNo: String = ""

        /**
         * 自助机编号
         */
        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = DongruanConfig.me.operator

        /**
         * 交易流水号，建议传入商户订单号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        /**
         * 付款方式：JHWX=微信，JHZFB=支付宝
         */
        @field:XmlElement(name = "BankPayType")
        var bankPayType: String = ""

        /**
         * 交易金额，最小充值10元，最大充值10000元
         */
        @field:XmlElement(name = "BankPayCost")
        var bankPayCost: BigDecimal = BigDecimal.ZERO

        @field:XmlElement(name = "BankInvoiceNo")
        var bankInvoiceNo: String = ""

        @field:XmlElement(name = "BankAuthNo")
        var bankAuthNo: String = ""

        @field:XmlElement(name = "BankTraceNo")
        var bankTraceNo: String = ""

        @field:XmlElement(name = "BankBatchNo")
        var bankBatchNo: String = ""

        @field:XmlElement(name = "BankTranDate")
        var bankTranDate: String = ""

        @field:XmlElement(name = "BankRefNo")
        var bankRefNo: String = ""

        @field:XmlElement(name = "BankScanCode")
        var bankScanCode: String = ""

        @field:XmlElement(name = "BankMid")
        var bankMid: String = ""

        @field:XmlElement(name = "BankTid")
        var bankTid: String = ""

        @field:XmlElement(name = "BankSMFTradeNo")
        var bankSmfTradeNo: String = ""

        @field:XmlElement(name = "ThirdPayType")
        @field:XmlJavaTypeAdapter(ThirdPayType.XmlAdaptor::class)
        var thirdPayType: ThirdPayType = ThirdPayType.WeChatPay

        constructor(
            outTradeNo: String,
            transactionId: String,
            bankPayType: String,
            bankPayCost: BigDecimal,
            bankPayTime: LocalDateTime,
            thirdPayType: ThirdPayType
        ) : this() {
            this.bankTransNo = outTradeNo
            this.bankTraceNo = outTradeNo
            this.bankSmfTradeNo = transactionId
            this.bankPayType = bankPayType
            this.bankPayCost = bankPayCost
            this.bankTranDate = bankPayTime.format(DateTimeFormatters.PURE_DATETIME_FORMATTER)
            this.thirdPayType = thirdPayType
        }
    }
}