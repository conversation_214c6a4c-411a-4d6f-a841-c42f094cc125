package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanPatientRequest() : Request() {

    @field:XmlElement(name = "IDNO")
    var idNo: String = ""

    /**
     * 查询模式：1=身份证号，2=住院号
     */
    @field:XmlElement(name = "MODE")
    var mode: Int = 1

    constructor(idNo: String, mode: Int) : this() {
        // 住院号只取末10位
        this.idNo = if (mode == 1) idNo else idNo.takeLast(10)
        this.mode = mode
    }

}
