package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class DepartmentRequest() : Request() {

    /**
     * 为空时查询一级科室
     */
    @field:XmlElement(name = "DeptType")
    var type: String = ""

    constructor(type: String) : this() {
        this.type = type
    }

}
