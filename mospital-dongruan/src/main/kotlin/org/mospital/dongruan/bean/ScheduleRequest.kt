package org.mospital.dongruan.bean

import org.mospital.common.jaxb.LocalDateJaxbAdapter
import java.time.LocalDate
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class ScheduleRequest() : Request() {

    /**
     * 科室编码
     */
    @field:XmlElement(name = "DeptCode")
    var deptCode: String = ""

    /**
     * 医生编码
     */
    @field:XmlElement(name = "DoctCode")
    var doctCode: String = ""

    /**
     * 排班日期
     */
    @field:XmlJavaTypeAdapter(LocalDateJaxbAdapter::class)
    @field:XmlElement(name = "SeeDate")
    var date: LocalDate = LocalDate.now()

    constructor(deptCode: String, doctCode: String, date: LocalDate): this() {
        this.deptCode = deptCode
        this.doctCode = doctCode
        this.date = date
    }

}
