package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class ScheduleItemRequest() : Request() {

    /**
     * 科室编码
     */
    @field:XmlElement(name = "SchemaID")
    var schemaId: String = ""

    constructor(schemaId: String) : this() {
        this.schemaId = schemaId
    }

}