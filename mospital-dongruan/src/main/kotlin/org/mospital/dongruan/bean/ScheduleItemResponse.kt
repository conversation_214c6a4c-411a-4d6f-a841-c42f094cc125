package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class ScheduleItemResponse : Response() {

    @field:XmlElement(name = "ArrangeDetail")
    var scheduleItems: MutableList<ScheduleItem> = mutableListOf()

    @XmlRootElement(name = "ArrangeDetail")
    @XmlAccessorType(XmlAccessType.FIELD)
    class ScheduleItem {

        @field:XmlElement(name = "SchemaID")
        var schid: String = ""

        @field:XmlElement(name = "BookIndexID")
        var bookIndexId: String = ""

        @field:XmlElement(name = "BookTime")
        var bookTime: String = ""

        /**
         * 状态：0=未预约，1=已预约
         */
        @field:XmlElement(name = "BookSate")
        var bookState: String = ""

        @field:XmlElement(name = "BookSeeNo")
        var bookSeeNo: String = ""

        val isAvailable: Boolean
            get() = bookState == "0"
    }

}