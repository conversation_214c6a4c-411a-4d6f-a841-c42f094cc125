package org.mospital.dongruan.bean

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter
import java.math.BigDecimal

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class ScheduleResponseV1 : Response() {

    @field:XmlElement(name = "ArrangeList")
    var schedules: MutableList<Schedule> = mutableListOf()

    @XmlRootElement(name = "ArrangeList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Schedule {

        @field:XmlElement(name = "SchemaID")
        var schid: String = ""

        @field:XmlElement(name = "DoctCode")
        var doctorCode: String = ""

        var doctorName: String = ""

        @field:XmlElement(name = "DeptCode")
        var departmentCode: String = ""

        @field:XmlElement(name = "DeptName")
        var departmentName: String = ""

        @field:XmlElement(name = "RegLeveId")
        var regTypeCode: String = ""

        @field:XmlElement(name = "RegLeveName")
        var regType: String = ""

        @field:XmlElement(name = "SeeDate")
        var date: String = ""

        @field:XmlElement(name = "NoonCode")
        @field:XmlJavaTypeAdapter(DayPeriod.XmlAdaptor::class)
        var dayPeriod: DayPeriod = DayPeriod.OTHER

        @field:XmlElement(name = "RegFee")
        var fee: BigDecimal? = null

        /**
         * 挂号限额
         */
        @field:XmlElement(name = "RegLmt")
        var guahaoLimit: Int = -1

        /**
         * 已挂号数量
         */
        @field:XmlElement(name = "Reged")
        var guahaoCount: Int? = null

        /**
         * 预约限额
         */
        @field:XmlElement(name = "TelLmt")
        var yuyueLimit: Int = -1

        /**
         * 已预约数量
         */
        @field:XmlElement(name = "Teled")
        var yuyueCount: Int? = null

        /**
         * 1=专家；0=普通
         */
        @field:XmlElement(name = "Flag")
        var type: String = ""

        /**
         * 老年人挂号费
         */
        @field:XmlElement(name = "OldPersonRegFee")
        var oldPersonRegFee: BigDecimal? = null

        /**
         * 医学捐献者挂号费
         */
        @field:XmlElement(name = "DonationPersonRegFee")
        var donationPersonRegFee: BigDecimal? = null

        val remainingCount: Int
            get() {
                val today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE)
                return if (date == today) {
                    guahaoLimit - (guahaoCount ?: 0)
                } else if (date > today) {
                    yuyueLimit - (yuyueCount ?: 0)
                } else {
                    0
                }
            }

        fun hasRemaining(): Boolean = remainingCount > 0

    }

}
