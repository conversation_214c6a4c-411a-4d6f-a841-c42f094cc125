package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@Suppress("unused")
@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class AvailableRegistrationResponseV1 : Response() {

    @field:XmlElement(name = "RegList")
    var registrations: MutableList<Registration> = mutableListOf()

    @XmlRootElement(name = "RegList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Registration {
        /**
         * 门诊流水号
         */
        @field:XmlElement(name = "ClinicNO")
        var clinicNo: String = ""

        /**
         * 科室名称
         */
        @field:XmlElement(name = "DeptName")
        var departmentName: String = ""

        /**
         * 医生姓名
         */
        @field:XmlElement(name = "DoctName")
        var doctorName: String = ""

        /**
         * 挂号时间
         */
        @field:XmlElement(name = "RegDate")
        var registerTime: String = ""

        /**
         * 预约号标志：1=预约号，0=非预约号
         */
        @field:XmlElement(name = "BookFlag")
        var bookFlag: Int = 0

        /**
         * 科室位置
         */
        @field:XmlElement(name = "DeptAddress")
        var deptAddress: String = ""

        /**
         * 看诊标志：已诊、未诊
         */
        @field:XmlElement(name = "SeeFlag")
        var seeFlag: String = ""

        /**
         * 支付订单号
         */
        @field:XmlElement(name = "OrderId")
        var orderId: String = ""

        /**
         * 支付方式
         */
        @field:XmlElement(name = "PayType")
        var payType: String = ""

        /**
         * 支付金额
         */
        @field:XmlElement(name = "RegCost")
        var regCost: String = ""

        /**
         * 午别
         */
        @field:XmlElement(name = "Noon")
        var noon: String = ""

        @field:XmlElement(name = "SchemaID")
        var schemaId: String = ""

        /**
         * 额外信息
         */
        var extra: MutableMap<String, Any> = mutableMapOf()

        /**
         * List representing the prescriptions associated with a particular registration.
         */
        var prescriptions: MutableList<PrescriptionResponse.Prescription> = mutableListOf()
    }
}
