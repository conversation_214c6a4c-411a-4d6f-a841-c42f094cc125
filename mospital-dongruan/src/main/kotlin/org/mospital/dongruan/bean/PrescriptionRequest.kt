package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class PrescriptionRequest() : Request() {

    @field:XmlElement(name = "ClinicNO")
    var clinicNo: String = ""

    constructor(clinicNo: String) : this() {
        this.clinicNo = clinicNo
    }

}
