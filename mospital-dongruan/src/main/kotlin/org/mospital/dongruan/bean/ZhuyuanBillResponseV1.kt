package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import java.math.BigDecimal

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanBillResponseV1: Response() {

    @field:XmlElement(name = "content")
    var zhuyuanBills: MutableList<ZhuyuanBill> = mutableListOf()

    @XmlRootElement(name = "content")
    @XmlAccessorType(XmlAccessType.FIELD)
    class ZhuyuanBill() {

        /**
         * 住院号
         */
        @field:XmlElement(name = "InpatientNo")
        var inpatientNo: String = ""

        /**
         * 姓名
         */
        @field:XmlElement(name = "Name")
        var name: String = ""

        /**
         * 预交金票号
         */
        @field:XmlElement(name = "InvoiceNo")
        var invoiceNo: String = ""

        /**
         * 充值方式
         */
        @field:XmlElement(name = "Channel")
        var prepayType: String = ""

        val prepayTypeName: String
            get() = when (prepayType) {
                "JHWX" -> "微信小程序"
                "JHZFB" -> "支付宝"
                "YS" -> "院内账户"
                "NHCD" -> "农行银行卡"
                "NHSMF" -> "农行扫码付"
                "CA" -> "现金"
                "FYCD" -> "分院银行卡"
                "FYSMF" -> "分院扫码付"
                "SM" -> "统一扫码付"
                else -> prepayType
            }

        @field:XmlElement(name = "Channelno")
        var channelNo: String = ""

        /**
         * 金额
         */
        @field:XmlElement(name = "Amount")
        var prepayCost: BigDecimal = BigDecimal.ZERO

        /**
         * 充值时间
         */
        @field:XmlElement(name = "Date")
        var payTime: String = ""

        /**
         * 充值、退款
         */
        @field:XmlElement(name = "Type")
        var type: String = ""

        /**
         * 充值、退款
         */
        @field:XmlElement(name = "HisNo")
        var hisNo: String = ""
    }

}