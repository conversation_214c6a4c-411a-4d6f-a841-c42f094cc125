package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class MedicineOrderResponse : Response() {

    @field:XmlElement(name = "ApplyDrugList")
    var medicineOrders: MutableList<MedicineOrder> = mutableListOf()

    @XmlRootElement(name = "ApplyDrugList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class MedicineOrder {

        /**
         * 药品名称
         */
        @field:XmlElement(name = "DrugName")
        var drugName: String = ""

        /**
         * 药品规格
         */
        @field:XmlElement(name = "Specs")
        var specs: String = ""

        /**
         * 数量
         */
        @field:XmlElement(name = "Quantity")
        var quantity: String = ""

        /**
         * 单位
         */
        @field:XmlElement(name = "Unit")
        var unit: String = ""

        /**
         * 价格
         */
        @field:XmlElement(name = "Price")
        var price: String = ""

        /**
         * 费用
         */
        @field:XmlElement(name = "Cost")
        var cost: String = ""

        /**
         * 发药科室
         */
        @field:XmlElement(name = "DrugedDept")
        var drugDept: String = ""

        /**
         * 状态
         */
        @field:XmlElement(name = "State")
        var state: String = ""

        /**
         * 申请时间
         */
        @field:XmlElement(name = "ApplyDate")
        var applyDate: String = ""

        /**
         * 门诊流水号
         */
        @field:XmlElement(name = "Clinic_No")
        var clinicNo: String = ""
    }

}