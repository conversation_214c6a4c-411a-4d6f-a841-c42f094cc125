package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class DepartmentResponse : Response() {

    @field:XmlElement(name = "DeptList")
    var departments: MutableList<Department> = mutableListOf()

    /**
     * 一级科室只有type字段可用
     */
    @XmlRootElement(name = "DeptList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Department {

        @field:XmlElement(name = "DeptType")
        var type: String = ""

        @field:XmlElement(name = "DeptCode")
        var code: String = ""

        @field:XmlElement(name = "DeptName")
        var name: String = ""

        @field:XmlElement(name = "DeptIntroduce")
        var intro: String = ""

        @field:XmlElement(name = "DeptAddress")
        var address: String = ""

        @field:XmlElement(name = "Flag")
        var active: String = ""

        @field:XmlElement(name = "Branch")
        var branch: String = ""

        fun isAvailable(): Boolean = active == "1"

    }

}
