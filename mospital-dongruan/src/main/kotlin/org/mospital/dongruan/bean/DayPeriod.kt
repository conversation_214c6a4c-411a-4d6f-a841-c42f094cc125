package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.adapters.XmlAdapter

enum class DayPeriod(val code: Int) {
    MORNING(1),    // 上午
    NOON(2),       // 中午
    AFTERNOON(3),  // 下午
    EVENING(4),    // 夜间
    DAWN(5),       // 凌晨
    OTHER(9);      // 其他

    companion object {
        private val codeMap: Map<String, DayPeriod> = values().associateBy { it.code.toString() }
        fun fromCode(code: String): DayPeriod = codeMap[code] ?: OTHER
    }

    class XmlAdaptor : XmlAdapter<String, DayPeriod>() {
        override fun marshal(v: DayPeriod?): String = (v ?: OTHER).code.toString()
        override fun unmarshal(v: String?): DayPeriod = DayPeriod.fromCode(v ?: "")
    }
}