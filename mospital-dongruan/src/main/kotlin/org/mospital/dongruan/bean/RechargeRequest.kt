package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.dongruan.DongruanConfig
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class RechargeRequest() : Request() {

    /**
     * 实体卡号
     */
    @field:XmlElement(name = "MarkNO")
    var markNo: String = ""

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    /**
     * 交易流水号，建议传入商户订单号
     */
    @field:XmlElement(name = "Trace")
    var trace: String = ""

    /**
     * 交易终端号
     */
    @field:XmlElement(name = "TerminalNo")
    var terminalNo: String = ""

    /**
     * 交易日期，格式：MMdd
     */
    @field:XmlElement(name = "Date")
    var date: String = ""

    /**
     * 交易时间，格式：HHmmss
     */
    @field:XmlElement(name = "Time")
    var time: String = ""

    constructor(markNo: String, transModel: TransModel, trace: String, transTime: LocalDateTime): this() {
        this.markNo = markNo
        this.transModel = transModel
        this.trace = trace
        this.date = transTime.format(DateTimeFormatter.ofPattern("MMdd"))
        this.time = transTime.format(DateTimeFormatter.ofPattern("HHmmss"))
    }

    @XmlRootElement(name = "TransModel")
    @XmlAccessorType(XmlAccessType.FIELD)
    class TransModel() {
        /**
         * 银行名称
         */
        @field:XmlElement(name = "BankName")
        var bankName: String = ""

        /**
         * 银行卡号
         */
        @field:XmlElement(name = "BankAccountNO")
        var bankAccountNo: String = ""

        /**
         * 自助机编号
         */
        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = DongruanConfig.me.operator

        /**
         * 交易流水号，建议传入商户订单号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        /**
         * 付款方式：JHWX=微信，JHZFB=支付宝
         */
        @field:XmlElement(name = "BankPayType")
        var bankPayType: String = ""

        /**
         * 交易金额，最小充值10元，最大充值10000元
         */
        @field:XmlElement(name = "BankPayCost")
        var bankPayCost: BigDecimal = BigDecimal.ZERO

        constructor(bankTransNo: String, bankPayType: String, bankPayCost: BigDecimal) : this() {
            this.bankTransNo = bankTransNo
            this.bankPayType = bankPayType
            this.bankPayCost = bankPayCost
        }
    }

}
