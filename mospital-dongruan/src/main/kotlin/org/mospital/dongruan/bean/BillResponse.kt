package org.mospital.dongruan.bean

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class BillResponse : Response() {

    @field:XmlElement(name = "PrepayList")
    var bills: MutableList<Bill> = mutableListOf()

    @XmlRootElement(name = "PrepayList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Bill {

        /**
         * 虚拟卡号
         */
        @field:XmlElement(name = "CardNO")
        var cardNo: String = ""

        /**
         * 发生序号
         */
        @field:XmlElement(name = "HappenNO")
        var happenNo: String = ""

        /**
         * 账号
         */
        @field:XmlElement(name = "AccountNO")
        var accountNo: String = ""

        /**
         * 姓名
         */
        @field:XmlElement(name = "Name")
        var name: String = ""

        /**
         * 预交金票号
         */
        @field:XmlElement(name = "PrepayInvoiceNO")
        var invoiceNo: String = ""

        /**
         * 充值方式
         */
        @field:XmlElement(name = "PrepayType")
        var prepayType: String = ""

        /**
         * 充值方式
         */
        @field:XmlElement(name = "PrepayTypeName")
        var prepayTypeName: String = ""

        /**
         * 金额
         */
        @field:XmlElement(name = "PrepayCost")
        var prepayCost: BigDecimal = BigDecimal.ZERO

        /**
         * 银行名称
         */
        @field:XmlElement(name = "OpenBank")
        var bankName: String = ""

        /**
         * 银行账户
         */
        @field:XmlElement(name = "OpenAccount")
        var bankAccountNo: String = ""

        /**
         * 交易流水号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        /**
         * 充值时间
         */
        @field:XmlElement(name = "PayTime")
        var payTime: String = ""

    }

}
