package org.mospital.dongruan.bean

import org.mospital.common.jaxb.LocalDateJaxbAdapter
import java.time.LocalDate
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class DoctorRequest(): Request() {

    /**
     * 科室编码
     */
    @field:XmlElement(name = "DeptCode")
    var deptCode: String = ""

    /**
     * 排班日期
     */
    @field:XmlJavaTypeAdapter(LocalDateJaxbAdapter::class)
    @field:XmlElement(name = "SeeDate")
    var date: LocalDate = LocalDate.now()

    constructor(deptCode: String, date: LocalDate): this() {
        this.deptCode = deptCode
        this.date = date
    }

}
