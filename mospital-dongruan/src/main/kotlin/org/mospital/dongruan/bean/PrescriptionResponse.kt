package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.*

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class PrescriptionResponse : Response() {

    @field:XmlElement(name = "ChargeFeeList")
    var prescriptions: MutableList<Prescription> = mutableListOf()

    override fun isOk(): Boolean = code == 1 || msg.startsWith("不存在有效的挂号信息")

    @XmlRootElement(name = "ChargeFeeList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Prescription {

        /**
         * 门诊流水号
         */
        @field:XmlElement(name = "ClinicNO")
        var clinicNo: String = ""

        /**
         * 处方号
         */
        @field:XmlElement(name = "RecipeNO")
        var prescriptionNo: String = ""

        /**
         * 科室名称
         */
        @field:XmlElement(name = "DeptName")
        var departmentName: String = ""

        /**
         * 医生姓名
         */
        @field:XmlElement(name = "DoctName")
        var doctorName: String = ""

        /**
         * 费用
         */
        @field:XmlElement(name = "TotCost")
        var cost: String = ""

        /**
         * 收费标志：0=未收费，1=已收费
         */
        @field:XmlElement(name = "Flag")
        var flag: String = ""

        /**
         * 挂号时间
         */
        @field:XmlTransient
        var registerTime: String = ""

        /**
         * 慢性病标志：0=非慢性病，1=慢性病
         */
        var chronicFlag: String = ""

        /**
         * 急诊标志：0=非急诊，1=急诊
         */
        var isEmergency: String = ""

        /**
         * 是否已收费
         */
        fun isCharged(): Boolean = flag == "1"

        /**
         * 是否慢性病处方
         */
        fun isChronic(): Boolean = chronicFlag == "1"

        /**
         * 是否急诊处方
         */
        fun isEmergency(): Boolean = isEmergency == "1"
    }

}
