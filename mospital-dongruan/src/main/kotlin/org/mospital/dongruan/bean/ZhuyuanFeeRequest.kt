package org.mospital.dongruan.bean

import org.mospital.common.jaxb.LocalDateJaxbAdapter
import java.time.LocalDate
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanFeeRequest() : Request() {

    @field:XmlElement(name = "InpatientNo")
    var inPatientNo: String = ""

    @field:XmlElement(name = "DateTime")
    @field:XmlJavaTypeAdapter(LocalDateJaxbAdapter::class)
    var date: LocalDate = LocalDate.now()

    constructor(inPatientNo: String, date: LocalDate) : this() {
        this.inPatientNo = inPatientNo
        this.date = date
    }

}
