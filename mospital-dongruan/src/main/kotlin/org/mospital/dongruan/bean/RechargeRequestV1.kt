package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@Suppress("unused")
@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class RechargeRequestV1() : Request() {

    @field:XmlElement(name = "MarkNO")
    var markNo: String = ""

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    constructor(markNo: String, transModel: TransModel) : this() {
        this.markNo = markNo
        this.transModel = transModel
    }

}