package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.dongruan.DongruanConfig
import java.math.BigDecimal

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class RefundRequest() : Request() {

    /**
     * 虚拟卡号
     */
    @field:XmlElement(name = "CardNO")
    var cardNo: String = ""

    /**
     * 账号
     */
    @field:XmlElement(name = "AccountNO")
    var accountNo: String = ""

    /**
     * 发生序号
     */
    @field:XmlElement(name = "HappenNO")
    var happenNo: String = ""

    /**
     * 预交金票号
     */
    @field:XmlElement(name = "PrepayInvoiceNO")
    var invoiceNo: String = ""

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    constructor(bill: BillResponse.Bill, refundAmount: BigDecimal, bankTransNo: String): this() {
        this.cardNo = bill.cardNo
        this.accountNo = bill.accountNo
        this.happenNo = bill.happenNo
        this.invoiceNo = bill.invoiceNo
        this.transModel = TransModel(
            bankTransNo = bankTransNo,
            bankPayType = bill.prepayType,
            bankPayCost = refundAmount,
        )
    }

    @XmlRootElement(name = "TransModel")
    @XmlAccessorType(XmlAccessType.FIELD)
    class TransModel() {
        /**
         * 银行名称
         */
        @field:XmlElement(name = "BankName")
        var bankName: String = ""

        /**
         * 银行卡号
         */
        @field:XmlElement(name = "BankAccountNO")
        var bankAccountNo: String = ""

        /**
         * 自助机编号
         */
        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = DongruanConfig.me.operator

        /**
         * 交易流水号，建议传入商户退款单号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        /**
         * 付款方式：JHWX=微信，JHZFB=支付宝
         */
        @field:XmlElement(name = "BankPayType")
        var bankPayType: String = ""

        /**
         * 交易金额，最小充值10元，最大充值10000元
         */
        @field:XmlElement(name = "BankPayCost")
        var bankPayCost: BigDecimal = BigDecimal.ZERO

        constructor(bankTransNo: String, bankPayType: String, bankPayCost: BigDecimal) : this() {
            this.bankTransNo = bankTransNo
            this.bankPayType = bankPayType
            this.bankPayCost = bankPayCost
        }
    }
}
