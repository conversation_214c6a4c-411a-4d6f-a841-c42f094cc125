package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlTransient

@XmlTransient
@XmlAccessorType(XmlAccessType.FIELD)
open class Response {

    @field:XmlElement(name = "ResultCode")
    open var code: Int = 0

    @field:XmlElement(name = "ResultMsg")
    open var msg: String = ""

    open fun isOk() = code == 1

}
