package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class DrugRequest(): Request() {

    /**
     * 药品名称或拼音码
     * 如果使用拼音码，必须是大写
     */
    @field:XmlElement(name = "TradeNameOrSpellCode")
    var code: String = ""

    constructor(code: String) : this() {
        this.code = code
    }

}