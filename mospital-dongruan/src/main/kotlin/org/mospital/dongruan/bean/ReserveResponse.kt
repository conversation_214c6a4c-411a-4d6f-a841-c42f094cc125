package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class ReserveResponse : Response() {

    /**
     * 当前队列中的序号
     */
    @field:XmlElement(name = "QueueNo")
    var queueNo: String = ""

    /**
     * 门诊流水号
     */
    @field:XmlElement(name = "ClinicNO")
    var clinicNo: String = ""

}
