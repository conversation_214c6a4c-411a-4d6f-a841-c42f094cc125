package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import org.mospital.common.IdUtil
import org.mospital.dongruan.DongruanConfig

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class CancelReservationRequest(): Request() {

    /**
     * 门诊流水号
     */
    @field:XmlElement(name = "ClinicNO")
    var clinicNo: String = ""

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    constructor(clinicNo: String): this() {
        this.clinicNo = clinicNo
        this.transModel = TransModel(bankTransNo = IdUtil.simpleUUID())
    }

    @XmlRootElement(name = "TransModel")
    @XmlAccessorType(XmlAccessType.FIELD)
    class TransModel() {
        /**
         * 自助机编号
         */
        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = DongruanConfig.me.operator

        /**
         * 交易流水号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        constructor(bankTransNo: String) : this() {
            this.bankTransNo = bankTransNo
        }
    }
}
