package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.*
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter
import org.mospital.common.jaxb.LocalDateJaxbAdapter
import org.mospital.common.jaxb.LocalDateTimeJaxbAdapter
import java.time.LocalDate
import java.time.LocalDateTime

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanPatientsResponse : Response() {

    @field:XmlElementWrapper(name = "InPatientInfoList")
    @field:XmlElement(name = "InPatientInfo")
    var patients: MutableList<ZhuyuanPatient> = mutableListOf()

    override fun isOk(): Boolean {
        return super.isOk() && patients.isNotEmpty()
    }

    @XmlRootElement(name = "InPatientInfo")
    @XmlAccessorType(XmlAccessType.FIELD)
    class ZhuyuanPatient {

        @field:XmlElement(name = "InpatientNo")
        var zhuyuanNo: String = ""

        @field:XmlElement(name = "InDate")
        @field:XmlJavaTypeAdapter(LocalDateTimeJaxbAdapter::class)
        var ruyuanTime: LocalDateTime? = null

        @field:XmlElement(name = "DeptName")
        var departmentName: String = ""

        @field:XmlElement(name = "BedNo")
        var bedNo: String = ""

        /**
         * 住院预交金合计
         */
        @field:XmlElement(name = "PrePayCost")
        var prepayCost: String = ""

        /**
         * 住院花费总金额
         */
        @field:XmlElement(name = "TotCost")
        var totalCost: String = ""

        @field:XmlElement(name = "InState")
        var zhuyuanState: String = ""

        /**
         * 住院余额
         * 对于医保患者，医保报销后，自费金额-预交金额
         * 对于自费患者，费用总金额-预交金额
         */
        @field:XmlElement(name = "FreeCost")
        var balance: String = ""

        @field:XmlElement(name = "PatientInfo")
        var patientInfo: ZhuyuanPatientInfo = ZhuyuanPatientInfo()

        @XmlRootElement(name = "PatientInfo")
        @XmlAccessorType(XmlAccessType.FIELD)
        class ZhuyuanPatientInfo() {

            @field:XmlElement(name = "Name")
            var name: String = ""

            @field:XmlElement(name = "Sex")
            @field:XmlJavaTypeAdapter(SexEnum.XmlAdaptor::class)
            var sex: SexEnum = SexEnum.NOT_GIVEN

            @field:XmlElement(name = "IDNO")
            var idCardNo: String = ""

            @field:XmlElement(name = "BirthDay")
            @field:XmlJavaTypeAdapter(LocalDateJaxbAdapter::class)
            var birthday: LocalDate? = null

            @field:XmlElement(name = "Phone")
            var mobile: String = ""

            @field:XmlElement(name = "Address")
            var address: String = ""
        }

    }



}
