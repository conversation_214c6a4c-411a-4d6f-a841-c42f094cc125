package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class PrescriptionDetailResponse : Response() {

    @field:XmlElement(name = "ChargeFeeDetailList")
    var prescriptionDetails: MutableList<PrescriptionDetail> = mutableListOf()

    @XmlRootElement(name = "ChargeFeeDetailList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class PrescriptionDetail {

        /**
         * 门诊流水号
         */
        @field:XmlElement(name = "ClinicNO")
        var clinicNo: String = ""

        /**
         * 处方号
         */
        @field:XmlElement(name = "RecipeNO")
        var prescriptionNo: String = ""

        /**
         * 处方序号
         */
        @field:XmlElement(name = "SeqNO")
        var seqNo: String = ""

        /**
         * 医嘱流水号
         */
        @field:XmlElement(name = "MoOrder")
        var medicalAdviceNo: String = ""

        /**
         * 项目编码
         */
        @field:XmlElement(name = "ItemCode")
        var itemCode: String = ""

        /**
         * 项目名称
         */
        @field:XmlElement(name = "ItemName")
        var itemName: String = ""

        /**
         * 项目类别，是否药品
         */
        @field:XmlElement(name = "DrugFlag")
        var drugFlag: String = ""

        /**
         * 规格
         */
        @field:XmlElement(name = "Specs")
        var specification: String = ""

        /**
         * 每次用量
         */
        @field:XmlElement(name = "DoseNoce")
        var dose: String = ""

        /**
         * 每次用量单位
         */
        @field:XmlElement(name = "DoseUnit")
        var doseUnit: String = ""

        /**
         * 执行科室
         */
        @field:XmlElement(name = "ExecDept")
        var executionDepartmentName: String = ""

        /**
         * 数量
         */
        @field:XmlElement(name = "Qty")
        var quantity: String = ""

        /**
         * 单价
         */
        @field:XmlElement(name = "Price")
        var price: String = ""

        /**
         * 计价单位
         */
        @field:XmlElement(name = "PriceUnit")
        var priceUnit: String = ""

        /**
         * 总费用
         */
        @field:XmlElement(name = "Cost")
        var cost: String = ""

        /**
         * 组合号
         */
        @field:XmlElement(name = "ComBo")
        var combinationNo: String = ""

        /**
         * 科室名称
         */
        @field:XmlElement(name = "DeptName")
        var departmentName: String = ""

        /**
         * 医生姓名
         */
        @field:XmlElement(name = "DoctName")
        var doctorName: String = ""

        /**
         * 收费标志：0=未收费，1=已收费
         */
        @field:XmlElement(name = "Flag")
        var flag: String = ""

    }

}
