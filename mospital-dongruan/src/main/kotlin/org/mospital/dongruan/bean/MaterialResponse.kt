package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class MaterialResponse: Response() {

    @field:XmlElement(name = "UnDrugItemList")
    var materials: MutableList<Material> = mutableListOf()

    @XmlRootElement(name = "UnDrugItemList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Material {

        @field:XmlElement(name = "ItemCode")
        var code: String = ""

        @field:XmlElement(name = "ItemName")
        var name: String = ""

        @field:XmlElement(name = "FeeName")
        var feeType: String = ""

        @field:XmlElement(name = "Unit")
        var unit: String = ""

        @field:XmlElement(name = "Price")
        var price: String = ""

    }
}