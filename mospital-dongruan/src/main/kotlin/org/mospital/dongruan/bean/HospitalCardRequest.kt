package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class HospitalCardRequest() : Request() {

    /**
     * 实体卡号
     * 伊犁友谊医院：支持传入身份证号
     */
    @field:XmlElement(name = "MarkNO")
    var markNo: String = ""

    constructor(markNo: String) : this() {
        this.markNo = markNo
    }

}
