package org.mospital.dongruan.bean

import org.mospital.common.jaxb.LocalDateJaxbAdapter
import java.time.LocalDate
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class MedicineOrderRequest() : Request() {

    @field:XmlElement(name = "CardNO")
    var cardNO: String = ""

    @field:XmlJavaTypeAdapter(LocalDateJaxbAdapter::class)
    @field:XmlElement(name = "ApplyBeginDate")
    var applyBeginDate: LocalDate = LocalDate.now()

    @field:XmlJavaTypeAdapter(LocalDateJaxbAdapter::class)
    @field:XmlElement(name = "ApplyEndDate")
    var applyEndDate: LocalDate = LocalDate.now()

    constructor(cardNO: String, applyBeginDate: LocalDate, applyEndDate: LocalDate) : this() {
        this.cardNO = cardNO
        this.applyBeginDate = applyBeginDate
        this.applyEndDate = applyEndDate
    }

}