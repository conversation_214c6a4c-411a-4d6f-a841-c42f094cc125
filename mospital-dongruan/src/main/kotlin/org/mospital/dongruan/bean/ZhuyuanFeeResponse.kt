package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanFeeResponse : Response() {

    @field:XmlElement(name = "InPatientFeeList")
    var fees: MutableList<Fee> = mutableListOf()

    @XmlRootElement(name = "InPatientFeeList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Fee() {

        @field:XmlElement(name = "FeeCode")
        var code: String = ""

        @field:XmlElement(name = "FeeName")
        var name: String = ""

        @field:XmlElement(name = "Unit")
        var unit: String = ""

        @field:XmlElement(name = "Qty")
        var quantity: String = ""

        @field:XmlElement(name = "UnitPrice")
        var unitPrice: String = ""

        @field:XmlElement(name = "TotCost")
        var totalAmount: String = ""

        @field:XmlElement(name = "OwnCost")
        var ownPayAmount: String = ""

    }

}
