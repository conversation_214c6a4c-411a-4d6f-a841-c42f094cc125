package org.mospital.dongruan.bean

import org.mospital.common.jaxb.LocalDateTimeJaxbAdapter
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class CheckRequest() : Request() {

    @field:XmlJavaTypeAdapter(LocalDateTimeJaxbAdapter::class)
    @field:XmlElement(name = "StartTime")
    var startTime: LocalDateTime = LocalDate.now().atStartOfDay()

    @field:XmlJavaTypeAdapter(LocalDateTimeJaxbAdapter::class)
    @field:XmlElement(name = "EndTime")
    var endTime: LocalDateTime = LocalDate.now().atTime(LocalTime.MAX)

    /**
     * 付款方式：JHWX=微信，JHZFB=支付宝
     */
    @field:XmlElement(name = "PayType")
    var payType: String = ""

    constructor(date: LocalDate, payType: String) : this() {
        this.startTime = date.atStartOfDay()
        this.endTime = date.atTime(LocalTime.MAX)
        this.payType = payType
    }

}
