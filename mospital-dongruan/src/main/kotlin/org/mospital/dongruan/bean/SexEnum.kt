package org.mospital.dongruan.bean

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.xml.bind.annotation.adapters.XmlAdapter

@Suppress("unused")
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
enum class SexEnum(val code: Int, val description: String) {

    MALE(1, "男"), FEMALE(2, "女"), NOT_GIVEN(9, "未提供");

    companion object {
        private val codeMap = values().associateBy { it.code.toString() }
        fun fromCode(code: String) = codeMap[code] ?: NOT_GIVEN
    }

    class XmlAdaptor : XmlAdapter<String, SexEnum>() {
        override fun marshal(v: SexEnum?): String = (v ?: NOT_GIVEN).code.toString()
        override fun unmarshal(v: String?): SexEnum = fromCode(v ?: "")
    }

}
