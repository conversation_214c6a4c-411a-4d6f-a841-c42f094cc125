package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class BillRequest() : Request() {

    /**
     * 虚拟卡号
     */
    @field:XmlElement(name = "CardNO")
    var cardNo: String = ""

    constructor(cardNo: String) : this() {
        this.cardNo = cardNo
    }

}
