package org.mospital.dongruan.bean

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class MenzhenPatientsResponse: Response() {

    @field:XmlElement(name = "Total")
    var total: Int = 0

    @field:XmlElement(name = "PatientList")
    var patients: MutableList<MenzhenPatient> = mutableListOf()

    @XmlRootElement(name = "PatientList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class MenzhenPatient() {
        /**
         * 姓名
         */
        @field:XmlElement(name = "Name")
        var name: String = ""

        /**
         * 虚拟卡号，病历号，患者索引号
         */
        @field:XmlElement(name = "CardNO")
        var cardNo: String = ""

        /**
         * 实体卡号
         */
        @field:XmlElement(name = "MarkNo")
        var markNo: String = ""

        /**
         * 卡类型
         */
        @field:XmlElement(name = "MarkType")
        var markType: String = ""

        /**
         * 账户余额
         */
        @field:XmlElement(name = "Vacancy")
        var balance: BigDecimal = BigDecimal.ZERO

        /**
         * 身份证号
         */
        @field:XmlElement(name = "IdNo")
        var idCardNo: String = ""

        /**
         * 联系电话
         */
        @field:XmlElement(name = "Phone")
        var phone: String = ""

        /**
         * 是否有效：1=有效，0=无效
         */
        @field:XmlElement(name = "Status")
        var status: String = ""

        fun isValid(): Boolean = status == "1"
    }

}
