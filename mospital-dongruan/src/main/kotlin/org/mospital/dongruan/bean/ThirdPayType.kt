package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.adapters.XmlAdapter

enum class ThirdPayType(val code: String) {
    NONE(""),
    Alipay("1"),
    WeChatPay("2"),
    UnionPay("3");

    companion object {
        private val codeMap: Map<String, ThirdPayType> = ThirdPayType.entries.associateBy { it.code }
        fun fromCode(code: String): ThirdPayType? = codeMap[code]
    }

    class XmlAdaptor : XmlAdapter<String, ThirdPayType>() {
        override fun marshal(v: ThirdPayType?): String = if (v == null) "" else v.code.toString()
        override fun unmarshal(v: String?): ThirdPayType? = fromCode(v ?: "")
    }
}