package org.mospital.dongruan.bean

import java.math.BigDecimal
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
class CheckResponse : Response() {

    @field:XmlElement(name = "BillCounts")
    var count: Int = 0

    @field:XmlElement(name = "BillTotCost")
    var totalAmount: BigDecimal = BigDecimal.ZERO

    @field:XmlElement(name = "BillList")
    var items: MutableList<Item> = mutableListOf()

    @XmlRootElement(name = "BillList")
    @XmlAccessorType(XmlAccessType.FIELD)
    class Item {

        @field:XmlElement(name = "CardNO")
        var cardNo: String = ""

        @field:XmlElement(name = "BankAccountNo")
        var bankAccountNo: String = ""

        @field:XmlElement(name = "PayCost")
        var amount: BigDecimal = BigDecimal.ZERO

        @field:XmlElement(name = "InvoiceNO")
        var invoiceNo: String = ""

        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        @field:XmlElement(name = "OperDate")
        var transTime: String = ""

        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = ""

        @field:XmlElement(name = "ModeCode")
        var mode: String = ""

        fun isRecharge(): Boolean = amount > BigDecimal.ZERO
        fun isRefund(): Boolean = !isRecharge()
        fun isMenzhen(): Boolean = mode == "1"
        fun isZhuyuan(): Boolean = mode == "2"

        fun isMipOrder(): Boolean = bankTransNo.isNullOrBlank() && invoiceNo.startsWith("M")

    }

}
