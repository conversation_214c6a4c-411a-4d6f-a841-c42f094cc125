package org.mospital.dongruan.bean

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter
import org.mospital.dongruan.DongruanConfig
import java.math.BigDecimal

@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
class ZhuyuanRechargeRequest() : Request() {

    /**
     * 住院号
     */
    @field:XmlElement(name = "InpatientNo")
    var zhuyuanNo: String = ""

    @field:XmlElement(name = "TransModel")
    var transModel: TransModel = TransModel()

    constructor(zhuyuanNo: String, transModel: TransModel) : this() {
        this.zhuyuanNo = zhuyuanNo
        this.transModel = transModel
    }

    @XmlRootElement(name = "TransModel")
    @XmlAccessorType(XmlAccessType.FIELD)
    class TransModel() {
        /**
         * 银行名称
         */
        @field:XmlElement(name = "BankName")
        var bankName: String = ""

        /**
         * 银行卡号
         */
        @field:XmlElement(name = "BankAccountNO")
        var bankAccountNo: String = ""

        /**
         * 自助机编号
         */
        @field:XmlElement(name = "BankMachineNO")
        var bankMachineNo: String = DongruanConfig.me.operator

        /**
         * 交易流水号，建议传入商户订单号
         */
        @field:XmlElement(name = "BankTransNO")
        var bankTransNo: String = ""

        /**
         * 付款方式：JHWX=微信，JHZFB=支付宝
         */
        @field:XmlElement(name = "BankPayType")
        var bankPayType: String = ""

        /**
         * 交易金额，最小充值10元，最大充值10000元
         */
        @field:XmlElement(name = "BankPayCost")
        var bankPayCost: BigDecimal = BigDecimal.ZERO


        @field:XmlElement(name = "ThirdPayType")
        @field:XmlJavaTypeAdapter(ThirdPayType.XmlAdaptor::class)
        var thirdPayType: ThirdPayType = ThirdPayType.WeChatPay

        constructor(bankTransNo: String, bankPayType: String, bankPayCost: BigDecimal, thirdPayType: ThirdPayType) : this() {
            this.bankTransNo = bankTransNo
            this.bankPayType = bankPayType
            this.bankPayCost = bankPayCost
            this.thirdPayType = thirdPayType
        }
    }

}
