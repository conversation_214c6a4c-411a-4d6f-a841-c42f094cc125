package org.mospital.dongruan

import org.mospital.common.IdUtil
import org.mospital.common.UrlUtil
import org.mospital.common.jaxb.JAXBKit
import org.mospital.common.toOneLineString
import org.mospital.dongruan.bean.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.LocalDate

@Suppress("unused")
class DongruanService private constructor(
    private val webServiceClient: WebServiceClient,
) {

    companion object {
        private val log: Logger = LoggerFactory.getLogger(DongruanService::class.java)

        private val serviceCache = mutableMapOf<String, DongruanService>()

        private fun createDefaultInstance(): DongruanService {
            val client = DefaultWebServiceClient(UrlUtil.parseUrl(DongruanConfig.me.wsdlURL))
            return DongruanService(client)
        }

        @Synchronized
        fun getInstance(): DongruanService {
            return serviceCache.getOrPut("default") { createDefaultInstance() }
        }

        /**
         * 默认实例，向后兼容
         */
        @JvmStatic
        val me: DongruanService by lazy {
            getInstance()
        }

        /**
         * 创建基于HTTP客户端的服务实例
         * @param config 配置对象，包含HTTP URL、domain、key等信息
         * @return 使用HTTP客户端的服务实例
         */
        fun createHttpInstance(config: DongruanConfig): DongruanService {
            val client = HttpWebServiceClient(config)
            return DongruanService(client)
        }

        /**
         * 基于配置文件创建HTTP版本的服务实例
         * 使用配置文件中的httpUrl、domain、key等字段
         */
        @JvmStatic
        val httpInstance: DongruanService by lazy {
            createHttpInstance(DongruanConfig.me)
        }

        // 用于测试的工厂方法
        fun createForTest(webServiceClient: WebServiceClient): DongruanService {
            return DongruanService(webServiceClient)
        }
    }

    /**
     * 发送请求到HIS系统
     * @param code 交易码
     * @param xml 请求XML
     * @return 响应XML
     * @throws java.net.SocketTimeoutException 当请求超时时抛出
     * @throws java.net.ConnectException 当连接失败时抛出
     */
    private fun sendRequest(code: String, xml: String = ""): String {
        val url = DongruanConfig.me.wsdlURL
        val traceId: String = IdUtil.simpleUUID()
        log.debug("Request#{}: url={}, code={}, input={}", traceId, url, code, xml.toOneLineString())

        val startTime = System.currentTimeMillis()

        try {
            val responseText: String = webServiceClient.process(code, xml)

            val endTime = System.currentTimeMillis()
            val responseTime = endTime - startTime

            log.debug("Response#{}: url={}, code={}, output={}", traceId, url, code, responseText.toOneLineString())

            // 记录响应时间统计
            val slowResponseThreshold = DongruanConfig.me.slowResponseThreshold
            if (responseTime > slowResponseThreshold) {
                log.warn("SlowResponse#{}: url={}, code={}, responseTime={}ms (超过{}ms阈值)", traceId, url, code, responseTime, slowResponseThreshold)
            } else {
                log.debug("ResponseTime#{}: url={}, code={}, responseTime={}ms", traceId, url, code, responseTime)
            }

            return responseText

        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            val responseTime = endTime - startTime

            // 记录异常和响应时间
            when (e) {
                is java.net.SocketTimeoutException -> {
                    log.error(
                        "Timeout#$traceId: code=$code, responseTime=${responseTime}ms, error=请求超时",
                        e
                    )
                }

                is java.net.ConnectException -> {
                    log.error(
                        "ConnectionFailed#$traceId: code=$code, responseTime=${responseTime}ms, error=连接失败",
                        e
                    )
                }

                else -> {
                    log.error(
                        "RequestFailed#$traceId: code=$code, responseTime=${responseTime}ms, error=${e.javaClass.simpleName}",
                        e
                    )
                }
            }

            // 重新抛出异常，让上层处理
            throw e
        }
    }

    fun getDepartments(request: DepartmentRequest): DepartmentResponse {
        val responseXml: String = sendRequest(code = "1002", xml = request.toXML())
        return JAXBKit.unmarshal(DepartmentResponse::class.java, responseXml)
    }

    fun getDepartmentsV1(request: DepartmentRequest): DepartmentResponseV1 {
        val responseXml: String = sendRequest(code = "1002", xml = request.toXML())
        return JAXBKit.unmarshal(DepartmentResponseV1::class.java, responseXml)
    }

    fun getDoctors(deptCode: String, date: LocalDate): DoctorResponse {
        val request = DoctorRequest(deptCode = deptCode, date = date)
        val responseXml: String = sendRequest(code = "1003", xml = request.toXML())
        return JAXBKit.unmarshal(DoctorResponse::class.java, responseXml)
    }

    fun getSchedules(deptCode: String, doctCode: String, date: LocalDate): ScheduleResponse {
        val request = ScheduleRequest(
            deptCode = deptCode,
            doctCode = doctCode,
            date = date
        )
        val responseXml: String = sendRequest(code = "1004", xml = request.toXML())
        return JAXBKit.unmarshal(ScheduleResponse::class.java, responseXml)
    }

    fun getSchedulesV1(
        deptCode: String,
        doctCode: String,
        date: LocalDate
    ): ScheduleResponseV1 {
        val request = ScheduleRequest(
            deptCode = deptCode,
            doctCode = doctCode,
            date = date
        )
        val responseXml: String = sendRequest(code = "1004", xml = request.toXML())
        return JAXBKit.unmarshal(ScheduleResponseV1::class.java, responseXml)
    }

    fun getScheduleItems(schid: String): ScheduleItemResponse {
        val request = ScheduleItemRequest(schemaId = schid)
        val responseXml: String = sendRequest(code = "1004a", xml = request.toXML())
        return JAXBKit.unmarshal(ScheduleItemResponse::class.java, responseXml)
    }

    fun queryAvailableRegistrations(cardNo: String): AvailableRegistrationResponse {
        val request = AvailableRegistrationRequest(cardNo = cardNo)
        val responseXml: String = sendRequest(code = "1005", xml = request.toXML())
        return JAXBKit.unmarshal(AvailableRegistrationResponse::class.java, responseXml)
    }

    fun queryAvailableRegistrationsV1(cardNo: String): AvailableRegistrationResponseV1 {
        val request = AvailableRegistrationRequest(cardNo = cardNo)
        val responseXml: String = sendRequest(code = "1005", xml = request.toXML())
        return JAXBKit.unmarshal(AvailableRegistrationResponseV1::class.java, responseXml)
    }

    fun reserve(request: ReserveRequest): ReserveResponse {
        val responseXml: String = sendRequest(code = "1006", xml = request.toXML())
        return JAXBKit.unmarshal(ReserveResponse::class.java, responseXml)
    }

    fun reserveV1(request: ReserveRequestV1): ReserveResponse {
        val responseXml: String = sendRequest(code = "1006", xml = request.toXML())
        return JAXBKit.unmarshal(ReserveResponse::class.java, responseXml)
    }

    fun cancelReservation(clinicNo: String): CancelReservationResponse {
        val request = CancelReservationRequest(clinicNo = clinicNo)
        val responseXml: String = sendRequest(code = "1006a", xml = request.toXML())
        return JAXBKit.unmarshal(CancelReservationResponse::class.java, responseXml)
    }

    fun cancelReservationV1(request: CancelReservationRequestV1): CancelReservationResponse {
        val responseXml: String = sendRequest(code = "1006a", xml = request.toXML())
        return JAXBKit.unmarshal(CancelReservationResponse::class.java, responseXml)
    }

    fun registerHealthCard(request: HealthCardRegisterRequest): HealthCardRegisterResponse {
        val responseXml: String = sendRequest(code = "1008", xml = request.toXML())
        return JAXBKit.unmarshal(HealthCardRegisterResponse::class.java, responseXml)
    }

    fun registerHealthCardV2(request: HealthCardRegisterRequestV2): HealthCardRegisterResponse {
        val responseXml: String = sendRequest(code = "1008", xml = request.toXML())
        return JAXBKit.unmarshal(HealthCardRegisterResponse::class.java, responseXml)
    }

    fun queryMenzhenPatient(request: MenzhenPatientsRequest): MenzhenPatientsResponse {
        val responseXml: String = sendRequest(code = "1009", xml = request.toXML())
        return JAXBKit.unmarshal(MenzhenPatientsResponse::class.java, responseXml)
    }

    fun queryBills(cardNo: String): BillResponse {
        val request = BillRequest(cardNo = cardNo)
        val responseXml: String = sendRequest(code = "1010", xml = request.toXML())
        return JAXBKit.unmarshal(BillResponse::class.java, responseXml)
    }

    fun recharge(request: RechargeRequest): RechargeResponse {
        val responseXml: String = sendRequest(code = "1011", xml = request.toXML())
        return JAXBKit.unmarshal(RechargeResponse::class.java, responseXml)
    }

    fun rechargeV1(request: RechargeRequestV1): RechargeResponse {
        val responseXml: String = sendRequest(code = "1011", xml = request.toXML())
        return JAXBKit.unmarshal(RechargeResponse::class.java, responseXml)
    }

    fun refund(request: RefundRequest): RefundResponse {
        val responseXml: String = sendRequest(code = "1011a", xml = request.toXML())
        return JAXBKit.unmarshal(RefundResponse::class.java, responseXml)
    }

    fun queryPrescriptions(clinicNo: String): PrescriptionResponse {
        val request = PrescriptionRequest(clinicNo = clinicNo)
        val responseXml: String = sendRequest(code = "1012", xml = request.toXML())
        return JAXBKit.unmarshal(PrescriptionResponse::class.java, responseXml)
    }

    fun queryPrescriptionDetails(clinicNo: String, prescriptionNo: String): PrescriptionDetailResponse {
        val request = PrescriptionDetailRequest(clinicNo = clinicNo, prescriptionNo = prescriptionNo)
        val responseXml: String = sendRequest(code = "1013", xml = request.toXML())
        return JAXBKit.unmarshal(PrescriptionDetailResponse::class.java, responseXml)
    }

    fun payMenzhen(request: MenzhenPaymentRequest): MenzhenPaymentResponse {
        val responseXml: String = sendRequest(code = "1014", xml = request.toXML())
        return JAXBKit.unmarshal(MenzhenPaymentResponse::class.java, responseXml)
    }

    fun payMenzhenV1(request: MenzhenPaymentRequestV1): MenzhenPaymentResponse {
        val responseXml: String = sendRequest(code = "1014", xml = request.toXML())
        return JAXBKit.unmarshal(MenzhenPaymentResponse::class.java, responseXml)
    }

    fun queryHospitalCard(markNo: String): HospitalCardResponse {
        val request = HospitalCardRequest(markNo = markNo)
        val responseXml: String = sendRequest(code = "1016", xml = request.toXML())
        return JAXBKit.unmarshal(HospitalCardResponse::class.java, responseXml)
    }

    fun queryDrug(code: String): DrugResponse {
        val request = DrugRequest(code = code)
        val responseXml: String = sendRequest(code = "1017", xml = request.toXML())
        return JAXBKit.unmarshal(DrugResponse::class.java, responseXml)
    }

    fun queryMaterial(code: String): MaterialResponse {
        val request = MaterialRequest(code = code)
        val responseXml: String = sendRequest(code = "1018", xml = request.toXML())
        return JAXBKit.unmarshal(MaterialResponse::class.java, responseXml)
    }

    fun queryMedicineOrder(request: MedicineOrderRequest): MedicineOrderResponse {
        val responseXml: String = sendRequest(code = "1030", xml = request.toXML())
        return JAXBKit.unmarshal(MedicineOrderResponse::class.java, responseXml)
    }

    fun queryZhuyuanPatient(request: ZhuyuanPatientRequest): ZhuyuanPatientResponse {
        val responseXml: String = sendRequest(code = "2001", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanPatientResponse::class.java, responseXml)
    }

    fun queryZhuyuanPatients(request: ZhuyuanPatientsRequest): ZhuyuanPatientsResponse {
        val responseXml: String = sendRequest(code = "2001", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanPatientsResponse::class.java, responseXml)
    }

    fun zhuyuanRecharge(request: ZhuyuanRechargeRequest): ZhuyuanRechargeResponse {
        val responseXml: String = sendRequest(code = "2002", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanRechargeResponse::class.java, responseXml)
    }

    fun queryZhuyuanFee(request: ZhuyuanFeeRequest): ZhuyuanFeeResponse {
        val responseXml: String = sendRequest(code = "2003", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanFeeResponse::class.java, responseXml)
    }

    fun queryZhuyuanBills(request: ZhuyuanBillRequest): ZhuyuanBillResponse {
        val responseXml: String = sendRequest(code = "2004", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanBillResponse::class.java, responseXml)
    }

    fun queryZhuyuanBillsV1(request: ZhuyuanBillRequest): ZhuyuanBillResponseV1 {
        val responseXml: String = sendRequest(code = "2004", xml = request.toXML())
        return JAXBKit.unmarshal(ZhuyuanBillResponseV1::class.java, responseXml)
    }

    fun check(date: LocalDate, payType: String): CheckResponse {
        val request = CheckRequest(date = date, payType = payType)
        val responseXml: String = sendRequest(code = "3002", xml = request.toXML())
        return JAXBKit.unmarshal(CheckResponse::class.java, responseXml)
    }
}
