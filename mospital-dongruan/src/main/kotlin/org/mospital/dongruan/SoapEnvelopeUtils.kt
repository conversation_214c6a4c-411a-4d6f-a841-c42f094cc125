package org.mospital.dongruan

import org.w3c.dom.Document
import org.xml.sax.InputSource
import java.io.StringReader
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.xpath.XPathConstants
import javax.xml.xpath.XPathFactory

/**
 * SOAP消息处理工具类
 * 用于构建SOAP请求和解析SOAP响应
 */
object SoapEnvelopeUtils {

    /**
     * 构建SOAP请求envelope
     * @param transCode 交易代码
     * @param xml 业务XML数据
     * @return SOAP envelope字符串
     */
    fun buildSoapEnvelope(transCode: String, xml: String): String {
        return """<?xml version="1.0" encoding="utf-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:Process>
         <tem:transCode>$transCode</tem:transCode>
         <tem:xml><![CDATA[$xml]]></tem:xml>
      </tem:Process>
   </soapenv:Body>
</soapenv:Envelope>"""
    }

    /**
     * 从SOAP响应中提取ProcessResult内容
     * @param soapResponse SOAP响应字符串
     * @return ProcessResult中的XML内容
     * @throws Exception 如果解析失败
     */
    fun extractProcessResult(soapResponse: String): String {
        try {
            val factory = DocumentBuilderFactory.newInstance()
            factory.isNamespaceAware = false  // 简化处理，不使用命名空间
            val builder = factory.newDocumentBuilder()
            val document: Document = builder.parse(InputSource(StringReader(soapResponse)))

            val xpathFactory = XPathFactory.newInstance()
            val xpath = xpathFactory.newXPath()

            // 使用XPath查找ProcessResult节点，忽略命名空间
            val expression = "//*[local-name()='ProcessResult']/text()"
            val result = xpath.evaluate(expression, document, XPathConstants.STRING) as String

            check(!(result.isBlank())) { "ProcessResult not found in SOAP response" }

            // ProcessResult中的内容是XML编码的，需要解码
            return decodeXmlEntities(result)
        } catch (e: Exception) {
            throw IllegalStateException("Failed to parse SOAP response: ${e.message}", e)
        }
    }

    /**
     * 解码XML实体
     * @param encodedXml 编码的XML字符串
     * @return 解码后的XML字符串
     */
    private fun decodeXmlEntities(encodedXml: String): String {
        return encodedXml
            .replace("&amp;", "&")
            .replace("&lt;", "<")
            .replace("&gt;", ">")
            .replace("&quot;", "\"")
            .replace("&apos;", "'")
    }
} 