package org.mospital.dongruan

import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.mospital.common.http.HttpClientFactory
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit

/**
 * 基于HTTP的WebService客户端实现
 * 使用retrofit+okhttp替代传统的WebService客户端
 */
class HttpWebServiceClient(private val config: DongruanConfig) : WebServiceClient {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(HttpWebServiceClient::class.java)
        private val XML_MEDIA_TYPE = "text/xml;charset=UTF-8".toMediaType()
    }

    init {
        require(config.httpUrl.isNotBlank()) { "httpUrl cannot be blank" }
        require(config.domain.isNotBlank()) { "domain cannot be blank" }
        require(config.key.isNotBlank()) { "key cannot be blank" }
        require(config.connectTimeout > 0) { "connectTimeout must be greater than 0" }
        require(config.requestTimeout > 0) { "requestTimeout must be greater than 0" }
    }

    /**
     * Header拦截器，自动添加domain和key头
     */
    private class HeaderInterceptor(
        private val domain: String,
        private val key: String
    ) : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()
            val requestWithHeaders = originalRequest.newBuilder()
                .header("domain", domain)
                .header("key", key)
                .build()
            return chain.proceed(requestWithHeaders)
        }
    }

    private val httpService: DongruanHttpService by lazy {
        val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
            logger = logger,
            connectTimeout = config.connectTimeout.toLong(),
            readTimeout = config.requestTimeout.toLong(),
            useUuidRequestId = true
        ) { builder ->
            builder.addInterceptor(HeaderInterceptor(config.domain, config.key))
        }

        val retrofit = Retrofit.Builder()
            .baseUrl(config.httpUrl)
            .client(httpClient)
            .build()

        retrofit.create(DongruanHttpService::class.java)
    }

    override fun process(code: String, xml: String): String {
        try {
            // 构建SOAP envelope
            val soapEnvelope = SoapEnvelopeUtils.buildSoapEnvelope(code, xml)
            val requestBody = soapEnvelope.toRequestBody(XML_MEDIA_TYPE)

            // 发送HTTP请求
            val call = httpService.process(requestBody)
            val response = call.execute()

            if (!response.isSuccessful) {
                throw RuntimeException("HTTP request failed with code: ${response.code()}")
            }

            val responseBody = response.body()?.string()
                ?: throw RuntimeException("Received empty response from server")

            // 从SOAP响应中提取ProcessResult
            return SoapEnvelopeUtils.extractProcessResult(responseBody)
        } catch (e: Exception) {
            logger.error("HTTP request failed", e)
            when (e) {
                is IllegalStateException -> throw e
                is RuntimeException   -> throw e
                else                  -> throw RuntimeException("HTTP WebService call failed", e)
            }
        }
    }
} 