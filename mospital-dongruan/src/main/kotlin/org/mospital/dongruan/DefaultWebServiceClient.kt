package org.mospital.dongruan

import jakarta.xml.ws.BindingProvider
import org.mospital.dongruan.client.Service
import org.mospital.dongruan.client.ServiceSoap
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.net.URL

/**
 * 默认WebService客户端实现
 * 使用传统的SOAP协议与服务端通信
 */
class DefaultWebServiceClient(wsdlUrl: URL) : WebServiceClient {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(DefaultWebServiceClient::class.java)
    }

    private val soapClient: ServiceSoap = createConfiguredClient(wsdlUrl)

    override fun process(code: String, xml: String): String {
        return soapClient.process(code, xml)
    }

    /**
     * 创建配置了超时时间的WebService客户端
     */
    private fun createConfiguredClient(wsdlURL: URL): ServiceSoap {
        val service = Service(wsdlURL)
        val port = service.serviceSoap

        // 安全的类型检查和配置
        if (port is BindingProvider) {
            port.requestContext.apply {
                // 连接超时 - 建立连接的最长时间
                put("com.sun.xml.ws.connect.timeout", DongruanConfig.me.connectTimeout)
                put("com.sun.xml.internal.ws.connect.timeout", DongruanConfig.me.connectTimeout)
                // 请求超时 - 等待响应的最长时间
                put("com.sun.xml.ws.request.timeout", DongruanConfig.me.requestTimeout)
                put("com.sun.xml.internal.ws.request.timeout", DongruanConfig.me.requestTimeout)

                // JAX-WS RI 2.x 兼容性配置
                put("com.sun.xml.ws.developer.JAXWSProperties.CONNECT_TIMEOUT", DongruanConfig.me.connectTimeout)
                put("com.sun.xml.ws.developer.JAXWSProperties.REQUEST_TIMEOUT", DongruanConfig.me.requestTimeout)
            }
            logger.info("WebService客户端已配置超时: 连接超时=${DongruanConfig.me.connectTimeout}ms, 请求超时=${DongruanConfig.me.requestTimeout}ms, URL=$wsdlURL")
        } else {
            val actualType = port.javaClass.name
            val implementedInterfaces = port.javaClass.interfaces.joinToString { it.name }
            logger.error("WebService端口类型不兼容: 期望jakarta.xml.ws.BindingProvider，实际类型=$actualType，实现的接口=[$implementedInterfaces]")
            throw IllegalStateException("无法配置WebService超时设置: ServiceSoap端口未实现jakarta.xml.ws.BindingProvider接口")
        }

        return port
    }
} 