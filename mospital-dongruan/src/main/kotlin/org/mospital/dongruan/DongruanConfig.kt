package org.mospital.dongruan

import com.fasterxml.jackson.annotation.JsonProperty
import org.mospital.jackson.ConfigLoader

/**
 * DongruanConfig 配置类
 * 对应 dongruan.yaml 配置文件
 */
data class DongruanConfig(
    val operator: String = "",
    @JsonProperty("wsdlURL") // 保持与YAML键一致
    val wsdlURL: String = "",
    val connectTimeout: Int = 5000,
    val requestTimeout: Int = 30000,
    val slowResponseThreshold: Int = 2000,
    
    // HTTP客户端配置
    val httpUrl: String = "",
    val domain: String = "",
    val key: String = ""
) {
    companion object {
        @JvmStatic
        val me: DongruanConfig by lazy {
            try {
                ConfigLoader.loadConfig(
                    "dongruan.yaml",
                    DongruanConfig::class.java
                )
            } catch (e: Exception) {
                throw IllegalStateException(
                    "Failed to load Dongruan configuration",
                    e
                )
            }
        }
    }
}
