package org.mospital.dongruan

import org.junit.jupiter.api.Test
import org.mospital.common.jaxb.JAXBKit
import org.mospital.dongruan.bean.AvailableRegistrationResponseV1
import kotlin.test.assertTrue

class AvailableRegistrationResponseV1Test {

    @Test
    fun parseFromXml() {
        val xml = """
            <Response>
              <ResultCode>1</ResultCode>
              <ResultMsg>查询挂号信息成功</ResultMsg>
              <RegList>
                <ClinicNO>3463333</ClinicNO>
                <DeptName>口腔科门诊</DeptName>
                <DoctName>普通号</DoctName>
                <RegDate>2024-08-21 10:47:35</RegDate>
                <BookFlag>0</BookFlag>
                <DeptAddress />
                <SeeFlag>未诊</SeeFlag>
                <OrderId>2408211047182961452082999900</OrderId>
                <PayType>CA</PayType>
                <RegCost>18.00</RegCost>
                <Noon>上午</Noon>
              </RegList>
              <RegList>
                <ClinicNO>3463245</ClinicNO>
                <DeptName>口腔科门诊</DeptName>
                <DoctName>普通号</DoctName>
                <RegDate>2024-08-15 14:58:14</RegDate>
                <BookFlag>0</BookFlag>
                <DeptAddress />
                <SeeFlag>已诊</SeeFlag>
                <OrderId>2408151458151049888045001246</OrderId>
                <PayType>CA</PayType>
                <RegCost>18.00</RegCost>
                <Noon>中午</Noon>
              </RegList>
              <RegList>
                <ClinicNO>3463181</ClinicNO>
                <DeptName>口腔科门诊</DeptName>
                <DoctName>普通号</DoctName>
                <RegDate>2024-08-09 20:48:35</RegDate>
                <BookFlag>0</BookFlag>
                <DeptAddress />
                <SeeFlag>已诊</SeeFlag>
                <OrderId>2408092048372556425971999900</OrderId>
                <PayType>CA</PayType>
                <RegCost>18.00</RegCost>
                <Noon>夜间</Noon>
              </RegList>
              <RegList>
                <ClinicNO>3463055</ClinicNO>
                <DeptName>口腔科门诊</DeptName>
                <DoctName>普通号</DoctName>
                <RegDate>2024-08-02 10:55:44</RegDate>
                <BookFlag>0</BookFlag>
                <DeptAddress />
                <SeeFlag>已诊</SeeFlag>
                <OrderId>2408021055464233417511999900</OrderId>
                <PayType>CA</PayType>
                <RegCost>1.00</RegCost>
                <Noon>上午</Noon>
              </RegList>
            </Response>
        """.trimIndent()

        val response = JAXBKit.unmarshal(AvailableRegistrationResponseV1::class.java, xml)
        assertTrue(response.isOk(), response.msg)
        assertTrue(response.registrations.size == 4)

        val registration = response.registrations[0]
        assertTrue(registration.clinicNo == "3463333")
        assertTrue(registration.departmentName == "口腔科门诊")
        assertTrue(registration.doctorName == "普通号")
        assertTrue(registration.registerTime == "2024-08-21 10:47:35")
        assertTrue(registration.bookFlag == 0)
        assertTrue(registration.deptAddress == "")
        assertTrue(registration.seeFlag == "未诊")
        assertTrue(registration.orderId == "2408211047182961452082999900")
        assertTrue(registration.payType == "CA")
        assertTrue(registration.regCost == "18.00")
        assertTrue(registration.noon == "上午")
        assertTrue(registration.extra.isEmpty())
    }

}