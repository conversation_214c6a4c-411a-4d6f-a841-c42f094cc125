package org.mospital.dongruan

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class HttpWebServiceClientTest {

    // 测试用的WebServiceClient实现
    class TestWebServiceClient : WebServiceClient {
        var nextResponse: String = ""
        var lastCode: String = ""
        var lastXml: String = ""

        override fun process(code: String, xml: String): String {
            lastCode = code
            lastXml = xml
            return nextResponse
        }
    }

    @Test
    fun `test SOAP envelope building`() {
        val transCode = "1016"
        val xml = "<Request><MarkNO>************</MarkNO></Request>"
        
        val soapEnvelope = SoapEnvelopeUtils.buildSoapEnvelope(transCode, xml)
        
        assertTrue(soapEnvelope.contains("tem:transCode>1016</tem:transCode"))
        assertTrue(soapEnvelope.contains("<![CDATA[$xml]]>"))
        assertTrue(soapEnvelope.contains("xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\""))
        assertTrue(soapEnvelope.contains("xmlns:tem=\"http://tempuri.org/\""))
    }

    @Test
    fun `test SOAP response parsing`() {
        val soapResponse = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <soap:Body>
        <ProcessResponse xmlns="http://tempuri.org/">
            <ProcessResult>&lt;?xml version="1.0" encoding="utf-16"?&gt;
&lt;Response&gt;
  &lt;ResultCode&gt;1&lt;/ResultCode&gt;
  &lt;ResultMsg&gt;查询成功&lt;/ResultMsg&gt;
  &lt;MarkNO&gt;************&lt;/MarkNO&gt;
  &lt;AccountNO /&gt;
  &lt;CardNO&gt;**********&lt;/CardNO&gt;
  &lt;Name&gt;陈培如&lt;/Name&gt;
  &lt;Sex&gt;1&lt;/Sex&gt;
  &lt;IDNO /&gt;
  &lt;Vacancy&gt;0&lt;/Vacancy&gt;
  &lt;PhoneNum&gt;***********&lt;/PhoneNum&gt;
  &lt;MarkType&gt;1&lt;/MarkType&gt;
  &lt;ErhcCardNo&gt;************&lt;/ErhcCardNo&gt;
  &lt;IsDonationPerson&gt;0&lt;/IsDonationPerson&gt;
&lt;/Response&gt;</ProcessResult>
        </ProcessResponse>
    </soap:Body>
</soap:Envelope>"""

        val result = SoapEnvelopeUtils.extractProcessResult(soapResponse)
        
        assertTrue(result.contains("<ResultCode>1</ResultCode>"))
        assertTrue(result.contains("<ResultMsg>查询成功</ResultMsg>"))
        assertTrue(result.contains("<CardNO>**********</CardNO>"))
        assertTrue(result.contains("<Name>陈培如</Name>"))
    }

    @Test
    fun `test HTTP service creation with valid config`() {
        val config = DongruanConfig(
            httpUrl = "http://localhost:10011/",
            domain = "NEU_HZFW",
            key = "test-key",
            connectTimeout = 2000,
            requestTimeout = 10000
        )

        assertDoesNotThrow {
            DongruanService.createHttpInstance(config)
        }
    }

    @Test
    fun `test HTTP service creation with invalid config`() {
        val config = DongruanConfig(
            httpUrl = "",
            domain = "NEU_HZFW",
            key = "test-key"
        )

        assertThrows(IllegalArgumentException::class.java) {
            DongruanService.createHttpInstance(config)
        }
    }

    @Test
    fun `test DongruanService with HTTP client`() {
        val mockHttpClient = TestWebServiceClient()
        mockHttpClient.nextResponse = """<?xml version="1.0" encoding="utf-16"?>
<Response>
  <ResultCode>1</ResultCode>
  <ResultMsg>查询成功</ResultMsg>
  <MarkNO>************</MarkNO>
  <AccountNO></AccountNO>
  <CardNO>**********</CardNO>
  <Name>陈培如</Name>
  <Sex>1</Sex>
  <IDNO></IDNO>
  <Vacancy>0</Vacancy>
  <PhoneNum>***********</PhoneNum>
  <MarkType>1</MarkType>
  <ErhcCardNo>************</ErhcCardNo>
  <IsDonationPerson>0</IsDonationPerson>
</Response>"""

        val service = DongruanService.createForTest(mockHttpClient)
        val response = service.queryHospitalCard("************")

        assertTrue(response.code == 1)
        assertEquals("查询成功", response.msg)
        assertEquals("**********", response.cardNo)
        assertEquals("陈培如", response.name)
        assertEquals("1016", mockHttpClient.lastCode)
    }
} 