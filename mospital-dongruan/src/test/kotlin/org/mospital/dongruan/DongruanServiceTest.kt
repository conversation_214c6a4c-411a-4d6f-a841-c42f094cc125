package org.mospital.dongruan

import org.junit.jupiter.api.Test
import org.mospital.common.IdUtil
import org.mospital.dongruan.bean.*
import org.mospital.jackson.JacksonKit
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class DongruanServiceTest {

    @Test
    fun testGetTopDepartments() {
        val response: DepartmentResponse = DongruanService.me.getDepartments(DepartmentRequest(type = ""))
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testGetDepartments() {
        val response: DepartmentResponse = DongruanService.me.getDepartments(DepartmentRequest(type = "外科"))
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testGetDoctors() {
        val response: DoctorResponse =
            DongruanService.me.getDoctors(deptCode = "9908", date = LocalDate.now().plusDays(10))
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testGetSchedules() {
        val response: ScheduleResponse = DongruanService.me.getSchedules(
            deptCode = "9908",
            doctCode = "002158",
            date = LocalDate.now().plusDays(10)
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testGetSchedulesV1() {
        val response: ScheduleResponseV1 = DongruanService.me.getSchedulesV1(
            deptCode = "9207",
            doctCode = "002659",
            date = LocalDate.now().plusDays(1)
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testGetScheduleItems() {
        val response: ScheduleItemResponse = DongruanService.me.getScheduleItems(schid = "228561")
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testReserve() {
        val response: ReserveResponse = DongruanService.me.reserve(
            ReserveRequest(
                cardNo = "**********",
                schid = "179574",
                regType = 1,
                bankTransNo = IdUtil.simpleUUID()
            )
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testCancelReservation() {
        val response: CancelReservationResponse = DongruanService.me.cancelReservation(
            clinicNo = "920333"
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryHospitalCard() {
        val response: HospitalCardResponse = DongruanService.me.queryHospitalCard("**********")
        assertTrue(response.isOk(), response.msg)
        assertFalse(response.isDonationPerson)
    }

    @Test
    fun testQueryHospitalCardByIdCardNo() {
        val response: HospitalCardResponse = DongruanService.me.queryHospitalCard(markNo = "654002202202072612")
        assertTrue(response.isOk(), response.msg)
        println(JacksonKit.writeValueAsString(response))
    }

    @Test
    fun testQueryDrugByPinyin() {
        val response = DongruanService.me.queryDrug("FF")
        assertTrue(response.isOk(), response.msg)
        println(JacksonKit.writeValueAsString(response))
    }

    @Test
    fun testQueryDrugByHanzi() {
        val response = DongruanService.me.queryDrug("复方")
        assertTrue(response.isOk(), response.msg)
        println(JacksonKit.writeValueAsString(response))
    }

    @Test
    fun testQueryMaterialByPinyin() {
        val response = DongruanService.me.queryMaterial("ZSQ")
        assertTrue(response.isOk(), response.msg)
        println(JacksonKit.writeValueAsString(response))
    }

    @Test
    fun testQueryMaterialByHanzi() {
        val response = DongruanService.me.queryMaterial("注射器")
        assertTrue(response.isOk(), response.msg)
        println(JacksonKit.writeValueAsString(response))
    }

    @Test
    fun testQueryAvailableRegistrations() {
        val response: AvailableRegistrationResponse = DongruanService.me.queryAvailableRegistrations("0000587658")
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryPrescriptions() {
        val response: PrescriptionResponse = DongruanService.me.queryPrescriptions("3789876")
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryPrescriptionDetails() {
        val response: PrescriptionDetailResponse = DongruanService.me.queryPrescriptionDetails("3716485", "7335653")
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryMenzhenPatient() {
        val response: MenzhenPatientsResponse = DongruanService.me.queryMenzhenPatient(
            MenzhenPatientsRequest(
                name = "王小二",
                queryType = "2",
                queryValue = "654101198408030266"
            )
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryBill() {
        val response: BillResponse = DongruanService.me.queryBills(cardNo = "**********")
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryZhuyuanPatient() {
        val response: ZhuyuanPatientResponse = DongruanService.me.queryZhuyuanPatient(
            ZhuyuanPatientRequest(
                idNo = "**********",
                mode = 2
            )
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryZhuyuanFee() {
        val response: ZhuyuanFeeResponse = DongruanService.me.queryZhuyuanFee(
            ZhuyuanFeeRequest(
                inPatientNo = "ZY040000959165",
                date = LocalDate.now().minusDays(1)
            )
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryZhuyuanBills() {
        val response: ZhuyuanBillResponse = DongruanService.me.queryZhuyuanBills(
            ZhuyuanBillRequest(
                inPatientNo = "ZY01**********"
            )
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testCheck() {
        val response: CheckResponse = DongruanService.me.check(
            date = LocalDate.of(2022, 12, 8),
            payType = "JHWX"
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testRegisterHealthCard() {
        val response: HealthCardRegisterResponse = DongruanService.me.registerHealthCardV2(
            HealthCardRegisterRequestV2().apply {
                this.name = "王小二"
                this.phone = "***********"
                this.idCardNo = ""
                this.nation = "HA"
                this.sex = "1"
                this.address = "新疆伊犁"
                this.markNo = ""
                this.birthday = "1984-08-03"
                this.babyFlag = 1
                this.jhrName = "王二"
                this.jhrIdNo = "654101198408030266"
                this.transModel = HealthCardRegisterRequestV2.TransModel(bankTransNo = IdUtil.simpleUUID().take(30))
            }
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testQueryMedicineOrder() {
        val response: MedicineOrderResponse = DongruanService.me.queryMedicineOrder(
            MedicineOrderRequest(
                cardNO = "**********",
                applyBeginDate = LocalDate.now().minusMonths(2),
                applyEndDate = LocalDate.now()
            )
        )
        assertTrue(response.isOk(), response.msg)
    }

    @Test
    fun testPayMenzhen() {
        val response: MenzhenPaymentResponse = DongruanService.me.payMenzhen(
            MenzhenPaymentRequest(
                clinicNo = "856",
                recipeNo = "21160",
                transModel = TransModel(
                    outTradeNo = "MZ202408071322150946",
                    transactionId = "4200002296202408078886444859",
                    bankPayType = "JHWX",
                    bankPayCost = 0.01.toBigDecimal(),
                    bankPayTime = LocalDateTime.now()
                )
            )
        )
        assertTrue(response.isOk(), response.msg)
    }

}
