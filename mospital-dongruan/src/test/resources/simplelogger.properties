# SLF4J Simple Logger Configuration
# Output logs to standard output stream

# Default log level
org.slf4j.simpleLogger.defaultLogLevel=info

# Show date and time
org.slf4j.simpleLogger.showDateTime=true

# Date time format
org.slf4j.simpleLogger.dateTimeFormat=yyyy-MM-dd HH:mm:ss.SSS

# Show thread name
org.slf4j.simpleLogger.showThreadName=false

# Show logger name
org.slf4j.simpleLogger.showLogName=true

# Show short logger name
org.slf4j.simpleLogger.showShortLogName=true

# Show log level in brackets
org.slf4j.simpleLogger.levelInBrackets=true

# Output to standard output stream instead of standard error stream
org.slf4j.simpleLogger.logFile=System.out

# Log levels for specific packages
org.slf4j.simpleLogger.log.org.mospital=debug
org.slf4j.simpleLogger.log.okhttp3=warn
org.slf4j.simpleLogger.log.retrofit2=warn
