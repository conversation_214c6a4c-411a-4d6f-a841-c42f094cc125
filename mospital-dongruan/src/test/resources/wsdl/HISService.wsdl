<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" targetNamespace="http://tempuri.org/">
    <wsdl:types>
        <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
            <s:element name="Process">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="transCode" type="s:string"/>
                        <s:element minOccurs="0" maxOccurs="1" name="xml" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="ProcessResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" maxOccurs="1" name="ProcessResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="string" nillable="true" type="s:string"/>
        </s:schema>
    </wsdl:types>
    <wsdl:message name="ProcessSoapIn">
        <wsdl:part name="parameters" element="tns:Process"/>
    </wsdl:message>
    <wsdl:message name="ProcessSoapOut">
        <wsdl:part name="parameters" element="tns:ProcessResponse"/>
    </wsdl:message>
    <wsdl:message name="ProcessHttpGetIn">
        <wsdl:part name="transCode" type="s:string"/>
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ProcessHttpGetOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:message name="ProcessHttpPostIn">
        <wsdl:part name="transCode" type="s:string"/>
        <wsdl:part name="xml" type="s:string"/>
    </wsdl:message>
    <wsdl:message name="ProcessHttpPostOut">
        <wsdl:part name="Body" element="tns:string"/>
    </wsdl:message>
    <wsdl:portType name="ServiceSoap">
        <wsdl:operation name="Process">
            <wsdl:input message="tns:ProcessSoapIn"/>
            <wsdl:output message="tns:ProcessSoapOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:portType name="ServiceHttpGet">
        <wsdl:operation name="Process">
            <wsdl:input message="tns:ProcessHttpGetIn"/>
            <wsdl:output message="tns:ProcessHttpGetOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:portType name="ServiceHttpPost">
        <wsdl:operation name="Process">
            <wsdl:input message="tns:ProcessHttpPostIn"/>
            <wsdl:output message="tns:ProcessHttpPostOut"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="ServiceSoap" type="tns:ServiceSoap">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="Process">
            <soap:operation soapAction="http://tempuri.org/Process" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="ServiceSoap12" type="tns:ServiceSoap">
        <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="Process">
            <soap12:operation soapAction="http://tempuri.org/Process" style="document"/>
            <wsdl:input>
                <soap12:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap12:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="ServiceHttpGet" type="tns:ServiceHttpGet">
        <http:binding verb="GET"/>
        <wsdl:operation name="Process">
            <http:operation location="/Process"/>
            <wsdl:input>
                <http:urlEncoded/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:binding name="ServiceHttpPost" type="tns:ServiceHttpPost">
        <http:binding verb="POST"/>
        <wsdl:operation name="Process">
            <http:operation location="/Process"/>
            <wsdl:input>
                <mime:content type="application/x-www-form-urlencoded"/>
            </wsdl:input>
            <wsdl:output>
                <mime:mimeXml part="Body"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="Service">
        <wsdl:port name="ServiceSoap" binding="tns:ServiceSoap">
            <soap:address location="http://127.0.0.1:10008/Service.asmx"/>
        </wsdl:port>
        <wsdl:port name="ServiceSoap12" binding="tns:ServiceSoap12">
            <soap12:address location="http://127.0.0.1:10008/Service.asmx"/>
        </wsdl:port>
        <wsdl:port name="ServiceHttpGet" binding="tns:ServiceHttpGet">
            <http:address location="http://127.0.0.1:10008/Service.asmx"/>
        </wsdl:port>
        <wsdl:port name="ServiceHttpPost" binding="tns:ServiceHttpPost">
            <http:address location="http://127.0.0.1:10008/Service.asmx"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>