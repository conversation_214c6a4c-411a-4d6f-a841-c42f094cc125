# 东软HIS HTTP客户端支持

## 概述

`mospital-dongruan` 模块现已支持 WebService 和 HTTP 两种客户端实现，您可以根据实际需求选择合适的通信方式。

## 功能特性

- ✅ **向后兼容**: 现有代码无需修改即可继续使用
- ✅ **WebService客户端**: 传统SOAP协议支持
- ✅ **HTTP客户端**: 现代HTTP+SOAP混合架构
- ✅ **测试模式支持**: 两种客户端都支持测试环境切换
- ✅ **透明切换**: 相同的API接口，不同的底层实现
- ✅ **配置灵活**: 通过配置文件轻松切换客户端类型

## 配置说明

在 `dongruan.yaml` 文件中配置连接参数：

```yaml
# 基础配置
operator: "JHPHZF"
connectTimeout: 5000
requestTimeout: 30000
slowResponseThreshold: 2000

# WebService客户端配置（传统方式）
wsdlURL: "http://127.0.0.1:10008/Service.asmx?wsdl"
testWsdlURL: "http://127.0.0.1:7050/Service.asmx?wsdl"

# HTTP客户端配置（新增）
httpUrl: "http://localhost:10011/"
testHttpUrl: "http://localhost:7050/"
domain: "NEU_HZFW"
key: "f69897d6-d8a7-4c84-ac86-cca3b463b249"
```

## 使用示例

### WebService客户端（现有方式）

```kotlin
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.bean.HospitalCardRequest

// 使用默认的WebService客户端
val service = DongruanService.me

// 查询医院卡信息
val response = service.queryHospitalCard("999000015287")

if (response.isOk()) {
    println("查询成功: ${response.name}, 卡号: ${response.cardNo}")
} else {
    println("查询失败: ${response.resultMsg}")
}
```

### HTTP客户端（新增方式）

```kotlin
import org.mospital.dongruan.DongruanService
import org.mospital.dongruan.DongruanConfig

// 使用HTTP客户端（配置文件方式）
val service = DongruanService.httpInstance

// 或者自定义配置
val config = DongruanConfig(
    httpUrl = "http://your-server:10011/",
    domain = "YOUR_DOMAIN",
    key = "your-api-key",
    connectTimeout = 5000,
    requestTimeout = 30000
)
val customService = DongruanService.createHttpInstance(config)

// API调用方式完全相同
val response = service.queryHospitalCard("999000015287", testMode = true)
```

### 科室医生查询示例

```kotlin
// 查询科室列表
val deptRequest = DepartmentRequest()
val departments = service.getDepartments(deptRequest, testMode = false)

if (departments.isOk()) {
    departments.departments.forEach { dept ->
        println("科室: ${dept.name} (${dept.code})")
    }
}

// 查询医生列表
val doctors = service.getDoctors("DEPT001", LocalDate.now(), testMode = false)

// 预约挂号
val reserveRequest = ReserveRequestV1(
    cardNo = "**********",
    schid = "SCH001",
    regType = 1,
    bookIndexId = "BOOK001",
    isYiBaoPay = false,
    transModel = ReserveRequestV1.TransModel().apply {
        bankPayType = "JHWX"
        bankPayCost = BigDecimal("10.00")
        bankTransNo = "TXN123456789"
    }
)
val reserveResponse = service.reserveV1(reserveRequest, testMode = true)
```

## 技术实现

### WebService客户端
- 使用Apache CXF生成客户端代码
- 基于SOAP协议通信
- 支持WSDL自动代码生成
- 配置超时和连接参数

### HTTP客户端  
- 使用Retrofit + OkHttp实现
- 通过HTTP POST发送SOAP消息
- 自动添加domain和key请求头
- 支持请求ID日志追踪
- 高性能连接池管理

### SOAP消息处理
- 自动构建SOAP envelope
- 将业务XML嵌入CDATA中
- 解析SOAP响应提取ProcessResult
- XML实体自动编解码

## API对比

所有业务API在两种客户端下完全一致：

| 方法 | 说明 | WebService | HTTP |
|------|------|------------|------|
| `getDepartments()` | 查询科室列表 | ✅ | ✅ |
| `getDoctors()` | 查询医生列表 | ✅ | ✅ |
| `getSchedules()` | 查询排班信息 | ✅ | ✅ |
| `reserve()` | 预约挂号 | ✅ | ✅ |
| `queryHospitalCard()` | 查询医院卡 | ✅ | ✅ |
| `recharge()` | 账户充值 | ✅ | ✅ |
| `payMenzhen()` | 门诊缴费 | ✅ | ✅ |
| 所有其他API | 完整支持 | ✅ | ✅ |

## 性能对比

| 特性 | WebService客户端 | HTTP客户端 |
|------|------------------|------------|
| 协议 | SOAP over HTTP | HTTP POST |
| 性能 | 较慢 | 较快 |
| 连接池 | 有限支持 | 完整支持 |
| 日志追踪 | 基础 | 请求ID追踪 |
| 配置灵活性 | 低 | 高 |
| 错误处理 | 基础 | 增强 |

## 测试模式

两种客户端都支持测试模式，通过 `testMode` 参数控制：

```kotlin
// 生产环境调用
val response = service.getDepartments(request, testMode = false)

// 测试环境调用
val testResponse = service.getDepartments(request, testMode = true)
```

支持测试模式的交易码：
- 1002, 1003, 1004, 1004a, 1005, 1006, 1006a
- 1008, 1009, 1011, 1014, 1016

## 迁移指南

从WebService客户端迁移到HTTP客户端：

1. **更新配置文件**：添加httpUrl、domain、key字段
2. **更换服务实例**：将 `DongruanService.me` 改为 `DongruanService.httpInstance`
3. **API调用不变**：所有业务方法签名和返回值保持一致
4. **测试验证**：确保新的HTTP客户端在你的环境中正常工作

## 故障排查

### 常见错误及解决方案

#### 1. 配置错误
```
IllegalArgumentException: httpUrl cannot be blank
```
**解决方案**: 检查配置文件中的httpUrl字段是否正确设置

#### 2. 网络连接问题
```
HTTP request failed with code: 404
```
**解决方案**:
- 检查httpUrl是否正确
- 确认服务端点是否可访问
- 验证网络连接

#### 3. 认证失败
```
HTTP request failed with code: 401/403
```
**解决方案**:
- 检查domain和key配置是否正确
- 确认API密钥是否有效
- 联系服务提供方确认认证信息

#### 4. SOAP解析错误
```
ProcessResult not found in SOAP response
```
**解决方案**:
- 检查服务端返回的响应格式
- 确认服务端接口版本兼容性

## 最佳实践

1. **新项目使用HTTP客户端**：性能更好，功能更丰富
2. **现有项目渐进迁移**：先验证HTTP客户端稳定性，再逐步切换
3. **配置环境隔离**：生产、测试环境使用不同的配置
4. **日志监控**：关注响应时间和错误率
5. **异常处理**：合理处理网络异常和业务异常

## curl命令示例

以下是使用HTTP客户端的curl命令示例：

```bash
curl -X "POST" "http://localhost:10011/xcx/Service/op/Process" \
     -H 'domain: NEU_HZFW' \
     -H 'key: f69897d6-d8a7-4c84-ac86-cca3b463b249' \
     -H 'SOAPAction: http://tempuri.org/Process' \
     -H 'Content-Type: text/xml;charset=UTF-8' \
     -d '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:Process>
         <tem:transCode>1016</tem:transCode>
         <tem:xml><![CDATA[<Request>
         <MarkNO>999000015287</MarkNO>
         </Request>]]></tem:xml>
      </tem:Process>
   </soapenv:Body>
</soapenv:Envelope>'
```

## 依赖说明

HTTP客户端新增以下依赖：
- `retrofit2` - HTTP客户端框架
- `okhttp3` - HTTP客户端实现
- `okhttp3:logging-interceptor` - 日志拦截器

这些依赖在父POM中已统一管理版本。 