package org.mospital.bosi.ebill

import org.mospital.jackson.ConfigLoader

object EBillSetting {
    private val config: EBillConfig by lazy {
        ConfigLoader.loadConfig("bosi-ebill.yaml", EBillConfig::class.java, EBillSetting::class.java)
    }

    val url: String get() = config.url
    val appId: String get() = config.appId
    val appKey: String get() = config.appKey
}

private data class EBillConfig(
    val url: String = "",
    val appId: String = "",
    val appKey: String = ""
)
