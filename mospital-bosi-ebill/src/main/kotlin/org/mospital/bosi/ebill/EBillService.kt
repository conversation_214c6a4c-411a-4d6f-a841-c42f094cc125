package org.mospital.bosi.ebill

import org.apache.commons.codec.digest.DigestUtils
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

class EBillService {
    companion object {

        private fun getSign(paramMap: Map<String, String>): String {

            // 对所有传入参数按照字段名的 ASCII 码从小到大排序(字典序)
            val keySet = paramMap.keys
            val keyArray = keySet.toTypedArray()
            keyArray.sort()

            // 使用 URL 键值对的格式（即 key1=value1&key2=value2…）拼接成字符串 stringA
            val sb = StringBuilder()

            for (k in keyArray) {
                if (paramMap[k] != null && "" != paramMap[k]) {
                    sb.append(paramMap[k])
                }
            }

            return DigestUtils.md5Hex("${EBillSetting.appKey}${sb}${EBillSetting.appKey}").uppercase()
        }

        fun queryEBillUrl(requestBody: EBillRequest): String {
            val sign = getSign(
                mapOf(
                    "appId" to EBillSetting.appId,
                    "idCard" to requestBody.idCard,
                    "name" to requestBody.name,
                    "phone" to requestBody.phone,
                )
            )

            var url = EBillSetting.url.removeSuffix("?")
            url += "?name=${URLEncoder.encode(requestBody.name, StandardCharsets.UTF_8)}"
            url += "&phone=${URLEncoder.encode(requestBody.phone, StandardCharsets.UTF_8)}"
            url += "&idCard=${URLEncoder.encode(requestBody.idCard, StandardCharsets.UTF_8)}"
            url += "&appId=${URLEncoder.encode(EBillSetting.appId, StandardCharsets.UTF_8)}"
            url += "&security=${URLEncoder.encode(sign, StandardCharsets.UTF_8)}"
            return url
        }
    }
}