package org.mospital.alipay

/**
 * Alipay 配置类
 * 对应 alipay.yaml 配置文件
 */
data class AlipayConfig(
    val ma: MaConfig,
    val mip: MipConfig
)

/**
 * MA (小程序) 相关配置
 */
data class MaConfig(
    val appId: String,
    val merchantName: String,
    val merchantId: String,
    val pid: String,
    val encryptType: String,
    val encryptKey: String,
    val notifyUrl: String,
    val merchantPrivateKey: String,
    val merchantCertPath: String,
    val alipayCertPath: String,
    val alipayRootCertPath: String,
    val charset: String,
    val signType: String,
    val format: String,
    val serverUrl: String,
    val protocol: String
)

/**
 * MIP (医保移动支付) 相关配置
 */
data class MipConfig(
    val orgAppId: String,
    val orgChnlCrtfCode: String,
    val orgCode: String,
    val orgName: String,
    val orgCityCode: String,
    val callUrl: String,
    val notifyUrl: String
) 