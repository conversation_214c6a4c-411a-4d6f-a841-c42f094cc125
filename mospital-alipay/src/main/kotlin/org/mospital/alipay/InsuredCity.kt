package org.mospital.alipay

import com.alibaba.fastjson.JSONObject


data class InsuredCity(
    val code: String,
    val name: String,
    val isDefaultCity: String
) {
    fun isDefaultCity(): Boolean = isDefaultCity == "Y"

    constructor(jsonObject: JSONObject) : this(
        code = jsonObject.getString("ins_city_code"),
        name = jsonObject.getString("ins_city_name"),
        isDefaultCity = jsonObject.getString("is_default_city")
    )

}
