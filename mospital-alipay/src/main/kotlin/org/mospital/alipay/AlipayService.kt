package org.mospital.alipay

import com.alibaba.fastjson.JSONObject
import com.alipay.api.AlipayRequest
import com.alipay.api.AlipayResponse
import com.alipay.api.CertAlipayRequest
import com.alipay.api.DefaultAlipayClient
import com.alipay.api.domain.*
import com.alipay.api.internal.util.AlipayLogger
import com.alipay.api.request.*
import com.alipay.api.response.*
import org.dromara.hutool.core.net.url.RFC3986
import org.dromara.hutool.core.text.StrUtil
import org.mospital.common.IdUtil
import org.mospital.jackson.DateTimeFormatters
import java.io.File
import java.math.BigDecimal
import java.math.RoundingMode
import java.nio.charset.StandardCharsets
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import kotlin.math.min

@Suppress("unused")
object AlipayService {

    val alipayClient = DefaultAlipayClient(
        CertAlipayRequest().apply {
            this.serverUrl = AlipaySetting.maServerUrl
            this.appId = AlipaySetting.maAppId
            this.privateKey = AlipaySetting.maMerchantPrivateKey
            this.format = AlipaySetting.maFormat
            this.charset = AlipaySetting.maCharset
            this.signType = AlipaySetting.maSignType
            this.certPath = AlipaySetting.maMerchantCertPath
            this.rootCertPath = AlipaySetting.maAlipayRootCertPath
            this.alipayPublicCertPath = AlipaySetting.maAlipayCertPath
            this.encryptType = AlipaySetting.maEncryptType
            this.encryptor = AlipaySetting.maEncryptKey
        }
    )

    /**
     * 实名信息脱敏工具类
     * **See Also:** [应用安全开发指南](https://opendocs.alipay.com/open/200/security#%E2%80%8B%0A%E6%95%8F%E6%84%9F%E4%BF%A1%E6%81%AF%E7%94%A8%E4%BA%8E%E5%B1%95%E7%A4%BA%E7%9A%84%E5%9C%BA%E6%99%AF)
     */

    /**
     * 脱敏身份证号
     */
    fun hideIdCardNo(idCardNo: String): String {
        return if (idCardNo.length == 18) {
            StrUtil.hide(idCardNo, 1, 17)
        } else {
            StrUtil.hide(idCardNo, 1, 14)
        }
    }

    /**
     * 脱敏姓名
     */
    fun hideName(name: String): String {
        return when (name.length) {
            1 -> "*"
            else -> StrUtil.hide(name, 0, name.length - 1)
        }
    }

    /**
     * 脱敏手机号
     */
    fun hideMobile(mobile: String): String {
        return StrUtil.hide(mobile, 3, 9)
    }

    fun <T : AlipayResponse> execute(request: AlipayRequest<T>, accessToken: String? = null): T {
        val traceId: String = IdUtil.simpleUUID()
        AlipayLogger.logBizDebug(
            "AlipayRequest[${request::class.java.canonicalName}]#$traceId: ${
                JSONObject.toJSONString(
                    request
                )
            }"
        )
        val response: T = if (accessToken.isNullOrBlank()) {
            alipayClient.certificateExecute(request)
        } else {
            alipayClient.certificateExecute(request, accessToken)
        }
        AlipayLogger.logBizDebug(
            "AlipayResponse[${response::class.java.canonicalName}]#$traceId: ${
                JSONObject.toJSONString(
                    response
                )
            }"
        )
        return response
    }

    fun <T : AlipayResponse> sdkExecute(request: AlipayRequest<T>): T {
        val traceId: String = IdUtil.simpleUUID()
        AlipayLogger.logBizDebug(
            "AlipayRequest[${request::class.java.canonicalName}]#$traceId: ${
                JSONObject.toJSONString(
                    request
                )
            }"
        )
        val response: T = alipayClient.sdkExecute(request)
        AlipayLogger.logBizDebug(
            "AlipayResponse[${response::class.java.canonicalName}]#$traceId: ${
                JSONObject.toJSONString(
                    response
                )
            }"
        )
        return response
    }

    fun getOrRefreshAccessToken(
        authCode: String,
        grantType: GrantType = GrantType.AUTHORIZATION_CODE,
    ): AlipaySystemOauthTokenResponse {
        val request: AlipaySystemOauthTokenRequest = AlipaySystemOauthTokenRequest()
        request.grantType = grantType.code
        when (grantType) {
            GrantType.AUTHORIZATION_CODE -> {
                request.code = authCode
            }

            GrantType.REFRESH_TOKEN -> {
                request.refreshToken = authCode
            }
        }
        return execute(request)
    }

    fun getUserInfoShare(authToken: String): AlipayUserInfoShareResponse {
        val request: AlipayUserInfoShareRequest = AlipayUserInfoShareRequest()
        request.putOtherTextParam("auth_token", authToken)
        return execute(request)
    }

    /**
     * 蚂蚁森林能量发放
     * 参见：https://opendocs.alipay.com/pre-open/023n6i、https://opendocs.alipay.com/apis/0247h4
     * @param authCode 授权码
     * @param scene 能量发放场景值
     * @param outerNo 外部业务号，用作幂等。同一场景下，一条外部业务号只可消费一次。
     * @param extInfo 扩展信息
     */
    fun sendEnergy(
        authCode: String,
        scene: EnergyScene,
        outerNo: String,
        extInfo: Map<String, String> = emptyMap(),
    ): AlipayEcoCityserviceCityserviceEnergySendResponse {
        val oauthTokenResponse: AlipaySystemOauthTokenResponse = getOrRefreshAccessToken(authCode = authCode)
        if (!oauthTokenResponse.isSuccess) {
            return AlipayEcoCityserviceCityserviceEnergySendResponse().apply {
                this.code = oauthTokenResponse.code
                this.msg = oauthTokenResponse.msg
                this.subCode = oauthTokenResponse.subCode
                this.subMsg = oauthTokenResponse.subMsg
            }
        }

        val extInfoString: String = extInfo.entries.joinToString(separator = ",") {
            """{
                "ext_key": "${it.key}",
                "ext_value": "${it.value}"
            }""".trimIndent()
        }
        val request: AlipayEcoCityserviceCityserviceEnergySendRequest =
            AlipayEcoCityserviceCityserviceEnergySendRequest().apply {
                this.bizContent = ("""{
                "scene": "${scene.value}",
                "outer_no": "$outerNo",
                "ext_info": [$extInfoString]
            }""".trimIndent())
            }
        return execute(request = request, accessToken = oauthTokenResponse.accessToken)
    }

    /**
     * 智能消息推送 - 推送挂号单
     * 参见：https://opendocs.alipay.com/pre-open/01odaz
     * 推送同一挂号单不同状态时，createTime 必须保持一致，outBizNo 必须保持一致
     *
     * @param accessToken 智能消息推送的授权码
     * @param userId 目标用户的支付宝用户ID
     * @param outBizNo 预约单订单号，必须确保唯一性。推送同一挂号单的不同状态时，必须保证 outBizNo 一致。
     * @param createTime 订单创建时间。推送同一挂号单的不同状态时，必须保证 createTime 一致。
     * @param updateTime 订单修改时间
     * @param fee 挂号费
     * @param tradeNo 支付宝交易号，支付宝支付之后传入，非支付宝交易不传入
     * @param orderStatus 订单状态
     * @param skuId 挂号项ID
     * @param departmentName 就诊科室
     * @param departmentNum 诊室编号
     * @param departmentLocation 科室位置
     * @param navigationUrl 导航页面的链接
     * @param doctorName 医生姓名，可以为空
     * @param doctorRank 医生职级，可以为空
     * @param doctorId 医生ID，可以为空
     * @param doctorAvatarUrl 医生头像的链接，可以为空
     * @param patientName 就诊人姓名
     * @param scheduledTime 预约的就诊时间
     * @param takeNumUrl 取号入口页面的跳转地址，可以为空
     * @param takeNumPasswordUrl 取号密码页面的跳转地址，可以为空
     * @param callNumUrl 取号进度页面的跳转地址，可以为空
     * @param medicalOrderId 就诊单id，可以为空
     * @param medicalNum 就诊序号
     * @param linkPage 订单链接
     */
    fun pushHospitalOrder(
        accessToken: String,
        userId: String,
        outBizNo: String,
        createTime: Date,
        updateTime: Date,
        fee: BigDecimal,
        tradeNo: String = "",
        orderStatus: HospitalOrderStatus,
        skuId: String,
        departmentName: String,
        departmentNum: String = "",
        departmentLocation: String,
        navigationUrl: String = "",
        doctorName: String = "",
        doctorRank: String = "",
        doctorId: String = "",
        doctorAvatarUrl: String = "",
        patientName: String,
        scheduledTime: Date,
        takeNumUrl: String = "",
        takeNumPasswordUrl: String = "",
        callNumUrl: String = "",
        medicalOrderId: String = "",
        medicalNum: String,
        linkPage: String,
    ): AlipayCommerceAppAuthUploadResponse {
        val amount = fee.setScale(2, RoundingMode.HALF_UP).toString()
        val request = AlipayCommerceAppAuthUploadRequest().apply {
            // 应用服务名称，固定值
            this.serviceName = "alipay.commerce.app.data"

            // 目标用户的支付宝用户ID，根据文档判断应该是指ISV的PID
            this.targetId = AlipaySetting.maPid

            this.content = CommerceAppUploadRequestContent().apply {
                // 业务流程ID，固定值
                this.activityId = "upload_hospital_order"

                // 租户应用ID，支付宝分配
                // 文档中相关说明：4、创建租户 请联系支付宝业务BD申请获取，获取后才能调用以下接口
                this.tenantAppId = AlipaySetting.maTenantAppId

                this.body = JSONObject.toJSONString(
                    mapOf(
                        // 医院预约单订单号，必须确保唯一性
                        "out_biz_no" to outBizNo,

                        // ISV的PID，2088开头
                        "partner_id" to AlipaySetting.maPid,

                        // 就诊人id，授权人id，就诊人在支付宝平台的2088开头的16位id
                        "buyer_id" to userId,

                        // 医院在支付宝的小程序id
                        "tiny_app_id" to AlipaySetting.maAppId,

                        "order_create_time" to SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(createTime),
                        "order_modified_time" to SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(updateTime),
                        "amount" to amount,
                        "pay_amount" to amount,

                        // 支付宝交易号，支付宝支付之后传入，非支付宝交易不传入
                        "trade_no" to tradeNo,

                        // 订单类型，固定值
                        "order_type" to "HOSPITAL_ORDER",

                        // 外部订单类型，固定值
                        "out_biz_type" to "HOSPITAL_APPOINTMENT",

                        // 商户订单状态
                        "merchant_order_status" to orderStatus.name,

                        "item_order_list" to listOf(
                            mapOf(
                                // 商品名称，固定值
                                "item_name" to "挂号单",

                                // 商品数量，统一写 1
                                "quantity" to "1",

                                // 挂号项ID
                                "sku_id" to skuId,
                                "unit_price" to amount,
                            )
                        ),
                        "ext_info" to mapOf(
                            // 医院名称
                            "hospital" to AlipaySetting.maHospitalName,
                            // 医院登记号
                            "hospital_register_id" to AlipaySetting.maHospitalRegisterId,
                            "department" to departmentName,
                            "dept_num" to departmentNum,
                            "dept_loc" to departmentLocation,
                            "navigation" to navigationUrl,
                            "doctor" to doctorName,
                            "doctor_rank" to doctorRank,
                            "doctor_id" to doctorId,
                            "doctor_avatar" to doctorAvatarUrl,
                            "patient" to patientName,
                            "scheduled_time" to SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(scheduledTime),
                            "take_num_url" to takeNumUrl,
                            "take_num_password" to takeNumPasswordUrl,
                            "call_num_url" to callNumUrl,
                            "medical_order_id" to medicalOrderId,
                            "medical_num" to medicalNum,
                            "merchant_order_link_page" to linkPage
                        )
                    )
                )
            }
        }
        return execute(request = request, accessToken = accessToken)
    }

    fun createOrder(
        totalAmount: BigDecimal,
        openid: String,
        outTradeNo: String,
        subject: String,
        body: String,
    ): AlipayTradeCreateResponse {
        val model: AlipayTradeCreateModel = AlipayTradeCreateModel().apply {
            this.outTradeNo = outTradeNo
            this.totalAmount = totalAmount.setScale(2, RoundingMode.HALF_UP).toString()
            this.subject = subject
            this.body = body
            this.buyerId = openid
            this.extendParams = ExtendParams().apply {
                this.sysServiceProviderId = AlipaySetting.maPid
            }
        }
        val request = AlipayTradeCreateRequest()
        request.bizModel = model
        request.notifyUrl = AlipaySetting.maNotifyUrl
        return execute(request)
    }

    fun queryOrder(
        outTradeNo: String = "",
        tradeNo: String = "",
        queryOptions: List<String> = emptyList(),
    ): AlipayTradeQueryResponse {
        require(outTradeNo.isNotBlank() || tradeNo.isNotBlank()) {
            "支付宝交易号和商户订单号不能同时为空"
        }
        val model = AlipayTradeQueryModel()
        model.outTradeNo = outTradeNo
        model.tradeNo = tradeNo
        model.queryOptions = queryOptions
        val request = AlipayTradeQueryRequest()
        request.bizModel = model
        return execute(request)
    }

    /**
     * 退款
     * @param refundAmount 退款金额，以元为单位
     * @param tradeNo 支付宝订单号
     * @param outRequestNo 退款请求号
     */
    fun refund(refundAmount: BigDecimal, tradeNo: String, outRequestNo: String): AlipayTradeRefundResponse {
        val model = AlipayTradeRefundModel()
        model.tradeNo = tradeNo
        model.refundAmount = refundAmount.setScale(2, RoundingMode.HALF_UP).toString()
        model.outRequestNo = outRequestNo
        val request = AlipayTradeRefundRequest()
        request.bizModel = model
        return execute(request)
    }

    fun refundMip(
        refundAmount: BigDecimal,
        tradeNo: String,
        outTradeNo: String,
        outRequestNo: String,
        cancelSerialNo: String,
        cancelBillNo: String,
    ): AlipayTradeRefundApplyResponse {
        val extendParams: JSONObject = JSONObject().apply {
            this.put("cancel_serial_no", cancelSerialNo)
            this.put("cancel_bill_no", cancelBillNo)
        }

        val bizContent: JSONObject = JSONObject().apply {
            this.put("trade_no", tradeNo)
            this.put("out_trade_no", outTradeNo)
            this.put("refund_amount", refundAmount.setScale(2, RoundingMode.HALF_UP).toString())
            this.put("out_request_no", outRequestNo)
            this.put("refund_reason", "退费")
            this.put("extend_params", extendParams)
        }

        val request = AlipayTradeRefundApplyRequest()
        request.bizContent = JSONObject.toJSONString(bizContent)
        return execute(request)
    }

    /**
     * <a href="https://opendocs.alipay.com/apis/028xqc">统一收单交易退款查询</a>
     * @param outRequestNo 退款请求号
     * @param outTradeNo 商户订单号
     * @param tradeNo 支付宝交易号
     */
    fun queryRefund(
        outRequestNo: String,
        outTradeNo: String = "",
        tradeNo: String = "",
        queryOptions: List<String> = listOf("refund_detail_item_list", "gmt_refund_pay", "deposit_back_info"),
    ): AlipayTradeFastpayRefundQueryResponse {
        require(outRequestNo.isNotBlank()) {
            "退款请求号不能为空"
        }
        require(tradeNo.isNotBlank() || outTradeNo.isNotBlank()) {
            "支付宝交易号和商户订单号不能同时为空"
        }
        val model = AlipayTradeFastpayRefundQueryModel()
        model.outRequestNo = outRequestNo
        model.tradeNo = tradeNo
        model.outTradeNo = outTradeNo
        model.queryOptions = queryOptions
        val request = AlipayTradeFastpayRefundQueryRequest()
        request.bizModel = model
        return execute(request)
    }

    fun buildBillPath(date: LocalDate): String {
        return StringBuilder().apply {
            append(AlipaySetting.maBillDownloadPath)
            if (!AlipaySetting.maBillDownloadPath.endsWith(File.separator)) {
                append(File.separator)
            }
            append(AlipaySetting.maMerchantId)
            append('_')
            append(date.format(DateTimeFormatters.PURE_DATE_FORMATTER))
            append(".csv.zip")
        }.toString()
    }

    fun queryBillDownloadUrl(
        date: LocalDate,
        billType: BillType = BillType.TRADE
    ): AlipayDataDataserviceBillDownloadurlQueryResponse {
        val request = AlipayDataDataserviceBillDownloadurlQueryRequest().apply {
            this.bizModel = AlipayDataDataserviceBillDownloadurlQueryModel().apply {
                this.billType = billType.code
                this.billDate = date.format(DateTimeFormatters.NORM_DATE_FORMATTER)
            }
        }
        return execute(request)
    }

    /**
     * 医保移动支付授权信息查询
     * https://opendocs.alipay.com/pre-apis/021jtx
     */
    fun commerceMedicalAuthInfoQuery(
        alipayUserId: String,
        accessToken: String,
        patientName: String = "",
        patientCertNo: String = "",
        patientCertType: String = "01",
        callbackPage: String? = null,
    ): AlipayCommerceMedicalAuthinfoAuthQueryResponse {
        val callUrl = if (callbackPage.isNullOrBlank()) {
            AlipaySetting.mipCallUrl
        } else {
            "alipays://platformapi/startapp?appId=${AlipaySetting.maAppId}&page=" + RFC3986.SEGMENT.encode(
                callbackPage,
                StandardCharsets.UTF_8
            )
        }
        val bizModel: AlipayCommerceMedicalAuthinfoAuthQueryModel =
            AlipayCommerceMedicalAuthinfoAuthQueryModel().apply {
                this.alipayUserId = alipayUserId
                // 定点医疗机构应用ID，即医药机构渠道编号，参见反馈单中的渠道编号
                this.orgAppId = AlipaySetting.mipOrgAppId
                // 业务请求唯一流水号，请务必保持每次请求的唯一性
                this.reqBizNo = IdUtil.simpleUUID()
                // 固定为NATHSA
                this.insCode = "NATHSA"
                // 授权成功后回调医药机构前端地址
                this.callUrl = callUrl
                // 线上业务类型编码
                this.olBizTypeCode = "04107"
                // 机构渠道认证编码，参见反馈单中的机构渠道认证编码
                this.orgChnlCrtfCode = AlipaySetting.mipOrgChnlCrtfCode
                // 定点医药机构编码，参见反馈单中的定点医药机构编码
                this.orgCode = AlipaySetting.mipOrgCode
                this.extendParams = AuthExtendParams().apply {
                    this.sysServiceProviderId = AlipaySetting.maPid
                    if (patientName.isNotBlank() && patientCertNo.isNotBlank()) {
                        this.patientName = patientName
                        this.patientCertNo = patientCertNo
                        this.patientCertType = patientCertType
                    }
                }
            }
        val request = AlipayCommerceMedicalAuthinfoAuthQueryRequest()
        request.bizModel = bizModel
        return execute(request, accessToken)
    }

    fun commerceMedicalCardAuthQuery(accessToken: String): AlipayCommerceMedicalCardAuthQueryResponse {
        val bizModel = AlipayCommerceMedicalCardAuthQueryModel().apply {
            this.insCode = "NATHSA"
            this.relationType = "00"
        }
        val request = AlipayCommerceMedicalCardAuthQueryRequest()
        request.bizModel = bizModel
        return execute(request, accessToken)
    }

    @Deprecated("Use parseInsuredCities instead")
    fun parseCanbaoCity(alipayCommerceMedicalCardAuthQueryResponse: AlipayCommerceMedicalCardAuthQueryResponse): Array<String> {
        var cityName: String? = null
        var cityId: String? = null
        if (!alipayCommerceMedicalCardAuthQueryResponse.isSuccess) {
            return arrayOf("", "")
        }
        try {
            val jsonObject: JSONObject = JSONObject.parseObject(alipayCommerceMedicalCardAuthQueryResponse.body)
                .getJSONObject("alipay_commerce_medical_card_auth_query_response")
                .getJSONArray("data")
                .getJSONObject(0)
                .getJSONArray("insured_cities")
                .getJSONObject(0)
            cityId = jsonObject.getString("city_code") ?: jsonObject.getString("ins_city_code")
            cityName = jsonObject.getString("ins_city_name")
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return arrayOf(cityId ?: "", cityName ?: "")
    }

    fun parseInsuredCities(alipayCommerceMedicalCardAuthQueryResponse: AlipayCommerceMedicalCardAuthQueryResponse): List<InsuredCity> {
        if (!alipayCommerceMedicalCardAuthQueryResponse.isSuccess) {
            return emptyList()
        }
        return try {
            JSONObject.parseObject(alipayCommerceMedicalCardAuthQueryResponse.body)
                .getJSONObject("alipay_commerce_medical_card_auth_query_response")
                .getJSONArray("data")
                .getJSONObject(0)
                .getJSONArray("insured_cities")
                .map { InsuredCity(it as JSONObject) }
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 创建医保订单
     * @param outTradeNo 商户订单号
     * @param totalAmount 订单金额，以元为单位
     * @param medOrgOrd 医疗机构订单号，对应医保移动支付中心费用明细上传接口的入参 medOrgOrd
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     * @param payAuthNo 医保支付授权码
     * @param medicalCardInstId 医保电子凭证机构号，获取线上医保业务授权接口返回此参数
     * @param medicalCardId 医保电子凭证授权码，获取线上医保业务授权接口返回此参数
     * @param medicalRequestContent 医保参数透传体，没有需传空对象
     */
    fun createMipOrder(
        outTradeNo: String,
        totalAmount: BigDecimal,
        medOrgOrd: String,
        payOrderId: String,
        payAuthNo: String,
        medicalCardInstId: String,
        medicalCardId: String,
        subject: String,
        medicalRequestContent: JSONObject = JSONObject(),
    ): AlipayTradeAppPayResponse {
        val request = AlipayTradeAppPayRequest()
        request.notifyUrl = AlipaySetting.mipNotifyUrl

        val medicalRequestExt: JSONObject = JSONObject().apply {
            this.put("gmt_out_create", LocalDateTime.now().format(DateTimeFormatters.NORM_DATETIME_FORMATTER))
            this.put("out_trade_no", medOrgOrd)
            this.put("serial_no", payOrderId)
            this.put("bill_no", payOrderId)
            this.put("industry", "HOSPITAL")
            this.put("org_no", AlipaySetting.mipOrgCode)
            this.put("org_name", AlipaySetting.mipOrgName)
            this.put("pay_auth_no", payAuthNo)
            this.put("chinfo", "gjydzf")
            this.put("scene", "TREATMENT")
            this.put("insurance_pay_scene", "OUTPATIENT")
            this.put("payment_city_code", AlipaySetting.mipOrgCityCode)
        }

        val extendParams: JSONObject = JSONObject().apply {
            this.put("sys_service_provider_id", AlipaySetting.maPid)
            this.put("IS_INSURANCE_PAY", "T")
            this.put("medical_card_inst_id", medicalCardInstId)
            this.put("medical_card_id", medicalCardId)
            this.put("medical_request_content", JSONObject.toJSONString(medicalRequestContent))
            this.put("medical_request_ext", JSONObject.toJSONString(medicalRequestExt))
        }

        val bizContent: JSONObject = JSONObject().apply {
            this.put("out_trade_no", outTradeNo)
            this.put("total_amount", totalAmount.setScale(2, RoundingMode.HALF_UP).toString())
            this.put("subject", subject)
            this.put("product_code", "QUICK_MSECURITY_PAY")
            this.put(
                "time_expire",
                LocalDateTime.now().plusMinutes(10).format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
            )
            this.put("extend_params", extendParams)
        }
        request.bizContent = JSONObject.toJSONString(bizContent)
        return sdkExecute(request)
    }

    /**
     * 医疗行业医院数据上传
     * 参见：https://opendocs.alipay.com/pre-open/031a45、https://opendocs.alipay.com/pre-apis/02x1v3
     * @param
     */
    fun uploadHospitalMedicalIndustryData(hospitalDataList: List<HospitalData>): AlipayCommerceMedicalIndustrydataHospitalUploadResponse {
        val bizModel = AlipayCommerceMedicalIndustrydataHospitalUploadModel()
        bizModel.requestId = IdUtil.simpleUUID()
        bizModel.isvPid = AlipaySetting.maPid
        bizModel.hospitalList = hospitalDataList

        val request = AlipayCommerceMedicalIndustrydataHospitalUploadRequest()
        request.bizModel = bizModel
        return execute(request)
    }

    /**
     * 医院科室信息上传
     * 参见：https://opendocs.alipay.com/pre-open/031a45、https://opendocs.alipay.com/pre-apis/02x1v2
     */
    fun uploadDepartmentMedicalIndustryData(departmentDataList: List<DepartmentData>): List<AlipayCommerceMedicalIndustrydataDepartmentUploadResponse> {
        return (0..departmentDataList.size / 100).map {
            val bizModel = AlipayCommerceMedicalIndustrydataDepartmentUploadModel()
            bizModel.requestId = IdUtil.simpleUUID()
            bizModel.isvPid = AlipaySetting.maPid
            bizModel.departmentList = departmentDataList.subList(it * 100, min((it + 1) * 100, departmentDataList.size))

            val request = AlipayCommerceMedicalIndustrydataDepartmentUploadRequest()
            request.bizModel = bizModel
            execute(request)
        }
    }
}
