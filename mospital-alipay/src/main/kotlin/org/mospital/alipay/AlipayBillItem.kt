package org.mospital.alipay

import org.dromara.hutool.core.compress.ZipUtil
import org.dromara.hutool.core.func.SerConsumer
import org.dromara.hutool.core.io.IoUtil
import org.mospital.jackson.DateTimeFormatters
import java.io.File
import java.math.BigDecimal
import java.nio.charset.Charset
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.function.Consumer
import java.util.zip.ZipEntry
import java.util.zip.ZipFile

@Suppress("unused")
data class AlipayBillItem(
    /**
     * 账单日
     */
    val billDate: LocalDate,

    /**
     * 支付宝交易号
     */
    val tradeNo: String,

    /**
     * 商户订单号
     */
    val outTradeNo: String,

    /**
     * 业务类型：交易、退款
     */
    val tradeType: String,

    /**
     * 商品名称
     */
    val productName: String,

    /**
     * 创建时间
     */
    val createTime: LocalDateTime,

    /**
     * 完成时间
     */
    val completeTime: LocalDateTime,

    /**
     * 门店编号
     */
    val storeId: String,

    /**
     * 门店名称
     */
    val storeName: String,

    /**
     * 操作员
     */
    val operatorId: String,

    /**
     * 终端号
     */
    val terminalId: String,

    /**
     * 对方账户
     */
    val buyer: String,

    /**
     * 订单金额（元）
     */
    val totalAmount: BigDecimal,

    /**
     * 商家实收（元）
     */
    val receiptAmount: BigDecimal,

    /**
     * 支付宝红包（元）
     */
    val alipayRedpackAmount: BigDecimal,

    /**
     * 集分宝（元）
     */
    val pointAmount: BigDecimal,

    /**
     * 支付宝优惠（元）
     */
    val alipayDiscountAmount: BigDecimal,

    /**
     * 商家优惠（元）
     */
    val merchantDiscountAmount: BigDecimal,

    /**
     * 券核销金额（元）
     */
    val couponAmount: BigDecimal,

    /**
     * 券名称
     */
    val couponName: String,

    /**
     * 商家红包消费金额（元）
     */
    val merchantRedpackAmount: BigDecimal,

    /**
     * 卡消费金额（元）
     */
    val cardAmount: BigDecimal,

    /**
     * 退款批次号/请求号
     */
    val refundNo: String,

    /**
     * 服务费（元）
     */
    val fee: BigDecimal,

    /**
     * 分润（元）
     */
    val profitAmount: BigDecimal,

    /**
     * 备注
     */
    val remark: String
) {
    companion object {

        private fun parseLocalDateTime(s: String): LocalDateTime =
            LocalDateTime.parse(s, DateTimeFormatters.NORM_DATETIME_FORMATTER)

        private fun parseBigDecimal(s: String): BigDecimal =
            s.toBigDecimal()
        
        private fun fromCsvLine(line: String): AlipayBillItem {
            val fields = line.split(Regex("\t*,"))
            check(fields.size == 25) { "解析账单行出错：$line" }
            return AlipayBillItem(
                billDate = parseLocalDateTime(fields[4]).toLocalDate(),
                tradeNo = fields[0],
                outTradeNo = fields[1],
                tradeType = fields[2],
                productName = fields[3],
                createTime = parseLocalDateTime(fields[4]),
                completeTime = parseLocalDateTime(fields[5]),
                storeId = fields[6],
                storeName = fields[7],
                operatorId = fields[8],
                terminalId = fields[9],
                buyer = fields[10],
                totalAmount = parseBigDecimal(fields[11]),
                receiptAmount = parseBigDecimal(fields[12]),
                alipayRedpackAmount = parseBigDecimal(fields[13]),
                pointAmount = parseBigDecimal(fields[14]),
                alipayDiscountAmount = parseBigDecimal(fields[15]),
                merchantDiscountAmount = parseBigDecimal(fields[16]),
                couponAmount = parseBigDecimal(fields[17]),
                couponName = fields[18],
                merchantRedpackAmount = parseBigDecimal(fields[19]),
                cardAmount = parseBigDecimal(fields[20]),
                refundNo = fields[21],
                fee = parseBigDecimal(fields[22]),
                profitAmount = parseBigDecimal(fields[23]),
                remark = fields[24]
            )
        }

        fun parseBillItems(file: File): List<AlipayBillItem> {
            val result: MutableList<AlipayBillItem> = mutableListOf()

            val gbk = Charset.forName("GBK")
            val zipFile = ZipFile(file, gbk)
            ZipUtil.read(zipFile, Consumer<ZipEntry> {
                if (!it.name.endsWith("_业务明细.csv")) {
                    return@Consumer
                }

                var lineNumber = 0
                IoUtil.readLines(ZipUtil.getStream(zipFile, it), gbk, SerConsumer { line ->
                    lineNumber++
                    if (line.startsWith("#") || lineNumber <= 5) {
                        return@SerConsumer
                    }

                    result.add(fromCsvLine(line))
                })
            })

            return result
        }
    }

    fun isPay(): Boolean = tradeType == "交易"
    fun isRefund(): Boolean = tradeType == "退款"
    fun isMenzhen(): Boolean = outTradeNo.startsWith("MZ")
    fun isZhyuan(): Boolean = outTradeNo.startsWith("ZY")
    fun isZhenjian(): Boolean = outTradeNo.startsWith("ZJ")
    fun isYibao(): Boolean = outTradeNo.startsWith("YB")
}
