package org.mospital.alipay

import org.mospital.jackson.ConfigLoader

object AlipaySetting {

    private val config: AlipayConfig by lazy {
        ConfigLoader.loadConfig("alipay.yaml", AlipayConfig::class.java)
    }

    val maAppId: String get() = config.ma.appId
    val maMerchantName: String get() = config.ma.merchantName
    val maMerchantId: String get() = config.ma.merchantId
    val maPid: String get() = config.ma.pid
    val maEncryptType: String get() = config.ma.encryptType
    val maEncryptKey: String get() = config.ma.encryptKey
    val maNotifyUrl: String get() = config.ma.notifyUrl
    val maMerchantPrivateKey: String get() = config.ma.merchantPrivateKey
    val maMerchantCertPath: String get() = config.ma.merchantCertPath
    val maAlipayCertPath: String get() = config.ma.alipayCertPath
    val maAlipayRootCertPath: String get() = config.ma.alipayRootCertPath
    val maCharset: String get() = config.ma.charset
    val maSignType: String get() = config.ma.signType
    val maFormat: String get() = config.ma.format
    val maServerUrl: String get() = config.ma.serverUrl
    val maProtocol: String get() = config.ma.protocol
    val maTenantAppId: String get() = "" // 这个字段在新配置中不存在，保持为空
    val maHospitalName: String get() = "" // 这个字段在新配置中不存在，保持为空
    val maHospitalRegisterId: String get() = "" // 这个字段在新配置中不存在，保持为空
    val maBillDownloadPath: String get() = "" // 这个字段在新配置中不存在，保持为空

    val mipOrgAppId: String get() = config.mip.orgAppId
    val mipOrgChnlCrtfCode: String get() = config.mip.orgChnlCrtfCode
    val mipOrgCode: String get() = config.mip.orgCode
    val mipOrgName: String get() = config.mip.orgName
    val mipOrgCityCode: String get() = config.mip.orgCityCode
    val mipCallUrl: String get() = config.mip.callUrl
    val mipNotifyUrl: String get() = config.mip.notifyUrl

}
