package org.mospital.bsoft.mip

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mospital.jackson.JacksonKit
import kotlin.test.assertTrue

class BSoftMipServiceTest {

    @Test
    fun testGetPendingSettlements() {
        val pendingSettlementResult: PendingSettlementResult = runBlocking {
            BSoftMipService.WEIXIN.getPendingSettlements(
                PendingSettlementForm(
                    patientId = "2015182"
                )
            )
        }
        assertTrue(pendingSettlementResult.isOk(), pendingSettlementResult.message)
    }

    @Test
    fun testQueryFees() {
        val queryFeesResult: QueryFeesResult = runBlocking {
            BSoftMipService.WEIXIN.queryFees(
                QueryFeesForm(
                    patientId = "2015182"
                )
            )
        }
        assertTrue(queryFeesResult.isOk(), queryFeesResult.message)
        println(JacksonKit.writeValueAsString(queryFeesResult))
    }

    @Test
    fun testMergeFees() {
        val mergeFeesResult: MergeFeesResult = runBlocking {
            BSoftMipService.WEIXIN.mergeFees(
                MergeFeesForm(
                    feeIds = "YJ4937175"
                )
            )
        }
        assertTrue(mergeFeesResult.isOk(), mergeFeesResult.message)
    }

}
