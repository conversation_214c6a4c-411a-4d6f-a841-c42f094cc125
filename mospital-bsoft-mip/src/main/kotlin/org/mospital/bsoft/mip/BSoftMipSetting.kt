package org.mospital.bsoft.mip

import org.mospital.jackson.ConfigLoader

// 主 object，负责加载整个 YAML
object BsoftMipRootConfig {
    internal val config: BsoftMipConfig by lazy {
        ConfigLoader.loadConfig("bsoft-mip.yaml", BsoftMipConfig::class.java, BsoftMipRootConfig::class.java)
    }
}

// BSoftMipSetting 类，用于访问特定支付方式的配置
class BSoftMipSetting internal constructor(
    private val config: MipConfig
) {

    companion object {
        val WEIXIN = BSoftMipSetting(BsoftMipRootConfig.config.weixin)
        val ALIPAY = BSoftMipSetting(BsoftMipRootConfig.config.alipay)
    }

    // 使用类型判断和转换来访问属性
    val url: String
        get() = config.mipUrl

    val orgCode: String
        get() = config.orgCode

    val orgId: String
        get() = config.orgId

}
