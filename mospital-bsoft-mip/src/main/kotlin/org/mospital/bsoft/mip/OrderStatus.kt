package org.mospital.bsoft.mip

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonFormat

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
enum class OrderStatus(val code: Int, val desc: String) {

    YI_BAO_CUN(0, "已保存"),
    YU_JIE_SUAN_WAN_CHENG(1, "预结算完成"),
    JIE_SUAN_ZHONG(2, "结算中"),
    ZI_FEI_WAN_CHENG(3, "自费完成"),
    YI_BAO_ZHI_FU_WAN_CHENG(4, "医保支付完成"),
    YUAN_NEI_JIE_SUAN_WAN_CHENG(5, "院内结算完成"),
    JIE_SUAN_WAN_CHENG(6, "结算完成"),
    YI_TUI_KUAN(7, "已退款"),
    YI_YI_BAO_QUAN_BU_TUI_KUAN(8, "已医保全部退款"),
    JIN_ZI_FEI_QUAN_BU_TUI_KUAN(9, "仅自费全部退款"),
    JIN_ZI_FEI_BU_FEN_TUI_KUAN(10, "仅自费部分退款"),
    YI_BAO_QUAN_BU_TUI_KUAN_ZI_FEI_BU_FEN_TUI_KUAN(11, "医保全部退自费部分退款"),
    YI_CHE_XIAO(12, "已撤销"),
    YI_BAO_YI_CHE_XIAO(13, "医保已撤销"),
    YI_CHANG(14, "异常"),
    JIE_SUAN_SHI_BAI(15, "结算失败"),
    YI_BAO_JIE_SUAN_SHI_BAI_ZI_FEI_CHONG_ZHENG_SHI_BAI(16, "医保结算失败自费冲正失败");

    companion object {
        private val mapByCode = values().associateBy { it.code }

        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun fromCode(code: Int): OrderStatus? = mapByCode[code]
    }
}