package org.mospital.bsoft.mip

import okhttp3.OkHttpClient
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST

interface BSoftMipService {

    companion object {
        private fun getInstance(bsoftMipSetting: BSoftMipSetting): BSoftMipService {
            val logger: Logger = LoggerFactory.getLogger(BSoftMipService::class.java)

            // 使用带请求ID的HTTP客户端
            val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = 5_000,
                readTimeout = 30_000,
                useUuidRequestId = true
            )

            val retrofit: Retrofit = Retrofit.Builder()
                .baseUrl(bsoftMipSetting.url)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(JacksonKit.buildMapper()))
                .build()
            return retrofit.create(BSoftMipService::class.java)
        }

        val WEIXIN: BSoftMipService = getInstance(BSoftMipSetting.WEIXIN)
        val ALIPAY: BSoftMipService = getInstance(BSoftMipSetting.ALIPAY)
    }

    @POST("/api/ydybzf/selectPayZcf")
    suspend fun queryFees(@Body form: QueryFeesForm): QueryFeesResult

    @POST("/api/ydybzf/copyYj01zcf")
    suspend fun mergeFees(@Body form: MergeFeesForm): MergeFeesResult

    /**
     * 查询待结算费用清单
     */
    @POST("/api/ydybzf/getUFeeDataById")
    suspend fun getPendingSettlements(@Body form: PendingSettlementForm): PendingSettlementResult

    /**
     * 创建订单
     */
    @POST("/api/ydybzf/upOrderData")
    suspend fun createOrder(@Body form: CreateOrderForm): CreateOrderResult

    /**
     * 解锁订单，取消支付
     */
    @POST("/api/ydybzf/unlockCfAndYJ")
    suspend fun unlockOrder(@Body form: UnlockOrderForm): UnlockOrderResult
}
