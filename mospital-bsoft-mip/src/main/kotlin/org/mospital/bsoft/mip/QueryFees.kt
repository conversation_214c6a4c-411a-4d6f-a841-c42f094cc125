package org.mospital.bsoft.mip

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty

data class QueryFeesForm(
    @JsonProperty("brid")
    val patientId: String
)

data class QueryFeesResult(
    val code: Int,
    val message: String,
    @JsonAlias("data", "result")
    val data: List<Fee> = mutableListOf(),
) {
    fun isOk(): Boolean = code == 200

    data class Fee(
        val yjxh: String,
        val jgid: String,
        val fphm: String?,
        val mzxh: String?,
        val brid: String,
        val brxm: String,
        val kdrq: String?,
        val ksdm: String?,
        val fyksmc: String?,
        val zxksmc: String?,
        val kdysmc: String?,
        val ysdm: String?,
        val zxks: String?,
        val zxpb: String?,
        val hjgh: String?,
        val zfpb: String?,
        val jzxh: String?,
        val djzt: String?,
        val djly: String?,
        val mbbz: String?,
        val dbbz: String?,
        val ktbz: String?,
        val bftf: String?,
        val gsbz: String?,
        @JsonAlias("payState", "pay_state")
        val payState: String?,
        val iszcf: String?,
    )
}
