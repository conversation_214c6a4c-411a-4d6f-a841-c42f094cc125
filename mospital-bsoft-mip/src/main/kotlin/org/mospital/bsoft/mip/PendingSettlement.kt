package org.mospital.bsoft.mip

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal
import java.time.LocalDate

/**
 * 查询待结算费用清单
 */
data class PendingSettlementForm(
    @JsonProperty("brid")
    val patientId: String,

    @JsonProperty("brno")
    val patientNo: String = "",

    @JsonProperty("idNo")
    val idCardNo: String = "",

    val name: String = "",

    val startDate: LocalDate? = null,

    val endDate: LocalDate? = null,
)

data class PendingSettlementResult(
    val code: Int,
    val message: String,
    val data: List<JiuzhenRecord> = mutableListOf(),
) {
    fun isOk(): Boolean = code == 200

    fun toPendingSettlements(): List<PendingSettlement> {
        val pendingSettlements = mutableListOf<PendingSettlement>()
        data.forEach { jiuzhenRecord ->
            jiuzhenRecord.pendingSettlements.forEach { pendingSettlement ->
                pendingSettlement.jiuzhenXuhao = jiuzhenRecord.jiuzhenXuhao
                pendingSettlement.jiuzhenTime = jiuzhenRecord.jiuzhenTime
                pendingSettlements.add(pendingSettlement)
            }
        }
        return pendingSettlements
    }
}

data class JiuzhenRecord(
    @JsonAlias("jiuzhenXuhao", "jzxh")
    val jiuzhenXuhao: String,
    @JsonAlias("jiuzhenTime", "jzrq")
    val jiuzhenTime: String,
    @JsonAlias("departmentName", "jzksmc")
    val departmentName: String,
    @JsonAlias("doctorName", "jzysmc")
    val doctorName: String,
    @JsonAlias("pendingSettlements", "cfYjfeeDetails")
    val pendingSettlements: List<PendingSettlement> = mutableListOf(),
) {
    val nos: String
        get() = pendingSettlements.joinToString(",") { it.no }

    val feeIds: String
        get() = pendingSettlements.joinToString(",") { it.feeIds }

    val feeNames: String
        get() = pendingSettlements.joinToString(",") { it.feeNames }

    val amount: BigDecimal
        get() = pendingSettlements.sumOf { it.amount }

    val feeItems: List<PendingSettlement.Item>
        get() = pendingSettlements.flatMap { it.items }

    val unlockable: Boolean
        get() = pendingSettlements.all { it.unlockable }
}

/**
 * 待结算费用
 * @param no 处方（医技）号
 * @param name 费用名称
 * @param payState 支付状态，如果是1，需要显示取消支付的按钮
 */
data class PendingSettlement(

    @JsonAlias("no", "cfyjhm")
    val no: String = "",

    @JsonAlias("amount", "zjje")
    val amount: BigDecimal = BigDecimal.ZERO,

    @JsonAlias("departmentName", "kdksmc")
    val departmentName: String = "",

    @JsonAlias("doctorName", "kdysmc")
    var doctorName: String = "",

    @JsonAlias("manBingFlag", "mbbz")
    var manBingFlag: String = "",

    @JsonAlias("manBingCode", "mbbm")
    var manBingCode: String? = "",

    @JsonAlias("manBingName", "mbmc")
    var manBingName: String? = "",

    @JsonAlias("payState")
    var payState: String = "",

    @JsonAlias("zhenChaFeiFlag", "isZcf")
    var zhenChaFeiFlag: String = "",

    @JsonAlias("items", "feeDetails")
    val items: List<Item> = mutableListOf(),

    ) {

    var jiuzhenXuhao: String = ""

    var jiuzhenTime: String = ""

    val isManBing: Boolean
        get() = manBingFlag == "1"

    val unlockable: Boolean
        get() = payState == "1"

    val feeIds: String
        get() = items.joinToString(",") { it.no }

    val feeNames: String
        get() = items.joinToString(",") { it.name }

    data class Item(
        @JsonAlias("no", "fyxh")
        val no: String = "",

        @JsonAlias("name", "fymc")
        val name: String = "",

        @JsonAlias("price", "fydj")
        val price: String = "",

        @JsonAlias("quantity", "fysl")
        val quantity: String = "",

        @JsonAlias("amount", "fyje")
        val amount: BigDecimal = BigDecimal.ZERO,

        @JsonAlias("time", "fyrq")
        val time: String = "",

        @JsonAlias("departmentName", "fyks")
        val departmentName: String = "",
    )
}
