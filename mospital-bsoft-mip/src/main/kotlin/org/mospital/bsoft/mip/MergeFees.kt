package org.mospital.bsoft.mip

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty

data class MergeFeesForm(

    /**
     * 多个诊查费用id，以逗号分隔
     */
    @JsonProperty("yjxhs")
    val feeIds: String

)

data class MergeFeesResult(
    val code: Int,
    val message: String,
    @JsonAlias("data", "result")
    val data: List<Fee> = mutableListOf(),
) {
    fun isOk(): Boolean = code == 200 && data.isNotEmpty()

    data class Fee(
        val yjxh: String,
        val copyYjxh: String?,
        val jgid: String?,
        val brid: String?,
        val brxm: String?,
        val kdrq: String?,
        val ksdm: String?,
        val fyksmc: String?,
        val zxksmc: String?,
        val kdysmc: String?,
        val ysdm: String?,
        val zxks: String?,
        val zxpb: String?,
        val hjgh: String?,
        val zfpb: String?,
        val jzxh: String?,
        val djzt: String?,
        val djly: String?,
        val mbbz: String?,
        val dbbz: String?,
        val ktbz: String?,
        val bftf: String?,
        val gsbz: String?,
        @JsonAlias("payState", "pay_state")
        val payState: String?,
        val iszcf: String?,
    )
}