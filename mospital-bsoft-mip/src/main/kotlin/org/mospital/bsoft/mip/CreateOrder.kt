package org.mospital.bsoft.mip

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

/**
 * 创建订单
 * HIS接收到此请求后，会向省移动支付中心发起下列请求：
 * 【6201】上传费用明细、【6202】支付下单
 *
 * @param orgCode 定点医药机构编码
 * @param orgId 电子凭证机构号
 * @param idType 证件类别，01=身份证号
 * @param idNo 证件号码
 * @param userName 用户姓名
 * @param cfyjhm 处方（医技）号，多项以逗号分隔
 * @param feeIds 费用编码，多项以逗号分隔
 * @param payAuthNo 支付授权码
 * @param acctUsedFlag 个人账户使用标志：0=不使用，1=使用
 * @param payFlag 1=微信，2=支付宝
 * @param insuredId 人员参保地区划
 * @param btFlag 兵团医保标志
 * @param mbbm 慢病编码
 * @param mbbz 慢病标志
 * @param mbmc 慢病名称
 */
data class CreateOrderForm(
    @JsonProperty("orgCodg")
    val orgCode: String,
    val orgId: String,
    val idType: String = "01",
    val idNo: String,
    val userName: String,
    val cfyjhm: String,
    @JsonProperty("fyids")
    val feeIds: String,
    val payAuthNo: String,
    val acctUsedFlag: Int,
    val payFlag: Int,
    @JsonProperty("insuredID")
    val insuredId: String,
    val btFlag: String = "",
    val mbbm: String = "",
    val mbbz: String = "",
    val mbmc: String = ""
)

data class CreateOrderResult(
    val code: Int,
    val message: String,
    val data: Data?,
) {

    fun isOk(): Boolean = code == 200

    /**
     * @param payOrderId 支付订单号
     * @param orderStatus 订单状态
     * @param feeSumAmount 费用总额
     * @param ownPayAmount 现金支付金额
     * @param personalAccountPayAmount 个人账户支出
     * @param fundPayAmount 医保基金支出
     * @param deposit 住院押金
     * @param medOrgOrd 医疗机构订单号
     * @param extraData 扩展数据
     */
    data class Data(
        @JsonAlias("payOrderId", "payOrdId")
        val payOrderId: String,

        @JsonAlias("orderStatus", "ordStas")
        val orderStatus: OrderStatus,

        @JsonAlias("", "feeSumamt")
        val feeSumAmount: BigDecimal,

        @JsonAlias("ownPayAmount", "ownPayAmt")
        val ownPayAmount: BigDecimal,

        @JsonAlias("personalAccountPayAmount", "psnAcctPay")
        val personalAccountPayAmount: BigDecimal,

        @JsonAlias("fundPayAmount", "fundPay")
        val fundPayAmount: BigDecimal,

        @JsonAlias("deposit")
        val deposit: BigDecimal,

        @JsonAlias("medOrgOrd")
        val medOrgOrd: String,

        @JsonAlias("extraData", "extData")
        val extraData: MutableMap<String, Any>,
    )
}
