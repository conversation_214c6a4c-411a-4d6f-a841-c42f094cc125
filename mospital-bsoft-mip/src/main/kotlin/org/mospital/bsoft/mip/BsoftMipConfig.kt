package org.mospital.bsoft.mip

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * BsoftMipConfig 配置类
 * 对应 bsoft-mip.yaml 配置文件
 */
data class BsoftMipConfig(
    val weixin: MipConfig = MipConfig(),
    val alipay: MipConfig = MipConfig()
)

data class MipConfig(
    @JsonProperty("mipUrl")
    val mipUrl: String = "",
    @JsonProperty("orgCode")
    val orgCode: String = "",
    @JsonProperty("orgId")
    val orgId: String = ""
)
